import json
import logging
import os
from datetime import datetime, timezone
from urllib.parse import urljoin

import urllib3

logger = logging.getLogger()
logger.setLevel("INFO")


X_API_KEY = os.getenv("X_API_KEY", "").strip()
X_REQUEST_VALUE = os.getenv("X_REQUEST_VALUE", "").strip()

SERVICE_BASE_URL = os.getenv("SERVICE_BASE_URL", "").strip()
END_POINT_REQUEST = os.getenv("END_POINT_REQUEST", "/v1_0/s3/update-restored-status")
REQUEST_TIME_OUT = int(os.getenv("REQUEST_TIME_OUT", 30))


def lambda_handler(event, context):
    url = urljoin(SERVICE_BASE_URL, END_POINT_REQUEST).rstrip("/")

    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": X_API_KEY,
        "X-Request-Value": X_REQUEST_VALUE,
    }

    now = datetime.now(timezone.utc)
    logger.info(f"Lambda Request Handle S3 Restore Status: NOW(UTC) = {str(now)}")

    try:
        records = event.get("Records", [])
        if not records:
            logger.warning("No Records found in the event payload.")
            return

        payload_records = []
        for record in records:
            event_source = record.get("eventSource", "")
            event_name = record.get("eventName", "")
            if event_source != "aws:s3" or "ObjectRestore:Completed" not in event_name:
                logger.warning(
                    f"Skip record - unsupported event: source={event_source}, name={event_name}"
                )
                continue

            try:
                object_key = record["s3"]["object"]["key"]
                expires_at = record["glacierEventData"]["restoreEventData"][
                    "lifecycleRestorationExpiryTime"
                ]
            except KeyError as e:
                logger.warning(
                    f"Skip record - missing field {e}. record={json.dumps(record)}"
                )
                continue

            record = {
                "object_key": object_key,
                "expires_at": expires_at,
            }
            payload_records.append(record)
            logger.info(
                f"Received event for S3 object: {object_key} with expiry: {expires_at}"
            )

        if not payload_records:
            logger.warning("No valid ObjectRestore:Completed records to process.")
            return
        payload = {"records": payload_records}
        logger.info(f"Prepared payload with {len(payload_records)} record(s).")

        http = urllib3.PoolManager()
        response = http.request(
            "POST",
            url,
            body=json.dumps(payload),
            headers=headers,
            timeout=REQUEST_TIME_OUT,
        )
        logger.info(
            f"✅ Lambda Request Handle S3 Restore Status: {response.data.decode('utf-8')}"
        )

    except urllib3.exceptions.TimeoutError as e:
        error_msg = (
            f"❌ Lambda Request Handle S3 Restore Status request timed out: {str(e)}"
        )
        logger.error(error_msg)

    except urllib3.exceptions.RequestError as e:
        error_msg = f"❌ Lambda Request Handle S3 Restore Status An error occurred Error: {str(e)}"
        logger.error(error_msg)

    except Exception as e:
        error_msg = f"❌ Lambda Request Handle S3 Restore Status Error: {str(e)}"
        logger.error(error_msg)
