import json
import logging
import os
from datetime import datetime
from urllib.parse import urljoin

import urllib3

logger = logging.getLogger()
logger.setLevel("INFO")


X_API_KEY = os.getenv("X_API_KEY", "").strip()
X_REQUEST_VALUE = os.getenv("X_REQUEST_VALUE", "").strip()

SERVICE_BASE_URL = os.getenv("SERVICE_BASE_URL", "").strip()
END_POINT_REQUEST = os.getenv("END_POINT_REQUEST", "/v1_0/thumbnails")

THUMBNAIL_WIDTH = os.getenv("THUMBNAIL_WIDTH", 256)
THUMBNAIL_HEIGHT = os.getenv("THUMBNAIL_HEIGHT", 360)
QUALITY = os.getenv("QUALITY", 75)


REQUEST_TIME_OUT = int(os.getenv("REQUEST_TIME_OUT", 30))


def lambda_handler(event, context):
    url = urljoin(SERVICE_BASE_URL, END_POINT_REQUEST).rstrip("/")

    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": X_API_KEY,
        "X-Request-Value": X_REQUEST_VALUE,
    }

    now = datetime.now()
    logger.info(f"Lambda Request Create thumbnail: NOW(UTC) = {str(now)}")

    try:
        original_image_path = event["Records"][0]["s3"]["object"]["key"]
        payload = {
            "original_image_path": original_image_path,
            "thumbnail_width": int(THUMBNAIL_WIDTH),
            "thumbnail_height": int(THUMBNAIL_HEIGHT),
            "quality": int(QUALITY),
        }
        logger.info(f"Received event for S3 object: {original_image_path}")

        http = urllib3.PoolManager()
        response = http.request(
            "POST",
            url,
            body=json.dumps(payload),
            headers=headers,
            timeout=REQUEST_TIME_OUT,
        )
        logger.info(
            f"✅ Lambda Request Create thumbnail Success: {response.data.decode('utf-8')}"
        )

    except urllib3.exceptions.TimeoutError as e:
        error_msg = (
            f"❌ Lambda Request Create thumbnail The request timed out: {str(e)}"
        )
        logger.error(error_msg)
    except urllib3.exceptions.RequestError as e:
        error_msg = (
            f"❌ Lambda Request Create thumbnail An error occurred Error: {str(e)}"
        )
        logger.error(error_msg)
    except Exception as e:
        error_msg = f"❌ Lambda Request Create thumbnail Error: {str(e)}"
        logger.error(error_msg)
