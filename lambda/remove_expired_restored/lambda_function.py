import json
import logging
import os
from datetime import datetime, timezone
from urllib.parse import urljoin

import urllib3

logger = logging.getLogger()
logger.setLevel("INFO")

X_API_KEY = os.getenv("X_API_KEY", "").strip()
X_REQUEST_VALUE = os.getenv("X_REQUEST_VALUE", "").strip()

SERVICE_BASE_URL = os.getenv("SERVICE_BASE_URL", "").strip()
END_POINT_REQUEST = os.getenv(
    "END_POINT_REQUEST", "/v1_0/s3/remove-expired-restored-data"
)
REQUEST_TIME_OUT = int(os.getenv("REQUEST_TIME_OUT", 30))


def lambda_handler(event, context):
    url = urljoin(SERVICE_BASE_URL, END_POINT_REQUEST).rstrip("/")

    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": X_API_KEY,
        "X-Request-Value": X_REQUEST_VALUE,
    }

    now = datetime.now(timezone.utc)
    logger.info(f"[ObjectRestore:Delete] Handler start at UTC={now.isoformat()}")

    try:
        records = event.get("Records", [])
        if not records:
            logger.warning("No Records found in the event payload.")
            return

        payload_records = []
        for record in records:
            event_source = record.get("eventSource", "")
            event_name = record.get("eventName", "")

            if event_source != "aws:s3" or "ObjectRestore:Delete" not in event_name:
                logger.warning(
                    f"Skip record - unsupported event: source={event_source}, name={event_name}"
                )
                continue

            try:
                object_key = record["s3"]["object"]["key"]
                expires_at = record["glacierEventData"]["restoreEventData"][
                    "lifecycleRestorationExpiryTime"
                ]
            except KeyError as e:
                logger.warning(
                    f"Skip record - missing field {e}. record={json.dumps(record)}"
                )
                continue

            payload_records.append(
                {
                    "object_key": object_key,
                    "expires_at": expires_at,
                }
            )
            logger.info(
                f"Collected delete event: key={object_key}, expires_at={expires_at}"
            )

        if not payload_records:
            logger.warning("No valid ObjectRestore:Delete records to process.")
            return
        payload = {"records": payload_records}
        logger.info(f"Prepared payload with {len(payload_records)} record(s).")

        http = urllib3.PoolManager()
        response = http.request(
            "POST",
            url,
            body=json.dumps(payload),
            headers=headers,
            timeout=REQUEST_TIME_OUT,
        )
        logger.info(
            f"✅ Delete expired restored data response: {response.data.decode('utf-8')}"
        )

    except urllib3.exceptions.TimeoutError as e:
        logger.error(f"❌ Request timed out: {str(e)}")

    except urllib3.exceptions.RequestError as e:
        logger.error(f"❌ Request error: {str(e)}")

    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
