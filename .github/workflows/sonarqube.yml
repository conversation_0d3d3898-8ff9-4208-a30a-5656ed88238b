name: <PERSON><PERSON>q<PERSON>

on:
  push:
    branches:
      - develop
jobs:
  build:
    name: Build and analyze
    runs-on: ubuntu-latest
    environment: develop
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - uses: SonarSource/sonarqube-scan-action@v4
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}