from functools import wraps

from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import gigabyte_to_bytes
from gc_dentist_shared.core.constants import X_TENANT_UUID, StorageRedis
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.messages import CustomMessageCode


# Decorator check limit storage management base on tenant_uuid
def check_tenant_storage_limit(configuration):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            request = None
            for arg in args:
                if hasattr(arg, "headers"):
                    request = arg
                    break

            if not request:
                request = kwargs.get("request")

            if not request:
                raise CustomValueError(
                    message=CustomMessageCode.REQUEST_OBJECT_NOT_FOUND.title,
                    message_code=CustomMessageCode.REQUEST_OBJECT_NOT_FOUND.code,
                )

            tenant_uuid = request.headers.get(X_TENANT_UUID)
            if not tenant_uuid:
                raise CustomValueError(
                    message=CustomMessageCode.TENANT_UUID_HEADER_REQUIRED.title,
                    message_code=CustomMessageCode.TENANT_UUID_HEADER_REQUIRED.code,
                )

            try:
                redis_cli = await RedisCli.get_instance(configuration)
                prefix = StorageRedis.STORAGE_CURRENT_USAGE.value % tenant_uuid
                current_usage_bytes_from_redis = await redis_cli.get(prefix)

                if current_usage_bytes_from_redis:
                    current_usage_bytes = int(current_usage_bytes_from_redis)
                else:
                    s3_client = await S3Client.get_instance(configuration)
                    tenant_folder = f"{configuration.S3_FOLDER_NAME}/{tenant_uuid}"
                    current_usage_bytes_from_s3 = await s3_client.get_size_folder(
                        prefix=tenant_folder
                    )
                    await redis_cli.set(prefix, current_usage_bytes_from_s3)
                    current_usage_bytes = current_usage_bytes_from_s3

                # Get Limit storage management base on tenant_uuid
                # TODO: Get limit storage management base on tenant_uuid from pricing tables
                tenant_max_storage_gb = 50
                tenant_max_storage_bytes = gigabyte_to_bytes(tenant_max_storage_gb)

                if current_usage_bytes >= tenant_max_storage_bytes:
                    raise CustomValueError(
                        message=CustomMessageCode.TENANT_STORAGE_LIMIT_EXCEEDED.title,
                        message_code=CustomMessageCode.TENANT_STORAGE_LIMIT_EXCEEDED.code,
                    )

            except Exception as e:
                raise CustomValueError(
                    message=f"{CustomMessageCode.STORAGE_MANAGEMENT_ERROR.title}: {e}",
                    message_code=CustomMessageCode.STORAGE_MANAGEMENT_ERROR.code,
                )

            return await func(*args, **kwargs)

        return wrapper

    return decorator
