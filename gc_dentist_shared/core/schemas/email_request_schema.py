from typing import Any, Optional, Union

from pydantic import BaseModel, ConfigDict, EmailStr, Field, model_validator

from gc_dentist_shared.core.enums.mail_enum import MessageType, MultipartSubtypeEnum


# A lightweight version of fastapi_mail.MessageSchema to use in non-FastAPI apps
class MessageSchema(BaseModel):
    recipients: list[EmailStr] = Field(..., min_length=1)
    attachments: list[Union[dict[str, Any], str]] = Field(default_factory=list)
    subject: str = ""
    body: Optional[Union[str, list[Any]]] = None
    alternative_body: Optional[str] = None
    template_body: Optional[Union[list[Any], dict[str, Any], str]] = None
    cc: list[EmailStr] = Field(default_factory=list)
    bcc: list[EmailStr] = Field(default_factory=list)
    reply_to: list[EmailStr] = Field(default_factory=list)
    from_email: Optional[EmailStr] = None
    from_name: Optional[str] = None
    charset: str = "utf-8"
    subtype: MessageType = MessageType.PLAIN
    multipart_subtype: MultipartSubtypeEnum = MultipartSubtypeEnum.mixed
    headers: Optional[dict[str, Any]] = None

    @model_validator(mode="after")
    def _ensure_recipients(self):
        if not self.recipients or len(self.recipients) == 0:
            raise ValueError("At least one recipient is required in 'recipients'")
        return self


class MailRequest(MessageSchema):
    model_config = ConfigDict(extra="ignore")

    reply_to: Optional[EmailStr] = Field(
        default=None, description="Optional reply-to email address"
    )
    subject: str = Field(..., description="Email subject")
    template_name: Optional[str] = Field(
        default=None, description="Template name for rendering content (if used)"
    )
    template_variables: Optional[dict[str, Any]] = Field(
        default=None, description="Variables passed to the template when rendering"
    )
    category: int = Field(..., description="Template/mail category identifier")
    data_bidding: dict[str, Any] = Field(
        ..., description="Data used to bind into the template/content"
    )


class MailTemplateSchema(BaseModel):
    model_config = ConfigDict(extra="ignore")

    category: int
    data_bidding: dict[str, Any]
