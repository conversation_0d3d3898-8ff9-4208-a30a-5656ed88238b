import os
import re

import sqlparse
from sqlalchemy import Engine, event
from sqlalchemy.dialects import postgresql
from sqlparse import tokens as sqlparse_tokens
from sqlparse.sql import Where

# Pattern SQL dangerous
DANGEROUS_PATTERNS = [
    r"^\sDELETE\s+FROM\s+\w+(\s;)?\s*$",  # DELETE ALL DATA
    r"^\sTRUNCATE\s+TABLE\s+\w+(\s;)?\s*$",  # TRUNCATE
    r"^\sDROP\s+TABLE\s+\w+(\s;)?\s*$",  # DROP TABLE
    r"^\sDROP\s+DATABASE\s+\w+(\s;)?\s*$",  # DROP DATABASE
]

compiled_patterns = [re.compile(p, re.IGNORECASE) for p in DANGEROUS_PATTERNS]


def is_update_without_where(sql: str) -> bool:
    """Parse UPDATE statement using sqlparse for check WHERE."""
    parsed = sqlparse.parse(sql)
    if not parsed:
        return False

    stmt = parsed[0]
    tokens = [t for t in stmt.tokens if not t.is_whitespace]

    has_update = any(
        t.ttype is sqlparse_tokens.Keyword.DML and t.value.upper() == "UPDATE"
        for t in tokens
    )
    if not has_update:
        return False

    has_where = any(isinstance(t, Where) for t in stmt.tokens)
    return not has_where


@event.listens_for(Engine, "before_execute")
def prevent_dangerous_sql(conn, clauseelement, multiparams, params, execution_options):
    is_migration = os.getenv("ALEMBIC_CONTEXT") == "1"
    if is_migration:
        # Skip check when run alembic migration
        return clauseelement, multiparams, params

    try:
        dialect = postgresql.dialect()
        sql_text = str(
            clauseelement.compile(
                dialect=dialect, compile_kwargs={"literal_binds": True}
            )
        )
    except Exception:
        sql_text = str(clauseelement)

    for pattern in compiled_patterns:
        if pattern.match(sql_text):
            raise RuntimeError(f"⚠️  Dangerous SQL detected: {sql_text}")

    if is_update_without_where(sql_text):
        raise RuntimeError(f"⚠️ UPDATE without WHERE detected: {sql_text}")

    return clauseelement, multiparams, params
