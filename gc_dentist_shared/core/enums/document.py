from enum import IntEnum, StrEnum


class DocumentDataType(IntEnum):
    ORIGINAL = 1
    EDITED = 3


class DocumentStatus(IntEnum):
    DRAFT = 1
    ACTIVATED = 3
    INACTIVATED = 5


class DocumentDisplayMode(IntEnum):
    LIST_IMAGE = 1
    SINGLE_IMAGE = 3
    INTRAORAL_5_IMAGE = 7
    DENTAL_X_RAY_10_IMAGE = 9
    TWO_VERTICAL_IMAGE = 11
    TWO_HORIZONTAL_IMAGE = 13
    OTHER = 15


class DocumentExtension(IntEnum):
    PAGE = 1
    PDF = 3
    IMAGE = 5
    JSON = 7
    XLSX = 9
    CSV = 10


class DocumentS3Status(IntEnum):
    AVAILABLE = 2
    ARCHIVE = 4


class DocumentGroupKeyName(StrEnum):
    TREATMENT_RECORD = "treatment_record"
    MEDICAL_QUESTIONNAIRE = "medical_questionnaire"
    PERIODONTAL_EXAM = "periodontal_exam"
    PATIENT_PROVIDED_MATERIALS = "patient_provided_materials"
    IMG_LAYOUT = "img_layout"
    OTHER = "other"
    MEDICAL_DEVICE = "medical_device"
    BITE_FORCE_ANALYZER = "bite_force_analyzer"
    BITE_EYE = "bite_eye"
    WEARABLE_ELECTROMYOGRAPH = "wearable_electromyograph"
    MOTION_TRAINER = "motion_trainer"
