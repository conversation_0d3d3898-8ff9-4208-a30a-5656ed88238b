from enum import IntEnum, StrEnum


class RoleKeyEnum(IntEnum):
    """Role keys for various operations."""

    CLINIC_ADMIN = 1
    CLINIC_STAFF = 3


class RoleNameEnum(StrEnum):
    """Role keys for various operations."""

    CLINIC_ADMIN = "Admin"
    CLINIC_STAFF = "Staff"


ROLE_KEY_MAPPING = {
    RoleNameEnum.CLINIC_ADMIN.value: RoleKeyEnum.CLINIC_ADMIN.value,
    RoleNameEnum.CLINIC_STAFF.value: RoleKeyEnum.CLINIC_STAFF.value,
}
