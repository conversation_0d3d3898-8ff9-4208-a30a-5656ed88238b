from enum import IntEnum, StrEnum


class TenantPlanStatus(IntEnum):
    ACTIVATE = 1
    DEACTIVATE = 3
    SUSPENDED = 5


class PlanName(StrEnum):
    BASIC = "Basic"
    PREMIUM = "Premium"
    ENTERPRISE = "Enterprise"


class PlanKeyEnum(IntEnum):
    BASIC = 2
    PREMIUM = 4
    ENTERPRISE = 8


PLAN_KEY_MAPPING = {
    PlanKeyEnum.BASIC.value: PlanName.BASIC.value,
    PlanKeyEnum.PREMIUM.value: PlanName.PREMIUM.value,
    PlanKeyEnum.ENTERPRISE.value: PlanName.ENTERPRISE.value,
}
