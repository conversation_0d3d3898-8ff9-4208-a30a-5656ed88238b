from enum import IntEnum, StrEnum


class S3Folder(StrEnum):
    SUB = "sub"
    MAIN = "main"
    MEDICAL_DEVICES_FILE_IMPORT = "medical-devices-file-import"
    DOCUMENTS = "documents"
    MEDICAL_DEVICES = "medical-devices"


class S3RoleEnum(StrEnum):
    ADMIN = "admin"
    DOCTOR = "doctor"
    PATIENT = "patient"


class S3RestoreTier(StrEnum):
    """
    Represents the retrieval tier options when restoring objects from Amazon S3 Glacier storage.

    These tiers determine the speed and cost of retrieving archived data:

    - EXPEDITED: FASTEST retrieval (1-5 minutes), HIGHEST cost
    - STANDARD: MODERATE retrieval speed (3-5 hours), MEDIUM cost
    - BULK: SLOWEST retrieval (5-12 hours), LOWEST cost
    """

    EXPEDITED = "Expedited"
    STANDARD = "Standard"
    BULK = "Bulk"


class S3RestoreStatus(IntEnum):
    RESTORING = 2
    RESTORED = 4
    EXPIRED = 6
