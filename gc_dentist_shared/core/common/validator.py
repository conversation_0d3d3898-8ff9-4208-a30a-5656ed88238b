def password_validator(password: str) -> str:
    """
    Validates the password against common security rules.

    :param password: The password to validate.
    :return: The validated password.
    :raises ValueError: If the password does not meet the security criteria.
    """
    password = password.strip()

    if len(password) < 8:
        raise ValueError("Password must be at least 8 characters long.")

    if not any(char.isupper() for char in password):
        raise ValueError("Password must contain at least one uppercase letter.")

    if not any(char.islower() for char in password):
        raise ValueError("Password must contain at least one lowercase letter.")

    if not any(char.isdigit() for char in password):
        raise ValueError("Password must contain at least one digit.")

    if not any(char in '!@#$%^&*(),.?":{}|<>' for char in password):
        raise ValueError("Password must contain at least one special character.")

    return password
