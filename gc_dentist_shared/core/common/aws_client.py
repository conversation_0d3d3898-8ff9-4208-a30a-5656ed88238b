import boto3


def get_aws_client(service_name, configuration):
    session = boto3.session.Session()
    if configuration.ENVIRONMENT == "dev" and configuration.AWS_ACCESS_KEY_ID:

        client = session.client(
            service_name=service_name,
            region_name=configuration.AWS_REGION_NAME,
            aws_access_key_id=configuration.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=configuration.AWS_SECRET_ACCESS_KEY,
        )
    else:
        client = session.client(
            service_name=service_name, region_name=configuration.AWS_REGION_NAME
        )
    return client
