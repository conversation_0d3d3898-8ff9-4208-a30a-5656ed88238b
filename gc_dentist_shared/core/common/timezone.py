from datetime import datetime, timezone
from typing import Any

from pydantic import GetCoreSchemaHandler
from pydantic_core import core_schema


class Timestamp:

    @classmethod
    def __get_pydantic_core_schema__(
        cls, _source_type: Any, _handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        return core_schema.no_info_after_validator_function(
            cls.validate, core_schema.datetime_schema()
        )

    @classmethod
    def validate(cls, value: datetime) -> datetime:
        if not isinstance(value, datetime):
            raise TypeError("Expected a datetime object")

        if value.tzinfo is None:
            raise ValueError("Datetime must be timezone-aware")

        # Convert to UTC if not already
        return value.astimezone(timezone.utc)

    def __repr__(self):
        return "TimestampUTC"
