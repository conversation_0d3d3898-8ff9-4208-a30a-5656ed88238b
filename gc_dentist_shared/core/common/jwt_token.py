from datetime import datetime, timedelta, timezone
from typing import Optional

from authlib.jose import Jose<PERSON><PERSON><PERSON>, jwt
from authlib.jose.errors import ExpiredTokenError, InvalidTokenError
from cryptography.hazmat.primitives import serialization
from werkzeug.security import gen_salt

from gc_dentist_shared.core.constants import ISSUER
from gc_dentist_shared.core.logger.config import log

# JWT_SECRET_KEY: str = "your_jwt_secret"
# JWT_ALGORITHM: str = "HS256"

# OAUTH_CODE_EXPIRE_MINUTES: int = 10
# TEMPORARY_TOKEN_EXPIRE_MINUTES: int = 5
# ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
# REFRESH_TOKEN_EXPIRE_DAYS: int = 30

# DEFAULT_CLIENT_NAME_FOR_WEB: str = "Noda-Web"
# DEFAULT_CLIENT_NAME_FOR_APP: str = "Noda-App"


def generate_access_token(
    data: dict,
    jwt_secret_key: str,
    jwt_algorithm: str,
    access_token_expire_minutes: int,
    expires_delta: Optional[timedelta] = None,
):
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (
        expires_delta or timedelta(minutes=access_token_expire_minutes)
    )
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, jwt_secret_key, algorithm=jwt_algorithm), expire


def generate_refresh_token(
    data: dict, jwt_secret_key: str, jwt_algorithm: str, refresh_token_expire_days: int
):
    expire = datetime.now(timezone.utc) + timedelta(days=refresh_token_expire_days)
    to_encode = data.copy()
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, jwt_secret_key, algorithm=jwt_algorithm), expire


def encode_jwt_token(
    key_id: str,
    jwt_secret_key: str,
    jwt_algorithm: str,
    access_token_expire_minutes: int,
    grant_type,
    client,
    user,
    scope: str,
    role_key_ids: Optional[list[int]] = None,
) -> dict:
    now = datetime.now(timezone.utc)
    token_expires_minute = timedelta(minutes=access_token_expire_minutes)

    header = {"kid": key_id, "alg": jwt_algorithm}

    payload = {
        "iss": ISSUER,
        "sub": str(user.id),
        "scope": scope,
        "iat": int(now.timestamp()),
        "exp": int((now + token_expires_minute).timestamp()),
        "tenant_uuid": None,
        "role_key_ids": role_key_ids,
    }

    tenant_uuid = getattr(user, "tenant_uuid", None)
    if tenant_uuid is not None:
        payload["tenant_uuid"] = str(tenant_uuid)

    # private_key_obj = jwk.loads(jwt_secret_key.encode())
    private_key_obj = serialization.load_pem_private_key(jwt_secret_key, password=None)
    access_token_str = jwt.encode(header, payload, private_key_obj).decode("utf-8")

    # 3. create refresh token
    refresh_token_str = None
    # if 'refresh_token' in client.get_grant_type():
    if client.check_grant_type("refresh_token"):
        refresh_token_str = gen_salt(48)

    token_data = {
        "access_token": access_token_str,
        "refresh_token": refresh_token_str,
        "token_type": "Bearer",
        "scope": scope,
        "expires_in": int((now + token_expires_minute).timestamp()),
    }
    return token_data


def decode_jwt_token(
    jwt_public_key: str, jwt_algorithm: str, token: str or bytes
) -> dict or None:
    try:
        claims_options = {
            "header": {
                "alg": {"essential": True, "values": [jwt_algorithm]},
            }
        }

        claims = jwt.decode(token, jwt_public_key, claims_options=claims_options)

        claims.validate()
        return claims

    except (ExpiredTokenError, InvalidTokenError) as e:
        log.error(f"❌ JWT token has expired: {e}")
        raise e

    except JoseError as e:
        log.error(f"❌ Error while decoding JWT: {e}")
        return None
