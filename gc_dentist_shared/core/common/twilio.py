#!/usr/bin/env python3
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client
from twilio.rest.verify.v2.service.verification_check import VerificationCheckInstance

from gc_dentist_shared.core.constants import TWILIO_SMS_CHANNEL, TWILIO_STATUS_APPROVED
from gc_dentist_shared.core.exception_handler.custom_exception import (
    TwilioRestExceptionError,
)
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.messages import CustomMessageCode


class TwilioService(Client):
    _client = None

    def __init__(self, configuration):
        self.service_sid = configuration.TWILIO_SERVICE_SID
        self.messaging_service_sid = configuration.TWILIO_MESSAGE_SERVICE_SID
        super().__init__(
            username=configuration.TWILIO_ACCOUNT_SID,
            password=configuration.TWILIO_AUTH_TOKEN,
        )

    @classmethod
    def get_client(cls, configuration):
        if cls._client is None:
            cls._client = TwilioService(configuration=configuration)
        return cls._client

    def get_twilio_verifications_service(self):
        twilio_verifications_service = self._client.verify.v2.services(
            self._client.service_sid
        )
        return twilio_verifications_service

    def send_verification_otp(self, to: str, channel: str = TWILIO_SMS_CHANNEL) -> None:
        try:
            verifications_service = self.get_twilio_verifications_service()
            verifications_service.verifications.create(to=to, channel=channel)
        except TwilioRestException as e:
            log.error(f"❌ Twilio Service: generate_verification_otp error : {str(e)}")
            raise TwilioRestExceptionError()

    def verify_sms_otp(self, to: str, code: str) -> VerificationCheckInstance | None:
        try:
            verifications_service = self.get_twilio_verifications_service()
            verification = verifications_service.verification_checks.create(
                to=to, code=code
            )
            if verification.status == TWILIO_STATUS_APPROVED:
                return verification
            return None
        except TwilioRestException as e:
            log.error(f"❌ Twilio Service: verify_sms_otp error : {str(e)}")
            raise TwilioRestExceptionError()

    def send_message(self, to: str, body: str):
        try:
            message = self._client.messages.create(
                to=to,
                body=body,
                messaging_service_sid=self._client.messaging_service_sid,
            )
            return message.sid
        except TwilioRestException as e:
            log.error(f"❌ Twilio Rest Exception: send_message error : {str(e)}")
            raise TwilioRestExceptionError(
                message=CustomMessageCode.TWILIO_SEND_MESSAGE_ERROR.title,
                message_code=CustomMessageCode.TWILIO_SEND_MESSAGE_ERROR.code,
            )
        except Exception as e:
            log.error(f"❌ Twilio Service Exception: send_message error : {str(e)}")
            raise TwilioRestExceptionError()
