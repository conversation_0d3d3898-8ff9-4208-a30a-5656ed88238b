from email._header_value_parser import get_mailbox
from email.errors import HeaderParse<PERSON>rror
from email.header import <PERSON><PERSON>
from email.headerregistry import Address
from email.utils import formataddr

import boto3
from jinja2 import Template

from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.messages import INVALID_EMAIL_ADDRESS_NEWLINES
from gc_dentist_shared.core.schemas.email_request_schema import MailRequest


class SyncMailClient:
    """Synchronous Mail Client for sending emails using AWS SES."""

    _instance = None
    _ses_client = None

    def __init__(self, configuration):
        self.aws_access_key = configuration.AWS_ACCESS_KEY_ID
        self.aws_secret_key = configuration.AWS_SECRET_ACCESS_KEY
        self.aws_region = configuration.SES_REGION_NAME
        self.configuration = configuration

    @classmethod
    def get_instance(cls, configuration=None):
        if cls._instance is None:
            if configuration is None:
                raise ValueError("Configuration is required for initial instantiation")
            cls._instance = SyncMailClient(configuration)
            cls._ses_client = boto3.client(
                "sesv2",
                aws_access_key_id=cls._instance.aws_access_key,
                aws_secret_access_key=cls._instance.aws_secret_key,
                region_name=cls._instance.aws_region,
            )
        return cls._instance

    def __sanitize_address(self, addr, encoding):
        """Sanitize and format an email address.

        Args:
            addr: Email address as string or tuple (name, address)
            encoding: Character encoding for the address

        Returns:
            str: Formatted email address

        Raises:
            ValueError: If the address format is invalid
        """
        address = None
        if not isinstance(addr, tuple):
            addr = str(addr)
            try:
                token, rest = get_mailbox(addr)
            except (HeaderParseError, ValueError, IndexError):
                raise ValueError('Invalid address format: "{}"'.format(addr))
            else:
                if rest:
                    raise ValueError(
                        'Invalid address; only {} could be parsed from "{}"'.format(
                            token, addr
                        )
                    )
                nm = token.display_name or ""
                localpart = token.local_part
                domain = token.domain or ""
        else:
            nm, address = addr
            localpart, domain = address.rsplit("@", 1)

        address_parts = nm + localpart + domain
        if "\n" in address_parts or "\r" in address_parts:
            raise ValueError(INVALID_EMAIL_ADDRESS_NEWLINES)

        try:
            nm.encode("ascii")
            nm = Header(nm).encode()
        except UnicodeEncodeError:
            nm = Header(nm, encoding).encode()
        try:
            localpart.encode("ascii")
        except UnicodeEncodeError:
            localpart = Header(localpart, encoding).encode()
        domain = domain.encode("idna").decode("ascii")
        parsed_address = Address(username=localpart, domain=domain)
        return formataddr((nm, parsed_address.addr_spec))

    def send_message(
        self, message: MailRequest, send_with_each_recipient: bool = False
    ):
        """Send an email message.

        Args:
            message: MailRequest containing email details
            send_with_each_recipient: Whether to send individually to each recipient

        Returns:
            bool: True if the email was sent successfully, False otherwise

        Raises:
            Exception: If there was an error sending the email
        """
        ses_client = self._ses_client
        if not message or not ses_client:
            log.warning("❗No message value or SES client instance.")
            return False

        if not message.recipients or len(message.recipients) == 0:
            log.warning("❗No recipient address.")
            return False

        from_email = self.__sanitize_address(
            self.configuration.SES_FROM_MAIL, message.charset
        )
        recipients = [
            self.__sanitize_address(addr, message.charset)
            for addr in message.recipients
        ]
        request_data = dict(
            FromEmailAddress=from_email,
            Destination={"CcAddresses": message.cc, "BccAddresses": message.bcc},
            Content={
                "Simple": {
                    "Subject": {"Charset": "UTF-8", "Data": message.subject},
                    "Body": {"Html": {"Charset": "UTF-8", "Data": message.body}},
                }
            },
        )

        if send_with_each_recipient:
            for recipient in recipients:
                destination: dict = {
                    "ToAddresses": [recipient],
                    "CcAddresses": message.cc,
                    "BccAddresses": message.bcc,
                }
                request_data.update({"Destination": destination})

                try:
                    ses_client.send_email(**request_data)
                except Exception as error:
                    log.error(f"❌ Error sending email to {recipient}: {error}")
                    raise error
        else:
            request_data.update(
                {
                    "Destination": {
                        "ToAddresses": recipients,
                        "CcAddresses": message.cc,
                        "BccAddresses": message.bcc,
                    }
                }
            )
            try:
                ses_client.send_email(**request_data)
            except Exception as error:
                log.error(f"❌ Error sending email to {recipients}: {error}")
                raise error
        return True

    # region Template rendering methods

    def render_body_content_template(self, template: str, data_bidding: dict) -> str:
        template = Template(template)
        return template.render(**data_bidding)

    # endregion Template rendering methods
