from sqlalchemy import Text, case, cast, func


class PgcryptoExpression:
    """
    Helper for building SQLAlchemy expressions to encrypt, decrypt, and search
    data using PostgreSQL's pgcrypto extension.

    Requirements:
        - Enable the pgcrypto extension in PostgreSQL:
            CREATE EXTENSION IF NOT EXISTS pgcrypto;
        - Data format: "<version>:<ciphertext_hex>"
        - Supports multiple key versions for key rotation.
    """

    def __init__(self, configuration):
        self.configuration = configuration

    # ---------------------- ENCRYPT ----------------------
    def encrypt_expression(self, plaintext_value: str):
        """
        Build an expression to encrypt a plaintext value.

        Args:
            plaintext_value (str): Value to encrypt.

        Returns:
            SQLAlchemy expression: "<version>:<ciphertext_hex>"

        Example:
            pgexp = PgcryptoExpression(configuration)
            stmt = insert(User).values(
                email=pgexp.encrypt_expression("<EMAIL>")
            )
            await session.execute(stmt)
        """
        current_version = self.configuration.AWS_SECRET_CURRENT_VERSION
        current_key = self.configuration.AWS_SECRET_ROTATION_KEY_MAPPING.get(
            current_version
        )

        return func.concat(
            current_version,
            ":",
            func.encode(
                func.pgp_sym_encrypt(
                    cast(plaintext_value, Text), cast(current_key, Text)
                ),
                "hex",
            ),
        )

    # ---------------------- DECRYPT ----------------------
    def decrypt_expression(self, encrypted_column):
        """
        Build an expression to decrypt a column encrypted with encrypt_expression().

        Args:
            encrypted_column: Column in format "<version>:<ciphertext_hex>"

        Returns:
            SQLAlchemy expression: plaintext value.
            Like:
                CASE version_expr
                    WHEN 'v1' THEN pgp_sym_decrypt(..., 'key1')
                    WHEN 'v2' THEN pgp_sym_decrypt(..., 'key2')
                END
        Example:
            pgexp = PgcryptoExpression(configuration)
            stmt = select(
                User.id,
                pgexp.decrypt_expression(User.email).label("email")
            )
            result = await session.execute(stmt)
        """
        separator_pos = func.strpos(encrypted_column, ":")
        version_expr = func.substring(encrypted_column, 1, separator_pos - 1)
        hex_data_expr = func.substring(encrypted_column, separator_pos + 1)
        bytea_data_expr = func.decode(hex_data_expr, "hex")

        whens = {
            version: func.pgp_sym_decrypt(bytea_data_expr, cast(key, Text))
            for version, key in self.configuration.AWS_SECRET_ROTATION_KEY_MAPPING.items()
        }
        return case(whens, value=version_expr)

    # ---------------------- SEARCH ----------------------
    def like_expression(
        self, encrypted_column, pattern: str, case_insensitive: bool = True
    ):
        """
        Build a LIKE/ILIKE search expression on an encrypted column.

        Args:
            encrypted_column: Encrypted column.
            pattern (str): Search substring.
            case_insensitive (bool): Use ILIKE if True.

        Example:
            pgexp = PgcryptoExpression(configuration)
            stmt = select(User).where(
                pgexp.like_expression(User.email, "gmail")
            )
            result = await session.execute(stmt)
        """
        decrypted_expr = self.decrypt_expression(encrypted_column)
        if case_insensitive:
            return func.lower(cast(decrypted_expr, Text)).ilike(f"%{pattern.lower()}%")
        else:
            return cast(decrypted_expr, Text).like(f"%{pattern}%")

    def equal_expression(
        self, encrypted_column, value: str, case_insensitive: bool = True
    ):
        """
        Build an exact match (=) search expression on an encrypted column.

        Args:
            encrypted_column: Encrypted column.
            value (str): Exact value to match.
            case_insensitive (bool): Compare lower-case if True.
        Example:
            pgexp = PgcryptoExpression(configuration)
            stmt = select(User).where(
                pgexp.equal_expression(User.email, "<EMAIL>")
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
        """
        decrypted_expr = self.decrypt_expression(encrypted_column)
        if case_insensitive:
            return func.lower(cast(decrypted_expr, Text)) == value.lower()
        else:
            return cast(decrypted_expr, Text) == value
