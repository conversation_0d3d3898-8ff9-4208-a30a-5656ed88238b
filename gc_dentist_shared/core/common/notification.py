import itertools

from fastapi import BackgroundTasks
from firebase_admin import messaging

from gc_dentist_shared.core.common.strings import (
    extract_short_notification_body_to_mobile,
)
from gc_dentist_shared.core.common.utils import split_array
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.schemas.notification_schema import NotificationSchema


class Notification:
    CHUNK_SIZE = 500

    def __init__(self, configuration):
        self.configuration = configuration

    def build_payload_and_push_notification(
        self,
        message: str,
        type: str,
        title: str,
        body: str,
        fcm_tokens,
        background_tasks: BackgroundTasks,
        mobile_body_flag: bool = False,
        **kwargs,
    ):
        if fcm_tokens:
            fcm_tokens = list(
                itertools.chain.from_iterable(
                    [item] if not isinstance(item, list) else item
                    for item in fcm_tokens
                )
            )

            if len(fcm_tokens) > 0:
                fcm_tokens_chunks = split_array(fcm_tokens, self.CHUNK_SIZE)

                if mobile_body_flag:
                    mobile_body = extract_short_notification_body_to_mobile(body)
                    body = (
                        f"{mobile_body[:40]}..."
                        if len(mobile_body) > 40
                        else mobile_body
                    )

                for chunk in fcm_tokens_chunks:
                    data = {
                        "message": message,
                        "type": type,
                        **kwargs.get("extra_data", {}),
                    }

                    background_tasks.add_task(
                        self.send_each_for_multicast,
                        obj=NotificationSchema(
                            title=title, data=data, body=body, tokens=chunk
                        ),
                        is_show=kwargs.get("is_show", True),
                    )

    def send_each_for_multicast(self, obj: NotificationSchema, is_show=True) -> int:
        try:
            notification = messaging.Notification(title=obj.title, body=obj.body)
            message = messaging.MulticastMessage(
                data=obj.data, tokens=obj.tokens, notification=notification
            )
            if not is_show:
                message = messaging.MulticastMessage(data=obj.data, tokens=obj.tokens)

            batch_response = messaging.send_each_for_multicast(
                message, dry_run=self.configuration.FIREBASE_DRY_RUN
            )

            log.info(f"🚀 Sent message to {batch_response.success_count} device(s)")
            errors_lst = []
            for v in batch_response.responses:
                if v.exception:
                    error = {}
                    cause_resp = v.exception.__dict__.get("_cause").__dict__
                    cause_dict = cause_resp.get("response").json()
                    # Preparing custom error response list
                    error["status"] = cause_dict.get("error").get("status", None)
                    error["code"] = cause_dict.get("error").get("code", None)
                    error["error_code"] = (
                        cause_dict.get("error").get("details")[0].get("errorCode", None)
                    )
                    error["cause"] = cause_dict.get("error").get("message", None)
                    errors_lst.append(error)
                    log.warning(f"❗ Error sending message: {error}")
            return batch_response.success_count
        except Exception as e:
            log.error(f"❌ Error sending notification: {e}")
            return 0
