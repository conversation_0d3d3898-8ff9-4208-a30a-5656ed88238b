import asyncio
import urllib
from contextlib import AsyncExitStack
from datetime import datetime

from aiobotocore.session import AioSession
from botocore.config import Config
from botocore.exceptions import (
    ClientError,
    EndpointConnectionError,
    NoCredentialsError,
    PartialCredentialsError,
)

from gc_dentist_shared.core.common.utils import convert_datetime_with_timezone
from gc_dentist_shared.core.exception_handler.custom_exception import (
    S3BucketExceptionError,
)
from gc_dentist_shared.core.logger.config import log


class S3Client:
    _instance = None
    _s3_client = None
    _lock = asyncio.Lock()

    def __init__(self, configuration):
        self.aws_access_key = configuration.AWS_ACCESS_KEY_ID
        self.aws_secret_key = configuration.AWS_SECRET_ACCESS_KEY
        self.aws_region = configuration.AWS_REGION_NAME
        self.s3_bucket = configuration.S3_BUCKET_NAME
        self.configuration = configuration

    @classmethod
    async def get_instance(cls, configuration):
        async with cls._lock:
            if cls._instance is None:
                cls._instance = S3Client(configuration=configuration)
                await cls._instance.__initialize()
        return cls._instance

    async def __initialize(self):
        _exit_stack = AsyncExitStack()
        self._s3_client = await _exit_stack.enter_async_context(
            AioSession().create_client(
                "s3",
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.aws_region,
                config=Config(signature_version="s3v4"),
            )
        )

    async def upload_file(
        self,
        file,
        object_name,
        content_disposition=None,
        content_type=None,
        download_filename=None,
        tagging: str | None = None,
    ):
        try:
            put_object_args = {
                "Body": file,
                "Bucket": self.s3_bucket,
                "Key": object_name,
            }

            if download_filename or content_disposition:
                disposition_type = content_disposition or "attachment"
                encoded_filename = urllib.parse.quote(download_filename.encode("utf-8"))
                put_object_args["ContentDisposition"] = (
                    f"{disposition_type}; filename=\"{download_filename}\"; filename*=UTF-8''{encoded_filename}"
                    if encoded_filename
                    else disposition_type
                )
            if content_type:
                put_object_args["ContentType"] = content_type

            if tagging:
                put_object_args["Tagging"] = tagging

            await self._s3_client.put_object(**put_object_args)
        except EndpointConnectionError as e:
            log.error(f"❌ Endpoint connection error: {str(e)}")
            raise S3BucketExceptionError()
        except ClientError as e:
            log.error(f"❌ Client error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error: {str(e)}")
            raise S3BucketExceptionError()
        finally:
            file = None

    async def check_key_exists(self, key: str):
        try:
            # Check if the key exists in the bucket
            await self._s3_client.head_object(Bucket=self.s3_bucket, Key=key)
            return True
        except self._s3_client.exceptions.ClientError as e:
            # If a '404 Not Found' error occurs, the key does not exist
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise S3BucketExceptionError()

    async def get_object(self, key: str):
        try:
            response = await self._s3_client.get_object(Bucket=self.s3_bucket, Key=key)
            content = await response.get("Body").read()
            return content
        except ClientError as e:
            log.error(f"❌ Client error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error: {str(e)}")
            raise S3BucketExceptionError()

    async def delete_s3_object(self, object_name):
        try:
            await self._s3_client.delete_object(Bucket=self.s3_bucket, Key=object_name)
        except NoCredentialsError as e:
            log.error(f"❌ No credentials error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error: {str(e)}")
            raise S3BucketExceptionError()

    async def delete_s3_objects(self, keys: list[str]):
        try:
            result = await self._s3_client.delete_objects(
                Bucket=self.s3_bucket,
                Delete={
                    "Objects": [{"Key": key} for key in keys],
                },
            )
            return result.get("Deleted")
        except NoCredentialsError as e:
            log.error(f"❌ No credentials error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error: {str(e)}")
            raise S3BucketExceptionError()

    async def generate_presigned_url(
        self, key: str, type_object: str = "get_object", expiration=3600, **kwargs
    ):
        """
        Generate a presigned URL to share an S3 object.
        :param key: S3 object name.
        :param type_object: Type of the S3 operation, e.g., 'get_object', 'put_object'.
        :param expiration: Time in seconds for the presigned URL to remain valid.
        :param kwargs: Additional parameters for the presigned URL.
        :return: Presigned URL as a string.
        Example usage:
        >>> s3_client = await S3Client.get_instance()
        >>> url = await s3_client.generate_presigned_url('my_object.txt', expiration=3600)
        >>> url = await s3_client.generate_presigned_url(
                'my_object.txt',
                expiration=3600,
                params={
                    'ResponseContentDisposition': 'inline',
                    'ResponseContentType': 'application/pdf'
                }
            )
        """
        try:
            response = await self._s3_client.generate_presigned_url(
                type_object,
                Params={
                    "Bucket": self.s3_bucket,
                    "Key": key,
                    **kwargs.get("params", {}),
                },
                ExpiresIn=expiration,
            )
            return response
        except (NoCredentialsError, PartialCredentialsError) as e:
            log.error(f"❌ Credentials error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error: {str(e)}")
            raise S3BucketExceptionError()

    def generate_key_s3(
        self, prefix: str, object_name: str, add_date_path: bool = True
    ) -> str:
        """
        Generate a key for S3 based on a prefix and file name.
        :param prefix: prefix format: "{clinic_uuid}/{<role>}/{<role>_id}/{prefix_name}"
            - role can be "doctor", "patient", etc.
        :param object_name: name object in S3 bucket, e.g., "image.jpg" or "document.pdf".
        :param add_date_path: If True, add a date folder (YYYYMMDD) before the object name.
        :return: A string representing the S3 key.
        """
        key_parts = [self.configuration.S3_FOLDER_NAME, prefix]
        if add_date_path:
            now = convert_datetime_with_timezone(datetime.now().astimezone()).strftime(
                "%Y%m%d"
            )
            key_parts.append(now)

        key_parts.append(object_name)

        return "/".join(key_parts)

    async def upload_batch_file(self, files_to_upload: list[dict]):
        """
        Uploads a batch of files to S3 concurrently.

        :param files_to_upload: A list of dictionaries, where each dict contains:
            - 'file' (required): The bytes-like object to upload.
            - 'object_name' (required): The destination key in S3.
            - 'content_type' (optional): The MIME type of the file.
            - 'content_disposition' (optional): E.g., 'inline', 'attachment'.
            - 'download_filename' (optional): The filename suggested to the user on download.
        """
        tasks = []
        for file_info in files_to_upload:
            task = asyncio.create_task(
                self.upload_file(
                    file=file_info["file"],
                    object_name=file_info["object_name"],
                    content_type=file_info.get("content_type"),
                    content_disposition=file_info.get("content_disposition"),
                    download_filename=file_info.get("download_filename"),
                    tagging=file_info.get("tagging"),
                )
            )
            tasks.append(task)

        try:
            await asyncio.gather(*tasks)
        except EndpointConnectionError as e:
            log.error(f"❌upload_batch_file endpoint connection error: {str(e)}")
            raise S3BucketExceptionError()
        except ClientError as e:
            log.error(f"❌upload_batch_file client error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌upload_batch_file unexpected error: {str(e)}")
            raise S3BucketExceptionError()
        finally:
            pass

    async def copy_object(
        self,
        old_key: str,
        new_key: str,
        delete_old_object: bool = False,
    ) -> dict:
        """
        Copy an object to a new key (same bucket) preserving metadata & tags.
        Optionally delete the source object after a successful copy.

        :param old_key: Source object key.
        :param new_key: Destination object key.
        :param delete_old_object: If True, delete old_key after successful copy.
        :return: {"copied": bool, "deleted": bool}
        """
        try:
            # Copy: keep metadata & tags
            await self._s3_client.copy_object(
                Bucket=self.s3_bucket,
                Key=new_key,
                CopySource={"Bucket": self.s3_bucket, "Key": old_key},
                MetadataDirective="COPY",
                TaggingDirective="COPY",
            )

            deleted = False
            if delete_old_object:
                await self._s3_client.delete_object(
                    Bucket=self.s3_bucket,
                    Key=old_key,
                )
                deleted = True

            log.info(
                f"✅ Copied '{old_key}' -> '{new_key}'"
                + (" and deleted source" if deleted else "")
            )
            return {"copied": True, "deleted": deleted}

        except ClientError as e:
            log.error(f"❌ copy_object failed ({old_key} -> {new_key}): {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error in copy_object: {str(e)}")
            raise S3BucketExceptionError()

    async def copy_objects_batch(self, items: list[dict]):
        """
        Copies/moves a batch of S3 objects concurrently.

        :param items: A list of dictionaries; each dict contains:
            - 'old_key' (required): Source object key.
            - 'new_key' (required): Destination object key.
            - 'delete_old_object' (optional, default False): Delete source after copy.
        """
        tasks = []
        for it in items:
            task = asyncio.create_task(
                self.copy_object(
                    old_key=it["old_key"],
                    new_key=it["new_key"],
                    delete_old_object=it.get("delete_old_object", False),
                )
            )
            tasks.append(task)
        try:
            await asyncio.gather(*tasks)
        except EndpointConnectionError as e:
            log.error(f"❌copy_objects_batch endpoint connection error: {str(e)}")
            raise S3BucketExceptionError()
        except ClientError as e:
            log.error(f"❌copy_objects_batch client error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌copy_objects_batch unexpected error: {str(e)}")
            raise S3BucketExceptionError()

    async def get_size_folder(self, prefix: str) -> int:
        try:
            log.info(f"START get_size_folder with prefix {prefix}")

            total_size = 0
            paginator = self._s3_client.get_paginator("list_objects_v2")
            pages = paginator.paginate(Bucket=self.s3_bucket, Prefix=prefix)

            async for page in pages:
                if "Contents" in page:
                    for s3_object in page["Contents"]:
                        total_size += s3_object["Size"]
                await asyncio.sleep(0.1)

            log.info(f"END get_size_folder with prefix {prefix}")
            return total_size

        except ClientError as e:
            log.error(f"❌ Client error: {str(e)}")
            raise S3BucketExceptionError()
        except Exception as e:
            log.error(f"❌ Unexpected error: {str(e)}")
            raise S3BucketExceptionError()

    async def restore_object(self, key: str, days: int, tier: str):
        try:
            await self._s3_client.restore_object(
                Bucket=self.s3_bucket,
                Key=key,
                RestoreRequest={
                    "Days": days,
                    "GlacierJobParameters": {"Tier": tier},
                },
            )
        except ClientError as e:
            log.error(f"❌ Restore object client error for key '{key}': {str(e)}")
            raise S3BucketExceptionError()

        except Exception as e:
            log.error(f"❌ Restore object unexpected error for key '{key}': {str(e)}")
            raise S3BucketExceptionError()
