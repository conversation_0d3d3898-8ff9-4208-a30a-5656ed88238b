from authlib.jose.errors import ExpiredTokenError
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer

from gc_dentist_shared.core.common.jwks_client_manager import JW<PERSON><PERSON>lientManager
from gc_dentist_shared.core.common.jwt_token import decode_jwt_token
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.messages import CustomMessageCode

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def _unauthorized(
    detail: str = CustomMessageCode.UNAUTHORIZED_ERROR.title,
) -> HTTPException:
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=detail,
        headers={"WWW-Authenticate": "Bearer"},
    )


def get_current_user_claims(configuration, token: str) -> dict:
    try:
        # 1) Find public key via J<PERSON><PERSON>
        jwks_client = JWKSClientManager.get_instance(configuration)
        signing_key = jwks_client.get_signing_key_from_jwt(token)

        # 2) Verify token
        payload = decode_jwt_token(signing_key.key, configuration.JWT_ALGORITHM, token)

        return payload

    except ExpiredTokenError as e:
        log.error(f"❌ Token expired: {e}")
        raise e

    except Exception as e:
        log.error(f"❌ Exception get_current_user_claims token failed: {e}")
        return None


def depends_get_current_user_claims(configuration):
    """Dependency for getting current user claims"""

    def parse_token(token: str = Depends(oauth2_scheme)):
        try:
            # 1) Find public key via JWKS
            jwks_client = JWKSClientManager.get_instance(configuration)
            signing_key = jwks_client.get_signing_key_from_jwt(token)

            # 2) Verify token
            payload = decode_jwt_token(
                signing_key.key, configuration.JWT_ALGORITHM, token
            )
            if not payload:
                raise _unauthorized()

            return payload

        except HTTPException as e:
            log.error(
                f"❌ HTTPException get_current_user_claims token failed: {e.detail}"
            )
            raise _unauthorized()
        except Exception as e:
            log.error(f"❌ Exception get_current_user_claims token failed: {e}")
            raise _unauthorized()

    return parse_token
