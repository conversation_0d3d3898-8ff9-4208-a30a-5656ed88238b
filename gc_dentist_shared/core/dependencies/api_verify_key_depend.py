import base64
import hashlib
import hmac

from fastapi import Depends, HTTPException, Request, status

from gc_dentist_shared.core.logger.config import log


class APIVerifyKeyDepend:
    def __init__(self, secret_key_setting: str):
        self.secret_key_setting = secret_key_setting

    async def __call__(self, request: Request):
        try:
            api_key = request.headers.get("X-Api-Key")
            x_request_value = request.headers.get("X-Request-Value")

            if not api_key or not x_request_value:
                log.error("❌ Invalid Key")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Missing API authentication headers.",
                )
            try:
                decoded_key = base64.b64decode(api_key).decode()
                signature, encoded_x_request = decoded_key.split(":")
            except Exception as e:
                log.error(f"❌ Decode Key error: {e}, data: {api_key}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid API Key format.",
                )

            calculated_signature = hmac.new(
                self.secret_key_setting.encode(),
                x_request_value.encode(),
                hashlib.sha256,
            ).hexdigest()

            if not (
                hmac.compare_digest(calculated_signature, signature)
                and encoded_x_request == x_request_value
            ):
                log.error("❌ Invalid API Key or signature")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid API Key or signature.",
                )

            return request
        except HTTPException as e:
            raise e
        except Exception as e:
            log.error(f"❌ api_verify_key error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="api_verify_key Error.",
            )


def DependsAPIVerifyKey(secret_key: str):
    return Depends(APIVerifyKeyDepend(secret_key_setting=secret_key))
