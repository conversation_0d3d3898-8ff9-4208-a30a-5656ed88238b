from http import HTTPStatus

from gc_dentist_shared.core.messages import CustomMessageCode


class ExceptionNotFoundTenantError(Exception):
    """Raised when a tenant is not found in the database."""


class CustomValueError(ValueError):
    def __init__(
        self,
        message: str,
        message_code: str | None = None,
        status_code: int = HTTPStatus.BAD_REQUEST.value,
    ):
        self.status_code = status_code
        self.message = message
        self.message_code = message_code
        super().__init__(message)


class TwilioRestExceptionError(ValueError):
    def __init__(
        self,
        message: str = CustomMessageCode.TWILIO_ERROR.title,
        message_code: str | None = CustomMessageCode.TWILIO_ERROR.code,
        status_code: int = HTTPStatus.BAD_REQUEST.value,
    ):
        self.status_code = status_code
        self.message = message
        self.message_code = message_code
        super().__init__(message)


class S3BucketExceptionError(ValueError):
    def __init__(
        self,
        message: str = CustomMessageCode.S3_BUCKET_ERROR.title,
        message_code: str | None = CustomMessageCode.S3_BUCKET_ERROR.code,
        status_code: int = HTTPStatus.BAD_REQUEST.value,
    ):
        self.status_code = status_code
        self.message = message
        self.message_code = message_code
        super().__init__(message)
