from sqlalchemy import Column, Date, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MedicalDeviceMVT(TenantBase, DateTimeMixin):
    """
    Model to store measurement data from an MVT(Motion Trainer) device.
    """

    __tablename__ = "medical_device_mvt"

    id = Column(Integer, primary_key=True, autoincrement=True)
    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    image_file_paths = Column(
        JSONB,
        nullable=True,
        comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
    )

    __table_args__ = (
        UniqueConstraint(
            "external_patient_no",
            "device_data_id",
            name="_mvt_business_patient_device",
        ),
    )
