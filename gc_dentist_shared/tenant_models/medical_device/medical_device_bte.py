from sqlalchemy import <PERSON>umn, Date, <PERSON><PERSON><PERSON>, Integer, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MedicalDeviceBTE(TenantBase, DateTimeMixin):
    """
    Model to store measurement data from a BTE (Bite Eye) device.
    """

    __tablename__ = "medical_device_bte"

    id = Column(Integer, primary_key=True, autoincrement=True)
    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    bite_array = Column(
        JSONB,
        nullable=False,
        comment="Data array containing detailed information of the bites",
    )
    comment = Column(Text, nullable=True, comment="Additional notes or comments")
    image_file_paths = Column(
        JSONB,
        nullable=True,
        comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
    )

    __table_args__ = (
        UniqueConstraint(
            "external_patient_no",
            "device_data_id",
            name="_bte_business_patient_device",
        ),
    )
