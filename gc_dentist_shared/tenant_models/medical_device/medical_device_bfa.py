from sqlalchemy import (
    Column,
    Date,
    Float,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MedicalDeviceBFA(TenantBase, DateTimeMixin):
    """
    Model to store measurement data from a BFA (Bite Force Analyzer) device.
    """

    __tablename__ = "medical_device_bfa"

    id = Column(Integer, primary_key=True, autoincrement=True)
    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    auto_cleaning = Column(
        String, comment="Auto-cleaning mode (ON/OFF), Enums: BfaDeviceAutoCleaning"
    )
    area_total = Column(
        Float, nullable=False, comment="Total occlusion contact area (mm^2)"
    )
    area_left = Column(
        Float, nullable=False, comment="Left occlusion contact area (mm^2)"
    )
    area_right = Column(
        Float, nullable=False, comment="Right occlusion contact area (mm^2)"
    )
    ave = Column(Float, nullable=False, comment="Average pressure - Total (MPa)")
    ave_left = Column(
        Float, nullable=False, comment="Average pressure - Left side (MPa)"
    )
    ave_right = Column(
        Float, nullable=False, comment="Average pressure - Right side (MPa)"
    )
    max_total = Column(Float, nullable=False, comment="Maximum pressure - Total (MPa)")
    max_left = Column(
        Float, nullable=False, comment="Maximum pressure - Left side (MPa)"
    )
    max_right = Column(
        Float, nullable=False, comment="Maximum pressure - Right side (MPa)"
    )
    force_total = Column(Float, nullable=False, comment="Total bite force (N)")
    force_left = Column(Float, nullable=False, comment="Left bite force (N)")
    force_right = Column(Float, nullable=False, comment="Right bite force (N)")
    comment = Column(Text, nullable=True, comment="Additional notes or comments")
    image_file_paths = Column(
        JSONB,
        nullable=True,
        comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
    )

    __table_args__ = (
        UniqueConstraint(
            "external_patient_no",
            "device_data_id",
            name="_bfa_business_patient_device",
        ),
    )
