from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>MP, <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, Integer

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MedicalHistory(TenantBase, DateTimeMixin):
    __tablename__ = "medical_histories"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(
        Integer,
        ForeignKey("patient_users.id"),
        nullable=False,
        comment="Reference to patient user",
    )
    patient_waiting_id = Column(
        Integer,
        ForeignKey("patient_waitings.id"),
        nullable=True,
        comment="Reference to patient waiting record",
    )
    doctor_user_ids = Column(
        ARRAY(Integer), comment="List of doctor user IDs (PostgreSQL only)"
    )
    status = Column(Integer, nullable=False, comment="Status of the medical history")
    visit_start_datetime = Column(
        TIMESTAMP(timezone=True), comment="Start datetime of the visit"
    )
