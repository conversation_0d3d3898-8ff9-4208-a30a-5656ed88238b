from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class FormItemGroup(TenantBase, DateTimeMixin):
    __tablename__ = "form_item_groups"

    uuid = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        comment="Primary key - UUID of the group",
    )
    form_uuid = Column(
        ForeignKey("forms.uuid"),
        nullable=False,
        comment="Foreign key referencing the parent form",
    )
    title = Column(
        String,
        nullable=False,
        comment="Title of the question group/section (e.g. 'Basic Information')",
    )
    description = Column(
        String,
        nullable=True,
        comment="Optional description to show under the title",
    )
    display_type = Column(
        Integer,
        nullable=True,
        comment="Display style: accordion, page, section etc.",
    )
    order_index = Column(Integer, default=0, comment="Ordering index within the form")
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether the group is active",
    )
    is_deletable = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Allow delete Form Item Groups",
    )
