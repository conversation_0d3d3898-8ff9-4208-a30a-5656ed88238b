from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Inte<PERSON>, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class FormItem(TenantBase, DateTimeMixin):
    __tablename__ = "form_items"

    uuid = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        comment="Primary key - UUID of the question/item",
    )
    form_uuid = Column(
        ForeignKey("forms.uuid"),
        nullable=False,
        comment="Foreign key referencing the parent form",
    )
    label = Column(
        String,
        nullable=False,
        comment="Label or question text shown to the user",
    )
    sub_label = Column(
        String,
        nullable=True,
        comment="Sub Label or question text shown to the user",
    )
    field_type = Column(
        String,
        nullable=False,
        comment="Type of input: text, radio, checkbox, date, image, etc.",
    )
    item_side = Column(
        String,
        nullable=True,
        comment="User role responsible for inputting this item (e.g., doctor, patient, admin)",
    )
    required = Column(
        Boolean,
        default=False,
        comment="Whether this question is required to answer",
    )
    is_favorite = Column(
        Boolean,
        default=False,
        comment="Whether this question is required to answer",
    )
    extra_data = Column(
        JSON,
        nullable=True,
        comment="Extra metadata: options, placeholders, validation rules",
    )
    order_index = Column(
        Integer, default=0, comment="Ordering index within the form or group"
    )
    form_item_group_uuid = Column(
        ForeignKey("form_item_groups.uuid"),
        nullable=True,
        comment="Optional reference to item group",
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether this question is active",
    )
    is_deletable = Column(
        Boolean, default=True, nullable=False, comment="Allow delete Form item"
    )
