from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class FormSubmission(TenantBase, DateTimeMixin):
    __tablename__ = "form_submissions"

    uuid = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        comment="Primary key - UUID of the submission",
    )
    form_flow_uuid = Column(
        ForeignKey("form_flows.uuid"),
        nullable=False,
        comment="Foreign key referencing the submitted form flow",
    )
    doctor_user_id = Column(
        ForeignKey("doctor_users.id"),
        nullable=False,
        comment="Doctor who filled out the form on behalf of the patient",
    )
    patient_user_id = Column(
        ForeignKey("patient_users.id"),
        nullable=False,
        comment="Patient for whom the form was filled out",
    )
    form_flow_data = Column(
        JSON,
        nullable=False,
        comment="Submitted answers in JSON format and form item type",
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether this question is active",
    )
