from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class FormFlow(TenantBase, DateTimeMixin):
    __tablename__ = "form_flows"

    uuid = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        comment="Primary key - UUID of the form flow",
    )
    flow_name = Column(
        String,
        nullable=False,
        comment="Name of the Flow (e.g. 'Medical Questionnaire for Adults')",
    )
    flow_type = Column(
        Integer,
        nullable=False,
        comment="Form type identifier, Ex: Survey, basic information,...",
    )
    description = Column(
        String,
        nullable=True,
        comment="Optional description to show under the title",
    )
    version = Column(
        String,
        default="1.0",
        nullable=False,
        comment="Version of the form (e.g. 1.0, 2.1)",
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether this form is currently active",
    )
    is_deletable = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Allow delete Form Flows",
    )
