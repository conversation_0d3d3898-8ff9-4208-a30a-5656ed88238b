from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class Form(TenantBase, DateTimeMixin):
    __tablename__ = "forms"

    uuid = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        comment="Primary key - UUID of the form",
    )
    form_flow_uuid = Column(
        ForeignKey("form_flows.uuid"),
        nullable=False,
        comment="Foreign key referencing the submitted form flow",
    )
    form_name = Column(
        String,
        nullable=False,
        comment="Name of the form (e.g. 'Basic Information')",
    )
    description = Column(
        String,
        nullable=True,
        comment="Optional description to show under the title",
    )
    order_index = Column(
        Integer, nullable=False, comment="Ordering index within the form flows"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether this Form is active",
    )
    is_deletable = Column(
        Boolean, default=True, nullable=False, comment="Allow delete Form"
    )
