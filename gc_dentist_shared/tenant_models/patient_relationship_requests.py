from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, UniqueConstraint

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientRelationshipRequest(TenantBase, DateTimeMixin):
    __tablename__ = "patient_relationship_requests"

    id = Column(Integer, primary_key=True, autoincrement=True)
    requester_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    target_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    relationship = Column(Integer, nullable=False)
    approved_role = Column(Integer, nullable=True)
    approved_by_id = Column(Integer, nullable=True)
    status = Column(Integer, nullable=False, default=1)
    request_count = Column(Integer, nullable=False, default=0)
    updated_by = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint(
            "requester_user_id",
            "target_user_id",
            name="uq_patient_relationship_requests",
        ),
    )
