from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class DocumentGroup(TenantBase, DateTimeMixin):
    __tablename__ = "document_group"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(
        String, unique=True, nullable=False, comment="Name of the document group"
    )

    key_name = Column(String, unique=True, nullable=True)
    is_parent = Column(Boolean, nullable=False, default=False)
    group_parent_id = Column(Integer, nullable=True)

    created_by = Column(
        Integer, nullable=True, comment="ID of the user who created the record"
    )
    updated_by = Column(
        Integer, nullable=True, comment="ID of the user who last updated the record"
    )

    __table_args__ = (
        UniqueConstraint("name", name="uq_name_document_group"),
        UniqueConstraint("key_name", name="uq_key_name_document_group"),
    )
