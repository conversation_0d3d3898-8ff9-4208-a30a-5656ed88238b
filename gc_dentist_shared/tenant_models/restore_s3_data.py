from datetime import datetime, timezone
from uuid import uuid4

from sqlalchemy import T<PERSON>ESTAMP, Column, Integer, String  # type: ignore
from sqlalchemy.sql import func

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.tenant_models.master_base import TenantBase


class RestoreS3Data(TenantBase, DateTimeMixin):
    __tablename__ = "restore_s3_data"

    id = Column(Integer, primary_key=True, autoincrement=True)
    s3_url = Column(String, nullable=False, comment="Original S3 URL of the object")
    s3_url_temp = Column(
        String, nullable=True, comment="Temporary S3 URL for restored object"
    )
    document_uuid = Column(
        String,
        nullable=False,
        default=lambda: str(uuid4()),
        comment="UUID represents a group of documents with multiple versions.",
    )
    version_id = Column(
        Integer,
        nullable=False,
        comment="The version corresponding to a group of documents. Used for generating the S3 URL",
    )
    status = Column(
        Integer,
        nullable=False,
        server_default="2",
        comment="Status of the restoration process",
    )
    expires_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="Expiration date of the restored object",
    )
    restored_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="Time when the object was restored",
    )
    requested_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        default=datetime.now(timezone.utc),
        server_default=func.now(),
        comment="Time when the restoration was requested",
    )
