from uuid import uuid4

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint, text

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class DoctorProfile(TenantBase, DateTimeMixin):
    __tablename__ = "doctor_profiles"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    doctor_user_id = Column(Integer, ForeignKey("doctor_users.id"), nullable=False)
    first_name = Column(String(length=255), nullable=False)
    last_name = Column(String(length=255), nullable=False)
    first_name_kana = Column(String(length=255), nullable=True)
    last_name_kana = Column(String(length=255), nullable=True)
    date_of_birth = Column(String, nullable=False, comment="Date of birth")
    date_of_birth_hash = Column(
        String, nullable=True, comment="Date of birth hash, sha-256"
    )
    address_1 = Column(String(), nullable=True)
    address_2 = Column(String(), nullable=True)
    address_3 = Column(String(), nullable=True)
    order_index = Column(
        Integer,
        comment="Order index of doctor",
        server_default=text("0"),
        nullable=False,
    )
    phone = Column(String, nullable=False)
    phone_hash = Column(String(), comment="Phone hash, sha-256", nullable=True)
    email = Column(String(), nullable=True)
    email_hash = Column(
        String(), default=None, comment="Email hash, sha-256", nullable=True
    )
    gender = Column(
        Integer, nullable=False, server_default=text("1"), comment="1:Male, 2:Female"
    )
    prefecture_id = Column(Integer, ForeignKey("m_prefecture.id"), nullable=True)
    postal_code = Column(
        String(length=8),
        comment="Post code, ensure original length max 8",
        nullable=True,
    )
    country_code = Column(
        String(length=3),
        comment="Country code, ensure original length max 10",
    )

    created_by = Column(Integer, nullable=True)
    updated_by = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint("email", name="uq_doctor_profiles_email"),
        UniqueConstraint("phone", name="uq_doctor_profiles_phone"),
    )
