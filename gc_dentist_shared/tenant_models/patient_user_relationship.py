from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientUserRelationship(TenantBase, DateTimeMixin):
    __tablename__ = "patient_user_relationship"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    sub_patient_user_id = Column(
        Integer, ForeignKey("patient_users.id"), nullable=False
    )
    relationship = Column(Integer, nullable=False)
    request_id = Column(
        Integer, ForeignKey("patient_relationship_requests.id"), nullable=False
    )
    comment = Column(String, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint(
            "patient_user_id",
            "sub_patient_user_id",
            name="uq_patient_user_relationship",
        ),
    )
