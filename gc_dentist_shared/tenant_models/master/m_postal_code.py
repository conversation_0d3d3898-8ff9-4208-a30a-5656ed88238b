from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MasterPostalCode(TenantBase, DateTimeMixin):
    __tablename__ = "m_postal_code"

    id = Column(Integer, primary_key=True, autoincrement=True)
    postal_code = Column(String(20), nullable=False)
    pref_name = Column(String(64), nullable=False)
    pref_type = Column(String(32), nullable=True)
    pref_code = Column(String(16), nullable=True)
    city_name = Column(String(64), nullable=True)
    city_type = Column(String(32), nullable=True)
    city_code = Column(String(16), nullable=True)
    town = Column(String(128), nullable=True)
    del_flag = Column(Boolean, default=False)
