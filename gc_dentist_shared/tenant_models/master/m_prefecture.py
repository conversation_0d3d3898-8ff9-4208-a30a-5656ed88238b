from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, Integer

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MasterPrefecture(TenantBase, DateTimeMixin):
    __tablename__ = "m_prefecture"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name_json = Column(JSON, nullable=False)
    sort = Column(Integer, nullable=True)
    del_flag = Column(Boolean, default=False)
