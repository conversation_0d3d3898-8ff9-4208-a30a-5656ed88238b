from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MasterOralExamination(TenantBase, DateTimeMixin):
    __tablename__ = "m_oral_examinations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name_jp = Column(String, comment="Name Japanese")
    name_en = Column(String, comment="Name English")
    type = Column(
        String, default="FULL", comment="Type of the examination (FULL, PART, OVER)"
    )
    category = Column(
        Integer,
        default=1,
        comment="Category of the examination (1: Treatment, 2: Method, 3: Both Method and Treatment, 4: Other)",
    )
    order = Column(JSON, comment="Order of the examination items")
    need_treatment = Column(
        Integer,
        default=1,
        comment="Does this examination require treatment? (1: Yes, 2: No)",
    )
    is_hidden = Column(
        Boolean,
        default=False,
        comment="Is this examination hidden from the list?",
    )
