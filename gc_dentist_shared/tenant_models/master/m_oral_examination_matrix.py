from sqlalchemy import Column, Integer, UniqueConstraint

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MasterOralExaminationMatrix(TenantBase, DateTimeMixin):
    __tablename__ = "m_oral_examination_matrix"

    id = Column(Integer, primary_key=True, autoincrement=True)
    m_oral_examination_id = Column(
        Integer,
        nullable=False,
        comment="ID of the oral examination in the master table",
    )
    m_oral_examination_depend_id = Column(
        Integer,
        nullable=False,
        comment="ID of the dependent oral examination in the master table",
    )

    __table_args__ = (
        UniqueConstraint(
            "m_oral_examination_id",
            "m_oral_examination_depend_id",
            name="uq_master_oral_examination_matrix_depend",
        ),
    )
