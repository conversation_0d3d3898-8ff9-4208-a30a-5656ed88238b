from sqlalchemy import Column, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MasterEra(TenantBase, DateTimeMixin):
    __tablename__ = "m_eras"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, comment="Name of the eras")
    year = Column(Integer, comment="Year of the eras")
