from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class TeethDetail(TenantBase, DateTimeMixin):
    __tablename__ = "teeth_details"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    tooth_data = Column(
        JSON, nullable=False, comment="Details of the tooth all attributes"
    )
