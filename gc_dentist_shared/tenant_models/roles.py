from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer  # type: ignore
from sqlalchemy.dialects.postgresql import JSON<PERSON>

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class Roles(TenantBase, DateTimeMixin):
    """Roles table"""

    __tablename__ = "roles"

    role_key_id = Column(
        Integer,
        primary_key=True,
        autoincrement=False,
        comment="1: CLINIC_ADMIN, 3: CLINIC_STAFF, Enums: RoleKeyEnum",
        nullable=False,
    )
    name_json = Column(JSONB, comment="Master name for multiple languages")
    is_active = Column(Boolean, default=True, nullable=False)
    is_system = Column(Boolean, default=False, nullable=False)
    delete_flag = Column(Boolean, default=False)
