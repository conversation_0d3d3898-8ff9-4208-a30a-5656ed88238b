import hashlib
from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Integer, String, text

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class DoctorUser(TenantBase, DateTimeMixin):
    __tablename__ = "doctor_users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    uuid = Column(String, default=lambda: str(uuid4()))
    username = Column(String, nullable=False, unique=True)
    password = Column(String, nullable=False)
    login_failed = Column(Integer, nullable=False, default=0)
    login_success = Column(Integer, nullable=False, server_default=text("0"))
    required_change_password = Column(Boolean, nullable=False, default=False)
    status = Column(Boolean, nullable=False, server_default=text("true"))
    last_login = Column(TIMESTAMP(timezone=True), nullable=True)
    otp_first_send_at = Column(TIMESTAMP(timezone=True), nullable=True)
    otp_lock_expires_at = Column(TIMESTAMP(timezone=True), nullable=True)
    created_by = Column(
        Integer, nullable=True, comment="ID of the user who created the record"
    )
    updated_by = Column(
        Integer, nullable=True, comment="ID of the user who last updated the record"
    )

    def set_password(self, plain_password, tenant_uuid):
        if tenant_uuid is None and plain_password is None:
            raise Exception("tenant_uuid and plain_password cannot be None")

        if plain_password is not None and tenant_uuid is not None:
            self.password = hashlib.sha256(
                (plain_password + tenant_uuid).encode("utf-8")
            ).hexdigest()
        else:
            self.password = None

    def validate_password(self, plain_password, tenant_uuid):
        if plain_password is None or self.password is None or tenant_uuid is None:
            return False
        hashed = hashlib.sha256(
            (plain_password + tenant_uuid).encode("utf-8")
        ).hexdigest()
        return hashed == self.password
