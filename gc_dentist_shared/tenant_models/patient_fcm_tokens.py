from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientFCMToken(TenantBase, DateTimeMixin):
    __tablename__ = "patient_fcm_tokens"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    fcm_token = Column(String, nullable=False, comment="Firebase Cloud Messaging token")
    is_active = Column(
        <PERSON><PERSON><PERSON>, default=True, comment="Indicates if the FCM token is active"
    )
