from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class OralExamination(TenantBase, DateTimeMixin):
    __tablename__ = "oral_examinations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    medical_history_id = Column(
        Integer, ForeignKey("medical_histories.id"), nullable=False
    )
    examination_date = Column(TIMESTAMP(timezone=True), nullable=False)
    note = Column(Text, nullable=True)
    memo_path = Column(
        String,
        nullable=True,
        comment="Path to the oral examination memo file in S3",
    )
    tooth_type = Column(String, nullable=True, comment="Type of tooth examined")
    intraoral_examination = Column(
        JSON, nullable=True, comment="Details of the oral examination"
    )
    periodontal_1point = Column(JSON, nullable=True, comment="Periodontal 1-point")
    periodontal_4point = Column(JSON, nullable=True, comment="Periodontal 4-point")
    periodontal_6point = Column(JSON, nullable=True, comment="Periodontal 6-point")
    pcr = Column(JSON, nullable=True, comment="PCR results")
    pcr_6point = Column(JSON, nullable=True, comment="PCR 6-point results")

    created_by = Column(
        Integer, nullable=True, comment="ID of the user who created the record"
    )
    updated_by = Column(
        Integer,
        nullable=True,
        comment="ID of the user who last updated the record",
    )
