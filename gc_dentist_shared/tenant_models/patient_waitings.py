from sqlalchemy import (
    ARRAY,
    TIMES<PERSON><PERSON>,
    Column,
    Date,
    ForeignKey,
    Integer,
    String,
    Time,
    UniqueConstraint,
)

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientWaiting(TenantBase, DateTimeMixin):
    __tablename__ = "patient_waitings"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    reservation_id = Column(
        Integer, ForeignKey("reservations.id"), nullable=True, comment="Reservation ID"
    )
    emergency_flag = Column(
        Integer, nullable=False, default=0, comment="0: Normal, 1: Emergency"
    )
    coming_time = Column(TIMESTAMP(timezone=True), nullable=True)
    status = Column(Integer, nullable=False, default=0, comment="status")

    visit_start_date = Column(Date, nullable=False, comment="Visit start date")
    visit_start_time = Column(Time, nullable=False, comment="Visit start time")
    visit_end_date = Column(Date, nullable=True, comment="Visit end date")
    visit_end_time = Column(Time, nullable=True, comment="Visit end time")
    assigned_doctors = Column(
        ARRAY(Integer),
        nullable=True,
        comment="Comma-separated list of doctor IDs or names",
    )
    assigned_room = Column(String, nullable=True, comment="Assigned room name or ID")
    room_number = Column(Integer, comment="Room number", nullable=False, default=0)

    __table_args__ = (
        UniqueConstraint("reservation_id", name="uq_patient_waitings_reservation_id"),
    )
