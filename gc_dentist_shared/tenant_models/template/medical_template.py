from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase
from gc_dentist_shared.base.user_audit_mixin import UserAuditMixin


class MedicalTemplate(TenantBase, DateTimeMixin, UserAuditMixin):
    __tablename__ = "medical_templates"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    page_data = Column(JSONB, nullable=False, comment="Data of the page")
    status = Column(Integer, nullable=False, comment="Status of the template")
    size = Column(Integer, nullable=True, comment="Size of the template")
    tag = Column(Integer, nullable=True, comment="Tag of the template")
    document_group_id = Column(
        Integer,
        ForeignKey("document_group.id"),
        nullable=True,
        comment="Reference to document group",
    )
