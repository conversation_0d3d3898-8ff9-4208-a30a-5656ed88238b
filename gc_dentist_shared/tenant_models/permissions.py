from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String  # type: ignore

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class Permission(TenantBase, DateTimeMixin):
    __tablename__ = "permissions"
    id = Column(Integer, primary_key=True, autoincrement=True)
    module = Column(String(length=255), comment="Module Screen Name allowed to perform")
    sub_module = Column(
        String(length=255), comment="Sub Module Screen Name allowed to perform"
    )
    action = Column(String(length=30), comment="Action for module")
    permission_key = Column(
        String(length=255), comment="key permission, Example: Notification:view"
    )
    delete_flag = Column(Boolean, default=False, comment="Delete flag")
