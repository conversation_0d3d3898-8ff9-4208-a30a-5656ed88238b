from sqlalchemy import Column, Integer
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class MasterBaseModel(TenantBase):
    id = Column(Integer, primary_key=True, autoincrement=True)
    name_json = Column(JSONB, comment="Master name")
    sort = Column(Integer, unique=True, comment="Sort")

    __abstract__ = True

    # @hybrid_property
    # def name(self) -> str:
    #     lang = i18n.get_language()
    #     return self.name_json.get(lang) if self.name_json else None

    # @name.expression
    # def name(self) -> str:
    #     lang = i18n.get_language()
    #     name_json = func.jsonb_extract_path_text(self.name_json, lang)
    #     return name_json
