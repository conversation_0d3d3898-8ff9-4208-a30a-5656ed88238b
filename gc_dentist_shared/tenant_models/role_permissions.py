from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class RolePermission(TenantBase, DateTimeMixin):
    """Role permissions table linking roles to permissions"""

    __tablename__ = "role_permissions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    role_key_id = Column(
        Integer,
        ForeignKey("roles.role_key_id", ondelete="RESTRICT"),
        nullable=False,
        comment="Reference to role",
    )
    permission_id = Column(
        Integer,
        ForeignKey("permissions.id", ondelete="RESTRICT"),
        comment="Reference to permission",
    )
    delete_flag = Column(Boolean, default=False, comment="Delete flag")
