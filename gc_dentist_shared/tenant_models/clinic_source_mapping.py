from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class ClinicSourceMapping(TenantBase, DateTimeMixin):
    __tablename__ = "clinic_source_mappings"
    id = Column(Integer, primary_key=True, autoincrement=True)
    external_clinic_id = Column(String, nullable=False)
    source = Column(String, unique=True, nullable=False)
    extra_data = Column(JSON, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
