from sqlalchemy import <PERSON>umn, Date, Foreign<PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB, UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MedicalDeviceMVT(CentralBase, DateTimeMixin):
    """
    Model to store measurement data from an MVT(Motion Trainer) device.
    """

    __tablename__ = "medical_device_mvt"

    id = Column(Integer, primary_key=True, autoincrement=True)
    business_number = Column(
        String,
        nullable=False,
        comment="Identifier for the business establishment/clinic",
    )
    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )
    tenant_uuid = Column(
        UUID(as_uuid=True),
        ForeignKey("tenant_clinics.tenant_uuid"),
        nullable=False,
        comment="Foreign key to the TenantClinic table",
    )
    patient_user_id = Column(
        Integer,
        nullable=True,
        comment="ID of the patient in the internal system (if available)",
    )
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    image_file_paths = Column(
        JSONB,
        nullable=True,
        comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
    )

    __table_args__ = (
        UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_mvt_business_patient_device",
        ),
    )
