# gc_dentist_shared/central_models/medical_device/medical_device_sync_logs.py

from sqlalchemy import Column, Date, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MedicalDeviceSyncLogs(CentralBase, DateTimeMixin):
    """
    Model to log the history and status of data synchronization from devices
    into the database systems (Central and Tenant).
    """

    __tablename__ = "medical_device_sync_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    json_file_path = Column(
        Text,
        nullable=True,
        comment="The full S3 path to the ingested file. Unique if not null.",
    )
    business_number = Column(
        String, nullable=True, comment="Business number of the imported data"
    )
    external_patient_no = Column(
        String, nullable=True, comment="Patient number of the imported data"
    )
    device_data_id = Column(
        String, nullable=True, comment="Device data ID of the imported data"
    )
    device_type = Column(
        String,
        nullable=False,
        comment="Device type (BFA, EMG, MVT, BTE),Enums: MedicalDeviceType ",
    )
    examination_date = Column(
        Date, nullable=True, comment="Examination date of the imported data"
    )
    central_sync_status = Column(
        Integer,
        nullable=False,
        comment="Synchronization status to Central DB (SUCCESS, FAILED), Enums: MedicalDeviceSyncStatus",
    )
    tenant_sync_status = Column(
        Integer,
        nullable=False,
        comment="Synchronization status to Tenant DB (SUCCESS, FAILED), Enums: MedicalDeviceSyncStatus",
    )
    error_message = Column(
        Text, nullable=True, comment="Detailed error message if any process fails"
    )
    raw_data_payload = Column(
        JSONB,
        nullable=True,
        comment="Original JSON payload, used for debugging or reprocessing",
    )
