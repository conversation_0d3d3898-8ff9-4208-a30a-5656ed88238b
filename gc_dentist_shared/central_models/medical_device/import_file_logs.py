from sqlalchemy import Column, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class ImportFileLog(CentralBase, DateTimeMixin):
    """
    Model to log files received from external systems, tracking their
    origin, storage location, and reception status.
    """

    __tablename__ = "import_file_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)

    source = Column(
        String,
        nullable=False,
        index=True,
        comment="The source channel that sent the file, e.g., 'MIDDLEWARE_MEDICAL_APP'.",
    )

    json_file_path = Column(
        Text,
        unique=True,
        nullable=False,
        comment="The full S3 path to the ingested file. Unique if not null.",
    )
    image_file_paths = Column(
        JSONB,
        nullable=True,
        comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
    )

    status = Column(
        Integer,
        nullable=False,
        comment="The status of the file reception. Enums: SourceFileStatus",
    )

    error_message = Column(
        Text,
        nullable=True,
        comment="Stores the reason for a reception failure.",
    )
