from sqlalchemy import (
    Column,
    Date,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    String,
    Time,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MedicalDeviceEMG(CentralBase, DateTimeMixin):
    """
    Model to store measurement data from an EMG(Wearable Electromyograph) device.
    """

    __tablename__ = "medical_device_emg"

    id = Column(Integer, primary_key=True, autoincrement=True)
    business_number = Column(
        String,
        nullable=False,
        comment="Identifier for the business establishment/clinic",
    )
    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )
    tenant_uuid = Column(
        UUID(as_uuid=True),
        ForeignKey("tenant_clinics.tenant_uuid"),
        nullable=False,
        comment="Foreign key to the TenantClinic table",
    )
    patient_user_id = Column(
        Integer,
        nullable=True,
        comment="ID of the patient in the internal system (if available)",
    )
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    start_time = Column(Time, nullable=False, comment="Measurement start time")
    end_time = Column(Time, nullable=False, comment="Measurement end time")
    total_time = Column(
        Time, nullable=False, comment="Total analysis time (string format)"
    )
    max_peak = Column(
        Float, nullable=False, comment="Maximum clenching peak value (mV)"
    )
    base_peak = Column(
        Float, nullable=False, comment="Baseline section peak value (mV)"
    )
    total_clenching = Column(Float, nullable=False, comment="Total number of clenches")
    clenching_per_hour = Column(
        Float, nullable=False, comment="Clenching frequency per hour"
    )
    burst_total = Column(Float, nullable=False, comment="Total number of bursts")
    burst_total_dur = Column(
        Time, nullable=False, comment="Total duration of bursts (string format)"
    )
    burst_total_ave = Column(
        Time, nullable=False, comment="Average duration of bursts (string format)"
    )
    burst_per_hour = Column(Float, nullable=False, comment="Number of bursts per hour")
    burst_total_duration_per_hour = Column(
        Time,
        nullable=False,
        comment="Total duration of bursts per hour (string format)",
    )
    clenching_strength_ave = Column(
        Float, nullable=False, comment="Average clenching intensity"
    )
    image_file_paths = Column(
        JSONB,
        nullable=True,
        comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
    )

    __table_args__ = (
        UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_emg_business_patient_device",
        ),
    )
