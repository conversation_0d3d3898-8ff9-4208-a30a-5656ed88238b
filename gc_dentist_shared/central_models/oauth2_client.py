from authlib.integrations.sqla_oauth2 import OAuth2ClientMixin
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class OAuth2Client(CentralBase, DateTimeMixin, OAuth2ClientMixin):
    __tablename__ = "oauth_clients"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)

    tenant_uuid = Column(UUID(as_uuid=True), ForeignKey("tenant_clinics.tenant_uuid"))
    is_active = Column(Boolean, default=True)

    # def check_response_type(self, response_type):
    #     return response_type in self.response_types

    # def check_scope(self, scope):
    #     allowed = set(self.scope.split())
    #     return all(s in allowed for s in scope.split())
