import hashlib

from sqlalchemy import <PERSON><PERSON><PERSON>, TIMESTAMP, <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON>ey, Integer, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class GlobalUser(CentralBase, DateTimeMixin):
    __tablename__ = "global_users"
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String, unique=True, index=True)  # username, email, phone, etc.
    username_hash = Column(String, index=True)
    day_of_birth = Column(String, comment="Encrypted day of birth")
    day_of_birth_hash = Column(String, index=True)
    password = Column(String, nullable=True)  # support only for email or username
    is_active = Column(Boolean, nullable=False, default=True)
    extra_data = Column(JSON, nullable=True)
    last_login = Column(TIMESTAMP(timezone=True), nullable=True)
    user_mapping = relationship(
        "TenantPatientUserMapping", back_populates="global_user"
    )
    provider_uuid = Column(
        UUID(as_uuid=True), ForeignKey("auth_providers.provider_uuid"), nullable=True
    )
    provider_subject = Column(String, nullable=True)
    provider = relationship("AuthProvider", back_populates="global_user_mappings")

    def set_password(self, plain_password, tenant_uuid):
        if tenant_uuid is None and plain_password is None:
            raise Exception("tenant_uuid and plain_password cannot be None")

        if plain_password is not None and tenant_uuid is not None:
            self.password = hashlib.sha256(
                (plain_password + tenant_uuid).encode("utf-8")
            ).hexdigest()
        else:
            self.password = None

    def validate_password(self, plain_password, tenant_uuid):
        if plain_password is None or self.password is None or tenant_uuid is None:
            return False
        hashed = hashlib.sha256(
            (plain_password + tenant_uuid).encode("utf-8")
        ).hexdigest()
        return hashed == self.password

    def get_user_id(self):
        return self.id
