from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MailTemplate(CentralBase, DateTimeMixin):
    __tablename__ = "mail_templates"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    category = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    status = Column(Boolean, default=True)
    language = Column(String(5), default="ja")  # ISO language code
