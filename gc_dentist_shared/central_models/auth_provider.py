import enum
import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class ProviderType(str, enum.Enum):
    internal = "internal"  # Local user
    oauth2 = "oauth2"  # Google, Facebook
    oidc = "oidc"  # OpenID Connect


class AuthProvider(CentralBase, DateTimeMixin):
    __tablename__ = "auth_providers"

    provider_uuid = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    provider_type = Column(Enum(ProviderType), nullable=False)
    config = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True)

    user_mappings = relationship("TenantPatientUserMapping", back_populates="provider")
    global_user_mappings = relationship("GlobalUser", back_populates="provider")
