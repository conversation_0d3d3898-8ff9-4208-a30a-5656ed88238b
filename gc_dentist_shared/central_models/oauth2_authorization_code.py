from datetime import datetime, timezone

from authlib.integrations.sqla_oauth2 import OAuth2AuthorizationCodeMixin
from sqlalchemy import Column, Foreign<PERSON>ey, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class OAuth2AuthorizationCode(CentralBase, DateTimeMixin, OAuth2AuthorizationCodeMixin):
    __tablename__ = "oauth2_authorization_codes"

    id = Column(Integer, primary_key=True)
    code = Column(String, index=True, unique=True, nullable=False)
    global_user_id = Column(Integer, ForeignKey("global_users.id"), nullable=True)
    clinic_user_id = Column(
        Integer, ForeignKey("tenant_user_mappings.id"), nullable=True
    )
    tenant_uuid = Column(UUID(as_uuid=True), ForeignKey("tenant_clinics.tenant_uuid"))
    expires_at = Column(Integer)

    def is_expired(self):
        return datetime.now(timezone.utc).timestamp() > self.expires_at
