from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>n, <PERSON><PERSON>ey, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class TenantPlan(CentralBase, DateTimeMixin):
    """Tenant plans table tracking which tenant is subscribed to which plan"""

    __tablename__ = "tenant_plans"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_uuid = Column(
        UUID(as_uuid=True), nullable=False, comment="Unique identifier of the tenant"
    )
    plan_key_id = Column(
        Integer,
        ForeignKey("m_plans.plan_key_id", ondelete="RESTRICT"),
        nullable=False,
        comment="Reference to plan",
    )
    status = Column(
        Integer,
        nullable=False,
        default=1,
        comment="Enums TenantPlanStatus",
    )
    start_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        comment="When the plan started for the tenant",
    )
    end_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="When the plan ends for the tenant (null for unlimited)",
    )

    __table_args__ = (
        UniqueConstraint("tenant_uuid", "plan_key_id", name="uq_tenant_plan"),
    )
