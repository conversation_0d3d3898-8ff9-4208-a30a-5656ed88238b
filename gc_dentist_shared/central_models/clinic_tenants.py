from uuid import uuid4

from sqlalchemy import <PERSON>umn, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class TenantClinic(CentralBase, DateTimeMixin):
    __tablename__ = "tenant_clinics"

    tenant_uuid = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    business_number = Column(
        String,
        nullable=False,
        comment="Identifier for the business establishment/clinic",
    )
    tenant_name = Column(String, nullable=False, unique=True)
    tenant_slug = Column(String, nullable=False, unique=True)
    db_name = Column(String, nullable=False)
    db_uri = Column(String, nullable=True)
    plan_id = Column(Integer, nullable=True)
    status = Column(Integer, default=20)
