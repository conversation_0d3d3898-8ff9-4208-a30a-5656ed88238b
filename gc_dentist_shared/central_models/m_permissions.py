from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MasterPermission(CentralBase, DateTimeMixin):
    """Master permissions table in central database"""

    __tablename__ = "m_permissions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    module = Column(String(length=255), comment="Module Screen Name allowed to perform")
    sub_module = Column(
        String(length=255), comment="Sub Module Screen Name allowed to perform"
    )
    action = Column(String(length=30), comment="Action for module")
    permission_key = Column(
        String(length=255), comment="key permission, Example: Notification:view"
    )
    delete_flag = Column(Boolean, default=False, comment="Delete flag")
