# from gc_dentist_shared.base.central_declarative_base import CentralBase
# from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
# from sqlalchemy import Column, String, ForeignKey, Boolean, DateTime, Integer
# from sqlalchemy.dialects.postgresql import UUID
# import uuid

# class OauthRefreshToken(CentralBase, DateTimeMixin):
#     __tablename__ = "oauth_refresh_tokens"

#     id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
#     global_user_id = Column(Integer, ForeignKey("global_users.id"), nullable=True)
#     clinic_user_id = Column(UUID(as_uuid=True), ForeignKey("tenant_user_mappings.id"), nullable=True)
#     token = Column(String, unique=True, nullable=False)
#     expires_at = Column(DateTime, nullable=False)
#     revoked = Column(Boolean, default=False)
