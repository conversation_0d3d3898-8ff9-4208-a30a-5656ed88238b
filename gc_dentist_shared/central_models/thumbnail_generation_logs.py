from sqlalchemy import Column, Integer, Text

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class ThumbnailGenerationLog(CentralBase, DateTimeMixin):
    """
    Model to log the status and outcome of each thumbnail generation task.
    """

    __tablename__ = "thumbnail_generation_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)

    original_image_path = Column(
        Text,
        nullable=False,
        comment="The S3 path of the original image being processed.",
    )

    thumbnail_image_path = Column(
        Text,
        nullable=True,
        comment="The S3 path of the successfully generated thumbnail.",
    )

    status = Column(
        Integer,
        nullable=False,
        comment="The status of the file reception. Enums: ThumbnailStatus(IntEnum)",
    )

    error_message = Column(
        Text,
        nullable=True,
        comment="Stores the reason for a reception failure.",
    )
