# from gc_dentist_shared.base.central_declarative_base import CentralBase
# from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
# from sqlalchemy import Column, String, ForeignKey, JSON, LargeBinary, Integer
# from sqlalchemy.orm import relationship

# from sqlalchemy.dialects.postgresql import UUID
# import uuid
# import enum
# from datetime import datetime

# class WebAuthCredential(CentralBase, DateTimeMixin):
#     __tablename__ = "web_auth_credentials"

#     id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
#     global_user_id = Column(Integer, ForeignKey("global_users.id"), nullable=True)
#     clinic_user_id = Column(UUID(as_uuid=True), ForeignKey("tenant_user_mappings.id"), nullable=True)
#     credential_id = Column(LargeBinary, nullable=False)
#     public_key = Column(LargeBinary, nullable=False)
#     sign_count = Column(String, nullable=False)
#     transports = Column(JSON)
