from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MasterPlanPermission(CentralBase, DateTimeMixin):
    """Master plan permissions table linking plans to permissions with limits in central database"""

    __tablename__ = "m_plan_permissions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    plan_key_id = Column(
        Integer,
        ForeignKey("m_plans.plan_key_id", ondelete="RESTRICT"),
        nullable=False,
        comment="Reference to plan",
    )
    permission_id = Column(
        Integer,
        ForeignKey("m_permissions.id", ondelete="RESTRICT"),
        nullable=False,
        comment="Reference to permission",
    )
