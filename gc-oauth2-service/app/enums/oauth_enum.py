from enums.base import StrEnum


class GrantType(StrEnum):
    REFRESH_TOKEN = "refresh_token"
    AUTHORIZATION_CODE = "authorization_code"


class ResponseType(StrEnum):
    CODE = "code"
    TOKEN = "token"


class TokenEndpointAuthMethod(StrEnum):
    CLIENT_SECRET_BASIC = "client_secret_basic"  # pragma: allowlist secret
    CLIENT_SECRET_POST = "client_secret_post"  # pragma: allowlist secret


class ScopeSchema(StrEnum):
    PROFILE = "profile"
    EMAIL = "email"
