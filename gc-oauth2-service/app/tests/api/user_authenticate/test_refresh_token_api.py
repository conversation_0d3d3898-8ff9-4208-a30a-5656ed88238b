from http import HTTPStatus

import pytest
from core.messages import CustomMessageCode
from sqlalchemy import text
from tests.helpers.insert_data.create_oauth_token import (
    unittest_dynamic_create_oauth_token,
)
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_global_user import (
    unittest_create_global_user,
    unittest_create_tenant_patient_user_mapping,
)
from tests.helpers.insert_data.insert_patient_user import unittest_create_patient_user
from werkzeug.security import gen_salt


def get_payload(overrides=None):
    payload = {
        "client_id": gen_salt(24),
        "refresh_token": gen_salt(48),
        "grant_type": "refresh_token",
    }
    if overrides:
        payload.update(overrides)

    return payload


@pytest.fixture(scope="class")
def setup_global_user_flow_data(
    sync_central_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    client_id = internal_oauth_client["client_id"]
    tenant_uuid = str(tenant_uuid)

    try:
        sync_central_db_session_object.begin()
        global_user = unittest_create_global_user(sync_central_db_session_object)
        tenant_patient_user_mapping = unittest_create_tenant_patient_user_mapping(
            sync_central_db_session_object,
            global_user_id=global_user.id,
            tenant_uuid=tenant_uuid,
        )
        oauth_token = unittest_dynamic_create_oauth_token(
            sync_central_db_session_object,
            user=global_user,
            client_id=client_id,
            global_user_id=global_user.id,
        )
        sync_central_db_session_object.commit()
        return {
            "global_user_id": global_user.id,
            "tenant_patient_user_mapping_id": tenant_patient_user_mapping.id,
            "refresh_token": oauth_token.refresh_token,
        }

    finally:
        sync_central_db_session_object.close()


@pytest.fixture(scope="class")
def setup_clinic_patient_data(
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    client_id = internal_oauth_client["client_id"]
    tenant_uuid = str(tenant_uuid)
    try:
        sync_tenant_db_session_object.begin()
        patient_user = unittest_create_patient_user(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
            username=gen_salt(24),
        )

        sync_central_db_session_object.begin()
        oauth_token = unittest_dynamic_create_oauth_token(
            sync_central_db_session_object,
            user=patient_user,
            client_id=client_id,
            clinic_patient_id=patient_user.id,
            tenant_uuid=tenant_uuid,
        )

        refresh_token = oauth_token.refresh_token
        patient_user_id = patient_user.id

        sync_tenant_db_session_object.commit()
        sync_central_db_session_object.commit()
    finally:
        sync_tenant_db_session_object.close()
        sync_central_db_session_object.close()

    return {"refresh_token": refresh_token, "patient_user_id": patient_user_id}


@pytest.fixture(scope="class")
def setup_clinic_doctor_data(
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    client_id = internal_oauth_client["client_id"]
    tenant_uuid = str(tenant_uuid)

    try:
        sync_tenant_db_session_object.begin()
        doctor_user = unittest_insert_doctor(sync_tenant_db_session_object, tenant_uuid)

        sync_central_db_session_object.begin()
        oauth_token = unittest_dynamic_create_oauth_token(
            sync_central_db_session_object,
            user=doctor_user,
            client_id=client_id,
            clinic_doctor_id=doctor_user.id,
            tenant_uuid=tenant_uuid,
        )

        refresh_token = oauth_token.refresh_token
        doctor_user_id = doctor_user.id

        sync_tenant_db_session_object.commit()
        sync_central_db_session_object.commit()
    finally:
        sync_tenant_db_session_object.close()
        sync_central_db_session_object.close()

    return {"refresh_token": refresh_token, "doctor_user_id": doctor_user_id}


def test_refresh_token_global_user_success(
    client,
    sync_central_db_session_object,
    setup_global_user_flow_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_global_user_flow_data,
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]
    assert body["data"]["tenant_uuids"] == [str(tenant_uuid)]


def test_refresh_token_clinic_patient_success(
    client,
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    setup_clinic_patient_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_clinic_patient_data,
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]
    assert body["data"]["tenant_uuids"] == [str(tenant_uuid)]


def test_refresh_token_clinic_doctor_success(
    client,
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    setup_clinic_doctor_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_clinic_doctor_data,
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]
    assert "tenant_uuid" in body["data"]
    assert body["data"]["tenant_uuid"] == str(tenant_uuid)


@pytest.mark.parametrize(
    "data_idx, invalid_payload, expected_status_code, expected_message",
    [
        (
            0,
            # Invalid payload, missing request body
            {},
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
        ),
        (
            1,
            # Invalid payload, missing request bodys
            None,
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
        ),
        (
            2,
            # Invalid grant type
            get_payload({"grant_type": "invalid_grand_type"}),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INVALID_GRANT_TYPE.title,
        ),
        (
            3,
            # Invalid client_id
            get_payload({"client_id": gen_salt(24)}),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
        ),
    ],
)
def test_refresh_token_invalid_request(
    client,
    data_idx,
    invalid_payload,
    expected_status_code,
    expected_message,
    tenant_uuid,
    internal_oauth_client,
):
    response = client.post("/refresh-token", json=invalid_payload)

    assert response.status_code == expected_status_code
    body = response.get_json()
    assert body["success"] is False
    assert body["message"] == expected_message


def test_refresh_token_invalid_refresh_token(
    client,
    setup_global_user_flow_data,
    tenant_uuid,
    internal_oauth_client,
):
    data = setup_global_user_flow_data
    payload = get_payload({"client_id": internal_oauth_client["client_id"], **data})

    # Invalid refresh token
    invalid_payload = {**payload, "refresh_token": gen_salt(48)}
    response = client.post("/refresh-token", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    body = response.get_json()
    assert body["success"] is False
    assert body["message"] == CustomMessageCode.INVALID_REFRRESH_TOKEN.title


def test_refresh_token_inactive_oauth_client(
    client,
    sync_central_db_session_object,
    setup_global_user_flow_data,
    internal_oauth_client,
):
    client_id = internal_oauth_client["client_id"]
    refresh_token = setup_global_user_flow_data["refresh_token"]

    # Deactivate client
    try:
        with sync_central_db_session_object.begin():
            sync_central_db_session_object.execute(
                text(
                    "UPDATE oauth_clients SET is_active = false WHERE client_id = :cid"
                ),
                {"cid": client_id},
            )

        payload = get_payload({"client_id": client_id, "refresh_token": refresh_token})
        response = client.post("/refresh-token", json=payload)

        assert response.status_code == HTTPStatus.UNAUTHORIZED.value
        body = response.get_json()
        assert body["success"] is False
        assert body["message"] == CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title
    finally:
        # Reactivate client to avoid side effects on other tests
        with sync_central_db_session_object.begin():
            sync_central_db_session_object.execute(
                text(
                    "UPDATE oauth_clients SET is_active = true WHERE client_id = :cid"
                ),
                {"cid": client_id},
            )
