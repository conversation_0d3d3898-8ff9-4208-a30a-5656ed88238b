import time
from http import HTTPStatus
from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from tests.helpers.insert_data.insert_global_user import (
    unittest_create_global_user,
    unittest_create_tenant_patient_user_mapping,
    unittest_remove_tenant_patient_user_mapping_by_ids,
)
from werkzeug.security import gen_salt


def get_payload(overrides=None) -> dict:
    payload = {
        "client_id": gen_salt(24),
        "phone_number": str(int(time.time() * 1e6))[-11:],
        "country_code": "+81",
    }
    if overrides:
        payload.update(overrides)
    return payload


@pytest.fixture(scope="class")
def setup_data(sync_central_db_session_object, tenant_uuid: str) -> dict:
    phone_number = str(int(time.time() * 1e6))[-11:]
    country_code = "+81"

    with sync_central_db_session_object.begin():
        global_user = unittest_create_global_user(
            sync_central_db_session_object,
            phone_number=phone_number,
            country_code=country_code,
            is_active=True,
        )

        tenant_patient_user_mapping = unittest_create_tenant_patient_user_mapping(
            sync_central_db_session_object,
            global_user_id=global_user.id,
            tenant_uuid=tenant_uuid,
        )

        return {
            "global_user_id": global_user.id,
            "tenant_patient_user_mapping_id": tenant_patient_user_mapping.id,
            "phone_number": phone_number,
            "country_code": country_code,
        }


def test_send_otp_success(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    valid_payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )

    with patch(
        "services.user_patient_service.UserPatientService.send_otp_to_phone",
        return_value=True,
    ):
        response = client.post("/login/patient/send-otp", json=valid_payload)
    assert response.status_code == HTTPStatus.OK.value

    result = response.get_json()
    assert result["message"] == CustomMessageCode.OTP_SENT_SUCCESS.title


@pytest.mark.parametrize(
    "data_idx, invalid_payload, expected_status_code, expected_message",
    [
        (
            0,
            # Invalid payload, missing request body
            {},
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
        ),
        (
            1,
            # Invalid payload, missing request bodys
            None,
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
        ),
    ],
)
def test_send_otp_invalid_request(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    data_idx,
    invalid_payload,
    expected_status_code,
    expected_message,
):
    with patch(
        "services.user_patient_service.UserPatientService.send_otp_to_phone",
        return_value=True,
    ):
        response = client.post("/login/patient/send-otp", json=invalid_payload)

    assert response.status_code == expected_status_code

    result = response.get_json()
    assert result["message"] == expected_message


def test_send_otp_unknown_phone_number(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )

    # Unknown phone number
    invalid_payload = {**payload, "phone_number": str(int(time.time() * 1e6))[-11:]}

    with patch(
        "services.user_patient_service.UserPatientService.send_otp_to_phone",
        return_value=True,
    ):
        response = client.post("/login/patient/send-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.USER_NOT_FOUND.title


def test_send_otp_invalid_country_code(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )

    # Invalid country code -> invalid phone number
    invalid_payload = {**payload, "country_code": "ABC"}

    with patch(
        "services.user_patient_service.UserPatientService.send_otp_to_phone",
        return_value=True,
    ):
        response = client.post("/login/patient/send-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INVALID_PHONE_NUMBER.title


def test_send_otp_invalid_client_id(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
):
    payload = get_payload({**setup_data})

    # Invalid client id
    invalid_payload = {**payload, "client_id": gen_salt(24)}

    with patch(
        "services.user_patient_service.UserPatientService.send_otp_to_phone",
        return_value=True,
    ):
        response = client.post("/login/patient/send-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title


def test_send_otp_invalid_user_mapping(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    # Prepare payload and create only the internal client + global user (no tenant mapping)
    data = setup_data
    payload = get_payload({"client_id": internal_oauth_client["client_id"], **data})

    with sync_central_db_session_object.begin():
        unittest_remove_tenant_patient_user_mapping_by_ids(
            sync_central_db_session_object,
            [data["tenant_patient_user_mapping_id"]],
        )

    with patch(
        "services.user_patient_service.UserPatientService.send_otp_to_phone",
        return_value=True,
    ):
        response = client.post("/login/patient/send-otp", json=payload)

    # Expect failure because there is NO TenantPatientUserMapping for this user
    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INVALID_USER.title
