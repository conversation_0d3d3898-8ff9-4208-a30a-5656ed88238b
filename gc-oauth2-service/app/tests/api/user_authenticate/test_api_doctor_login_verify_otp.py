import random
import string
from contextlib import ExitStack
from unittest.mock import patch

import pytest
from core.constants import (
    DOCTOR_LOGIN_REDIS_PREFIX,
    OTP_LENGTH,
    OTP_SEND_WINDOW_MINUTES,
)
from core.messages import CustomMessageCode
from sqlalchemy.exc import DBAPIError
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor_response_info
from tests.helpers.redis_mock import mock_redis_client

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


def get_valid_payload(overrides=None):
    payload = {}
    if overrides:
        payload.update(overrides)
    return payload


def get_headers(tenant_slug, valid_headers=None):
    headers = {"X-Tenant-Slug": tenant_slug}
    if valid_headers:
        headers.update(valid_headers)
    return headers


def set_otp_redis_mock(tenant_uuid, user_id):
    aes_gcm = AesGCMRotation(None)
    hased_user_id = aes_gcm.sha256_hash(user_id)
    otp = "".join(random.choices(string.digits, k=OTP_LENGTH))
    otp_key = f"{DOCTOR_LOGIN_REDIS_PREFIX}_{tenant_uuid}_{hased_user_id}"
    mock_redis_client.setex(name=otp_key, time=OTP_SEND_WINDOW_MINUTES * 60, value=otp)
    return otp


def delete_otp_redis_mock(tenant_uuid, user_id):
    aes_gcm = AesGCMRotation(None)
    hased_user_id = aes_gcm.sha256_hash(user_id)
    otp_key = f"{DOCTOR_LOGIN_REDIS_PREFIX}_{tenant_uuid}_{hased_user_id}"
    mock_redis_client.delete(otp_key)


@pytest.fixture(scope="class")
def setup_data(
    sync_tenant_db_session_object,
    tenant_uuid,
):
    with sync_tenant_db_session_object.begin():
        doctor = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
    sync_tenant_db_session_object.commit()
    return doctor


def test_api_doctor_otp_login_pass(
    client, tenant_slug, tenant_uuid, internal_oauth_client, setup_data
):
    otp = set_otp_redis_mock(tenant_uuid, setup_data["doctor_id"])
    payload = get_valid_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            "user_id": setup_data["doctor_id"],
            "otp": otp,
        }
    )
    headers = get_headers(tenant_slug)

    with patch(
        "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = client.post(
            "/login/doctor/verify-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == 200
    result = response.json
    assert result["success"] is True
    assert "access_token" in result["data"]
    assert "refresh_token" in result["data"]
    assert "tenant_uuid" in result["data"]
    assert "expires_in" in result["data"]
    assert "token_type" in result["data"]


@pytest.mark.parametrize(
    "payload_func, status_code, result_flag, expected_message, expected_code",
    [
        (
            lambda tenant_uuid, doctor, client: {
                "client_id": client["client_id"],
                "user_id": doctor["doctor_id"],
                "otp": set_otp_redis_mock(tenant_uuid, doctor["doctor_id"]),
            },
            200,
            True,
            None,
            None,
        ),
        (
            lambda tenant_uuid, doctor, client: {
                "client_id": client["client_id"],
                "user_id": doctor["doctor_id"],
                "otp": "invalid_otp"
                + set_otp_redis_mock(tenant_uuid, doctor["doctor_id"]),
            },
            401,
            False,
            CustomMessageCode.OTP_INCORRECT.title,
            CustomMessageCode.OTP_INCORRECT.code,
        ),
        (
            lambda tenant_uuid, doctor, client: {
                "client_id": client["client_id"],
                "user_id": 99999999,
                "otp": set_otp_redis_mock(tenant_uuid, doctor["doctor_id"]),
            },
            401,
            False,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
        ),
        (
            lambda tenant_uuid, doctor, client: {
                "client_id": client["client_id"],
                "user_id": doctor["doctor_id"],
                "otp": "not_set_otp",
            },
            401,
            False,
            CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title,
            CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code,
        ),
        (
            lambda tenant_uuid, doctor, client: {
                "client_id": "invalid_client_id",
                "user_id": doctor["doctor_id"],
                "otp": set_otp_redis_mock(tenant_uuid, doctor["doctor_id"]),
            },
            401,
            False,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
        ),
        (
            lambda tenant_uuid, doctor, client: {
                "invalid_key": "invalid_value",
            },
            400,
            False,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
        ),
    ],
)
def test_api_doctor_otp_login_fail_invalid_otp(
    client,
    tenant_slug,
    tenant_uuid,
    internal_oauth_client,
    setup_data,
    payload_func,
    status_code,
    result_flag,
    expected_message,
    expected_code,
):
    payload = get_valid_payload(
        payload_func(
            tenant_uuid,
            setup_data,
            internal_oauth_client,
        )
    )
    headers = get_headers(tenant_slug)

    with patch(
        "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = client.post(
            "/login/doctor/verify-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] == result_flag
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    delete_otp_redis_mock(tenant_uuid, setup_data["doctor_id"])


@pytest.mark.parametrize(
    "status_code, expected_message, expected_code, patchs_func, call_counts",
    [
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "services.doctor_auth_service.DoctorAuthService.verify_otp",
                    side_effect=Exception("Unknown error occurred"),
                ),
            ],
            [1],
        ),
        (
            400,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
            [
                patch(
                    "services.doctor_auth_service.DoctorAuthService.verify_otp",
                    side_effect=ValueError("Value error occurred"),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
            [
                patch(
                    "services.doctor_auth_service.DoctorAuthService.verify_otp",
                    side_effect=CustomValueError(
                        CustomMessageCode.INVALID_REQUEST_DATA.title,
                        CustomMessageCode.INVALID_REQUEST_DATA.code,
                    ),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [3],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "services.doctor_auth_service.DoctorAuthService.get_internal_oauth_client",
                    side_effect=Exception("Internal function exception!"),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.doctor_auth_service.DoctorAuthService.create_access_token",
                    side_effect=Exception("Create token function exception!"),
                ),
            ],
            [1, 1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.auth_service.OAuth2ClientService.dynamic_save_token_internal_client_sync",
                    side_effect=Exception("Save token function exception!"),
                ),
            ],
            [1, 1],
        ),
    ],
)
def test_api_doctor_otp_login_case_exeption(
    tenant_uuid,
    client,
    tenant_slug,
    internal_oauth_client,
    setup_data,
    status_code,
    expected_message,
    expected_code,
    patchs_func,
    call_counts,
):
    otp = set_otp_redis_mock(tenant_uuid, setup_data["doctor_id"])
    payload = get_valid_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            "user_id": setup_data["doctor_id"],
            "otp": otp,
        }
    )
    headers = get_headers(tenant_slug)

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patchs_func:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/login/doctor/verify-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
    delete_otp_redis_mock(tenant_uuid, setup_data["doctor_id"])
