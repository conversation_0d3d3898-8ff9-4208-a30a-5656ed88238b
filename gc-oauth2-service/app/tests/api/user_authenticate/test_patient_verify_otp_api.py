import time
from http import HTTPStatus
from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from tests.helpers.insert_data.insert_global_user import (
    unittest_create_global_user,
    unittest_create_tenant_patient_user_mapping,
    unittest_remove_tenant_patient_user_mapping_by_ids,
)
from werkzeug.security import gen_salt


def get_payload(overrides=None):
    payload = {
        "client_id": gen_salt(24),
        "phone_number": str(int(time.time() * 1e6))[-11:],
        "country_code": "+81",
        "otp": "123456",
    }
    if overrides:
        payload.update(overrides)
    return payload


@pytest.fixture(scope="class")
def setup_data(sync_central_db_session_object, tenant_uuid):
    phone_number = str(int(time.time() * 1e6))[-11:]
    country_code = "+81"

    with sync_central_db_session_object.begin():
        global_user = unittest_create_global_user(
            sync_central_db_session_object,
            phone_number=phone_number,
            country_code=country_code,
            is_active=True,
        )

        tenant_patient_user_mapping = unittest_create_tenant_patient_user_mapping(
            sync_central_db_session_object,
            global_user_id=global_user.id,
            tenant_uuid=tenant_uuid,
        )

        return {
            "global_user_id": global_user.id,
            "tenant_patient_user_mapping_id": tenant_patient_user_mapping.id,
            "phone_number": phone_number,
            "country_code": country_code,
        }


def test_verify_otp_success(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )

    # Only mock Twilio verification to succeed
    with patch(
        "services.user_patient_service.TwilioService.get_client"
    ) as mock_twilio_get_client:
        mock_twilio_client = mock_twilio_get_client.return_value
        mock_twilio_client.verify_sms_otp.return_value = True

        response = client.post("/login/patient/verify-otp", json=payload)

    assert response.status_code == HTTPStatus.OK.value
    body = response.get_json()
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]


@pytest.mark.parametrize(
    "data_idx, invalid_payload, expected_status_code, expected_message",
    [
        (
            0,
            # Invalid payload, missing request body
            {},
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
        ),
        (
            1,
            # Invalid payload, missing request body
            None,
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
        ),
    ],
)
def test_verify_otp_invalid_request(
    client,
    sync_central_db_session_object,
    tenant_uuid,
    setup_data,
    data_idx,
    invalid_payload,
    expected_status_code,
    expected_message,
):
    # For these cases, validation fails before OTP verification, so no need to mock Twilio
    response = client.post("/login/patient/verify-otp", json=invalid_payload)

    assert response.status_code == expected_status_code
    result = response.get_json()
    assert result["message"] == expected_message


def test_verify_otp_unknown_phone_number(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )

    # Unknown phone number
    invalid_payload = {**payload, "phone_number": str(int(time.time() * 1e6))[-11:]}
    response = client.post("/login/patient/verify-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.USER_NOT_FOUND.title


def test_verify_otp_invalid_country_code(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )

    # Invalid country code -> invalid phone number
    invalid_payload = {**payload, "country_code": "ABC"}
    response = client.post("/login/patient/verify-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INVALID_PHONE_NUMBER.title


def test_verify_otp_invalid_client_id(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload({**setup_data})

    # Invalid client id
    invalid_payload = {**payload, "client_id": gen_salt(24)}
    response = client.post("/login/patient/verify-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title


def test_verify_otp_wrong_otp(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {"client_id": internal_oauth_client["client_id"], **setup_data}
    )
    invalid_payload = {**payload, "otp": "000000"}  # wrong OTP

    # Force OTP verification to fail
    with patch(
        "services.user_patient_service.TwilioService.get_client"
    ) as mock_twilio_get_client:
        mock_twilio_client = mock_twilio_get_client.return_value
        mock_twilio_client.verify_sms_otp.return_value = False

        response = client.post("/login/patient/verify-otp", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.OTP_VERIFICATION_FAILED.title


def test_verify_otp_invalid_user_mapping(
    client,
    sync_central_db_session_object,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    # Prepare payload and create only the internal client + global user (no tenant mapping)
    data = setup_data
    payload = get_payload({"client_id": internal_oauth_client["client_id"], **data})
    with sync_central_db_session_object.begin():
        unittest_remove_tenant_patient_user_mapping_by_ids(
            sync_central_db_session_object,
            [data["tenant_patient_user_mapping_id"]],
        )

    response = client.post("/login/patient/verify-otp", json=payload)

    # Expect failure because there is NO TenantPatientUserMapping for this user
    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INVALID_USER.title
