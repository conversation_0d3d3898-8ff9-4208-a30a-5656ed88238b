import time
from contextlib import ExitStack
from types import SimpleNamespace
from unittest.mock import patch
from uuid import uuid4

import pytest
from configuration.settings import configuration
from core.messages import CustomMessageCode
from sqlalchemy import select, text
from sqlalchemy.exc import DBAPIError
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor_response_info

from gc_dentist_shared.central_models.oauth2_client import OAuth2Client
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


def get_payload(overrides=None):
    payload = {}
    if overrides:
        payload.update(overrides)
    return payload


def dump_token_rawsql():
    return text(
        """INSERT INTO oauth_tokens
            (
                token_uuid,
                client_id,
                token_type,
                access_token,
                refresh_token,
                scope,
                issued_at,
                refresh_token_expires_at,
                access_token_revoked_at,
                refresh_token_revoked_at,
                expires_in,
                clinic_doctor_id,
                tenant_uuid
            )
            VALUES
            (
                :token_uuid,
                :client_id,
                'Bearer',
                :access_token,
                :refresh_token,
                'profile+mail',
                EXTRACT(EPOCH FROM NOW()),
                :refresh_token_expires_at,
                0,
                0,
                :expires_in,
                :clinic_doctor_id,
                :tenant_uuid
            )
        """
    )


def create_token(user_id, internal_client, token_expire):
    user = SimpleNamespace(
        id=user_id,
    )
    data_token = encode_jwt_token(
        configuration.RSA_KEY_MANIFEST.get("current_kid"),
        configuration.JWT_RSA_PRIVATE_KEY,
        configuration.JWT_ALGORITHM,
        token_expire,
        "",
        internal_client,
        user,
        " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
    )
    return {
        "access_token": data_token.get("access_token"),
        "refresh_token": data_token.get("refresh_token"),
        "token_type": data_token.get("token_type"),
        "expires_in": data_token.get("expires_in"),
    }


def create_token_to_central_db(
    sync_central_db_session_object,
    client_id,
    doctor,
    tenant_uuid,
    internal_client,
    token_expire=15,
):
    query = dump_token_rawsql()
    token = {
        "token_uuid": str(uuid4()),
        "client_id": client_id,
        "refresh_token_expires_at": int(time.time()) + (3600 * 24 * 30),
        "clinic_doctor_id": doctor["doctor_id"],
        "tenant_uuid": tenant_uuid,
        **create_token(
            user_id=doctor["doctor_id"],
            internal_client=internal_client,
            token_expire=token_expire,
        ),
    }
    sync_central_db_session_object.execute(query, token)
    return token


@pytest.fixture(scope="class")
def setup_data(
    sync_tenant_db_session_object,
    sync_central_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    with sync_tenant_db_session_object.begin():
        doctor_1 = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
        doctor_2 = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
        doctor_3 = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
    sync_tenant_db_session_object.commit()

    with sync_central_db_session_object.begin():
        result = sync_central_db_session_object.execute(
            select(OAuth2Client).where(
                OAuth2Client.client_id == internal_oauth_client["client_id"],
                OAuth2Client.is_active.is_(True),
            )
        )
        internal_client = result.scalar_one_or_none()

        token_1 = create_token_to_central_db(
            sync_central_db_session_object,
            internal_oauth_client["client_id"],
            doctor_1,
            tenant_uuid,
            internal_client,
        )

        token_2 = create_token_to_central_db(
            sync_central_db_session_object,
            internal_oauth_client["client_id"],
            doctor_2,
            tenant_uuid,
            internal_client,
        )

        token_expire = create_token_to_central_db(
            sync_central_db_session_object,
            internal_oauth_client["client_id"],
            doctor_3,
            tenant_uuid,
            internal_client,
            token_expire=-5,
        )
    sync_central_db_session_object.commit()

    return {
        "doctor_1": doctor_1,
        "doctor_2": doctor_2,
        "token_1": token_1,
        "token_2": token_2,
        "token_expire": token_expire,
    }


@pytest.mark.parametrize(
    "payload_func, header_func, status_code, result_flag, expected_message, expected_code",
    [
        (
            lambda client: {
                "client_id": client["client_id"],
            },
            lambda setup_data: {
                "Authorization": f"Bearer {setup_data['token_1']['access_token']}",
            },
            200,
            True,
            None,
            None,
        ),
        (
            lambda client: {
                "client_id": client["client_id"],
            },
            lambda setup_data: {
                "Authorization": f"Bearer {setup_data['token_2']['access_token']}",
            },
            200,
            True,
            None,
            None,
        ),
        (
            lambda client: {
                "client_id": client["client_id"],
            },
            lambda setup_data: {
                "Authorization": f"Bearer not_found_token_{str(uuid4())}",
            },
            401,
            False,
            CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.title,
            CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.code,
        ),
        (
            lambda client: {},
            lambda setup_data: {
                "Authorization": f"Bearer {setup_data['token_2']['access_token']}",
            },
            400,
            False,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
        ),
        (
            lambda client: {
                "client_id": client["client_id"],
            },
            lambda setup_data: {},
            401,
            False,
            CustomMessageCode.INVALID_AUTHORIZATION_HEADER.title,
            CustomMessageCode.INVALID_AUTHORIZATION_HEADER.code,
        ),
        (
            lambda client: {
                "client_id": "invalid_client_id",
            },
            lambda setup_data: {
                "Authorization": f"Bearer {setup_data['token_2']['access_token']}",
            },
            401,
            False,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
        ),
        (
            lambda client: {
                "client_id": client["client_id"],
            },
            lambda setup_data: {
                "Authorization": f"Bearer {setup_data['token_expire']['access_token']}",
            },
            401,
            False,
            CustomMessageCode.INVALID_TOKEN.title,
            CustomMessageCode.INVALID_TOKEN.code,
        ),
    ],
)
def test_api_logout_case(
    client,
    internal_oauth_client,
    setup_data,
    payload_func,
    header_func,
    status_code,
    result_flag,
    expected_message,
    expected_code,
):
    payload = get_payload(payload_func(internal_oauth_client))
    headers = header_func(setup_data)
    response = client.post(
        "/logout",
        json=payload,
        headers=headers,
    )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] == result_flag
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code


@pytest.mark.parametrize(
    "status_code, result_flag, expected_message, expected_code, patchs_func, call_counts",
    [
        (
            401,
            False,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "services.oauth2_service.OAuth2Service.logout",
                    side_effect=Exception("Exception logout function!"),
                )
            ],
            [1],
        ),
        (
            400,
            False,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
            [
                patch(
                    "services.oauth2_service.OAuth2Service.logout",
                    side_effect=ValueError("Exception logout function!"),
                )
            ],
            [1],
        ),
        (
            401,
            False,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
            [
                patch(
                    "services.oauth2_service.OAuth2Service.logout",
                    side_effect=CustomValueError(
                        message=CustomMessageCode.INVALID_REQUEST_DATA.title,
                        message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
                    ),
                )
            ],
            [1],
        ),
        (
            401,
            False,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session",
                    side_effect=DBAPIError("Database error!", None, None),
                )
            ],
            [3],
        ),
    ],
)
def test_api_logout_exception(
    client,
    internal_oauth_client,
    setup_data,
    status_code,
    result_flag,
    expected_message,
    expected_code,
    patchs_func,
    call_counts,
):
    payload = {
        "client_id": internal_oauth_client["client_id"],
    }
    headers = {
        "Authorization": f"Bearer {setup_data['token_2']['access_token']}",
    }

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patchs_func:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/logout",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] == result_flag
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
