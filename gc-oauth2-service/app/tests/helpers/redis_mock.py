class MockRedisClient:
    def __init__(self):
        self.data_redis = {}

    def get(self, key):
        return self.data_redis.get(key)

    def set(self, name, value):
        self.data_redis[name] = value

    def setex(self, name, time, value):
        self.data_redis[name] = value

    def delete(self, key):
        if key in self.data_redis:
            del self.data_redis[key]


mock_redis_client = MockRedisClient()
