import time
from datetime import datetime, timezone

from configuration.settings import configuration
from sqlalchemy import delete, select

from gc_dentist_shared.central_models import OAuth2Client, OAuth2Token
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token


def unittest_create_token(db_session, user, client_id: str) -> dict:
    result = db_session.execute(
        select(OAuth2Client).where(
            OAuth2Client.client_id == client_id, OAuth2Client.is_active.is_(True)
        )
    )
    internal_client = result.scalars().first()
    token = encode_jwt_token(
        configuration.RSA_KEY_MANIFEST.get("current_kid"),
        configuration.JWT_RSA_PRIVATE_KEY,
        configuration.JWT_ALGORITHM,
        configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
        "",
        internal_client,
        user,
        " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
    )
    return token


def unittest_dynamic_create_oauth_token(db_session, user, client_id: str, **kwargs):
    token = unittest_create_token(db_session, user, client_id)

    condition_list = {
        OAuth2Token.global_user_id: kwargs.get("global_user_id"),
        OAuth2Token.clinic_patient_id: kwargs.get("clinic_patient_id"),
        OAuth2Token.clinic_doctor_id: kwargs.get("clinic_doctor_id"),
        OAuth2Token.tenant_uuid: kwargs.get("tenant_uuid"),
    }
    conditions = [
        column == value for column, value in condition_list.items() if value is not None
    ]

    delete_stmt = delete(OAuth2Token).where(*conditions)
    db_session.execute(delete_stmt)

    new_token = OAuth2Token(
        client_id=client_id,
        token_type=token.get("token_type"),
        access_token=token.get("access_token"),
        refresh_token=token.get("refresh_token"),
        scope=token.get("scope"),
        issued_at=int(datetime.now(timezone.utc).timestamp()),
        refresh_token_expires_at=int(time.time())
        + (configuration.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60),
        expires_in=token.get("expires_in"),
        **kwargs,
    )
    db_session.add(new_token)
    db_session.flush()
    db_session.refresh(new_token)

    return new_token
