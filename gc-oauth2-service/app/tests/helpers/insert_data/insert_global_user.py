import time
from typing import Optional

from configuration.settings import configuration
from sqlalchemy import delete
from werkzeug.security import gen_salt

from gc_dentist_shared.central_models import GlobalUser, TenantPatientUserMapping
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.strings import format_phone_number


def unittest_create_global_user(
    db_session,
    *,
    phone_number: str = str(int(time.time() * 1e6))[-11:],
    country_code: str = "+81",
    is_active: bool = True,
    extra_data: Optional[dict] = None,
) -> GlobalUser:
    normalized_phone = format_phone_number(
        phone=phone_number, country_code=country_code
    )

    aes_gcm = AesGCMRotation(configuration)
    username_hash = aes_gcm.sha256_hash(normalized_phone)

    global_user = GlobalUser(
        username=gen_salt(24),
        username_hash=username_hash,
        is_active=is_active,
        extra_data=extra_data or {},
    )
    db_session.add(global_user)
    db_session.flush()
    db_session.refresh(global_user)
    return global_user


def unittest_create_tenant_patient_user_mapping(
    db_session,
    *,
    global_user_id: int,
    tenant_uuid,
    patient_user_id: int = 1,
    patient_no: Optional[str] = None,
    is_active: bool = True,
) -> TenantPatientUserMapping:
    tenant_patient_user_mapping = TenantPatientUserMapping(
        global_user_id=global_user_id,
        tenant_uuid=tenant_uuid,
        patient_user_id=patient_user_id,
        patient_no=patient_no,
        is_active=is_active,
    )
    db_session.add(tenant_patient_user_mapping)
    db_session.flush()
    db_session.refresh(tenant_patient_user_mapping)
    return tenant_patient_user_mapping


def unittest_remove_tenant_patient_user_mapping_by_ids(db_session, ids: list[int]):
    stmt = delete(TenantPatientUserMapping).where(TenantPatientUserMapping.id.in_(ids))
    db_session.execute(stmt)
