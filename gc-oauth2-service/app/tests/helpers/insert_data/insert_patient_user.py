from sqlalchemy import func, select
from werkzeug.security import gen_salt

from gc_dentist_shared.tenant_models import Patient<PERSON><PERSON>


def unittest_auto_generate_patient_no(db_session) -> str:
    query = select(func.nextval("patient_users_patient_no_seq"))
    result = db_session.execute(query)
    patient_no = result.scalar_one_or_none()
    return str(patient_no).zfill(11)


def unittest_create_patient_user(
    db_session,
    *,
    username: str = gen_salt(24),
    password: str = gen_salt(24),
    tenant_uuid: str,
    custom_user_fields=None,
) -> PatientUser:
    patient_no = unittest_auto_generate_patient_no(db_session)
    user_data = {
        "username": username,
        "patient_no": patient_no,
        "status": True,
    }
    if custom_user_fields:
        user_data.update(custom_user_fields)

    patient_user = PatientUser(**user_data)
    patient_user.set_password(password, str(tenant_uuid))
    db_session.add(patient_user)
    db_session.flush()
    db_session.refresh(patient_user)
    return patient_user
