import time
import uuid

from sqlalchemy import delete, select
from tests.helpers.enums.enum_base import UnittestGender

from gc_dentist_shared.tenant_models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


def unittest_insert_doctor(
    db_session, tenant_uuid, custom_user_fields=None, custom_profile_fields=None
):
    phone_number = str(int(time.time() * 1e6))[-11:]
    email = str(uuid.uuid4()) + "@example.com"
    password = str(uuid.uuid4())
    user_data = {
        "username": email,
        "status": True,
        "required_change_password": True,
    }
    if custom_user_fields:
        user_data.update(custom_user_fields)

    new_user = Doctor<PERSON>ser(**user_data)
    new_user.set_password(password, tenant_uuid)
    db_session.add(new_user)
    db_session.flush()
    db_session.refresh(new_user)

    profile_data = {
        "doctor_user_id": new_user.id,
        "last_name": str(uuid.uuid4()),
        "first_name": str(uuid.uuid4()),
        "last_name_kana": str(uuid.uuid4()),
        "first_name_kana": str(uuid.uuid4()),
        "email": email,
        "gender": UnittestGender.MALE,
        "phone": phone_number,
        "date_of_birth": "1990-01-01",
        "order_index": 0,
        "prefecture_id": 1,
        "postal_code": "0600000",
        "country_code": "+81",
    }
    if custom_profile_fields:
        profile_data.update(custom_profile_fields)

    new_profile = DoctorProfile(**profile_data)
    db_session.add(new_profile)
    db_session.flush()
    db_session.refresh(new_profile)

    return new_user


def unittest_insert_doctor_response_info(
    db_session, tenant_uuid, custom_user_fields=None, custom_profile_fields=None
):
    phone_number = str(int(time.time() * 1e6))[-11:]
    email = str(uuid.uuid4()).replace("-", "") + "@example.com"
    password = str(uuid.uuid4())
    user_data = {
        "username": email,
        "password": password,
        "status": True,
        "required_change_password": True,
    }
    if custom_user_fields:
        user_data.update(custom_user_fields)

    new_user = DoctorUser(**user_data)
    new_user.set_password(password, tenant_uuid)
    db_session.add(new_user)
    db_session.flush()
    db_session.refresh(new_user)

    profile_data = {
        "doctor_user_id": new_user.id,
        "last_name": str(uuid.uuid4()),
        "first_name": str(uuid.uuid4()),
        "last_name_kana": str(uuid.uuid4()),
        "first_name_kana": str(uuid.uuid4()),
        "email": email,
        "gender": UnittestGender.MALE,
        "phone": phone_number,
        "date_of_birth": "1990/01/01",
        "order_index": 0,
        "prefecture_id": 1,
        "postal_code": "0600000",
        "country_code": "+81",
    }
    if custom_profile_fields:
        profile_data.update(custom_profile_fields)

    new_profile = DoctorProfile(**profile_data)
    db_session.add(new_profile)
    db_session.flush()
    db_session.refresh(new_profile)

    return {
        "doctor_id": new_user.id,
        "doctor_user": user_data,
        "doctor_profile": profile_data,
    }


def unittest_remove_doctor_by_ids(
    db_session,
    doctor_ids,
):
    if not doctor_ids:
        return

    stmt = delete(DoctorProfile).where(DoctorProfile.doctor_user_id.in_(doctor_ids))
    db_session.execute(stmt)

    stmt = delete(DoctorUser).where(DoctorUser.id.in_(doctor_ids))
    db_session.execute(stmt)


def unittest_get_doctor_created_by_id(db_session, id):
    stmt = select(DoctorProfile).where(DoctorProfile.doctor_user_id == id)
    result = db_session.execute(stmt)
    return result.scalar_one_or_none()
