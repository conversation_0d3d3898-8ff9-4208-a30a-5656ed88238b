import contextvars

_tenant_db_name_ctx = contextvars.ContextVar("tenant_db_name", default=None)


def set_current_db_name(db_name: str):
    """
    set value database name to context
    """
    return _tenant_db_name_ctx.set(db_name)


def reset_current_db_name(token):
    """
    Reset context
    """
    _tenant_db_name_ctx.reset(token)


def get_current_db_name() -> str:
    """
    get database name from context
    """
    db_name = _tenant_db_name_ctx.get()
    if db_name is None:
        raise RuntimeError("No tenant database name set in context.")
    return db_name
