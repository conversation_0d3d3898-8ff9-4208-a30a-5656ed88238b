import logging
import sys


class Logger(logging.Logger):
    def __init__(self, name, level=logging.NOTSET):
        super().__init__(name, level)
        self.extra_info = None

    def _log_with_extra(self, level, msg, *args, **kwargs):
        kwargs["extra"] = self.extra_info

        super()._log(level, f"{self.extra_info} {msg}", args, **kwargs)

    def info(self, msg, *args, **kwargs):
        self._log_with_extra(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg, *args, **kwargs):
        self._log_with_extra(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg, *args, **kwargs):
        self._log_with_extra(logging.ERROR, msg, *args, **kwargs)

    def critical(self, msg, *args, **kwargs):
        self._log_with_extra(logging.CRITICAL, msg, *args, **kwargs)

    def success(self, msg, *args, **kwargs):
        self._log_with_extra(logging.INFO, msg, *args, **kwargs)


logging.setLoggerClass(Logger)
log = Logger("PaymentGateway")
log.setLevel(logging.INFO)

stdout_handler = logging.StreamHandler(sys.stdout)
stdout_handler.setLevel(logging.INFO)

stderror_handler = logging.StreamHandler(sys.stderr)
stderror_handler.setLevel(logging.ERROR)

formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
stdout_handler.setFormatter(formatter)
stderror_handler.setFormatter(formatter)

log.addHandler(stdout_handler)
log.addHandler(stderror_handler)
