import logging

from configuration.context.language_context import set_current_language
from flask import request

from gc_dentist_shared.core.logger.config import log

MAPPING_LANG_REGION = {
    "en": "en-US",
    "ja": "ja-JP",
}

logger = logging.getLogger(__name__)


class LanguageMiddleware:
    def __init__(self, app):
        self.app = app
        self.register()

    def register(self):
        self.app.before_request(self.before_request)

    def before_request(self):
        try:
            lang_code: str | None = request.accept_languages.best_match(
                MAPPING_LANG_REGION.keys()
            )
            set_current_language(lang_code)

            # Implement I18N

        except Exception as exc:
            log.error(f"Unhandled exception: {exc}")
            import traceback

            log.error(traceback.format_exc())
