from typing import Any, Optional

from flask import jsonify


class ApiResponse:
    @staticmethod
    def success(
        data: Any = None,
        message: Optional[str] = None,
        message_code: Optional[int] = None,
        status_code: int = 200,
    ) -> jsonify:
        return (
            jsonify(
                {
                    "success": True,
                    "data": data,
                    "message": message,
                    "messageCode": message_code,
                    "messageErrors": None,
                }
            ),
            status_code,
        )

    @staticmethod
    def error(
        message: Optional[str] = None,
        message_code: Optional[int] = None,
        message_errors: Optional[list[str]] = None,
        status_code: int = 400,
        data: Any = None,
    ) -> jsonify:
        return (
            jsonify(
                {
                    "success": False,
                    "data": data,
                    "message": message,
                    "messageCode": message_code,
                    "messageErrors": message_errors,
                }
            ),
            status_code,
        )
