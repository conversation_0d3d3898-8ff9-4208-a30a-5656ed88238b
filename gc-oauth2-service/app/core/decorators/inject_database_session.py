from functools import wraps

from db.db_connection import CentralDatabase


def inject_db_session(f):
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        sesion_marker = CentralDatabase.get_sessionmaker()
        async with sesion_marker() as session:
            kwargs["db_session"] = session
            result = await f(*args, **kwargs)
        return result

    return decorated_function


# @app.route("/items")
# @inject_db_session
# async def get_items(db_session):
#     print(f"DB session injected: {db_session}")
#     return jsonify([{"id": 1, "name": "An item"}])
