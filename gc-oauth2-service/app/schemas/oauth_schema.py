from core.messages import CustomMessageCode
from enums.oauth_enum import (
    GrantType,
    ResponseType,
    ScopeSchema,
    TokenEndpointAuthMethod,
)
from pydantic import BaseModel, Field, HttpUrl, field_validator

from gc_dentist_shared.core.logger.config import log


class CreateOauthClient(BaseModel):
    client_name: str = Field(..., description="Name of the OAuth client")
    client_uri: HttpUrl = Field(..., description="URI of the OAuth client")
    grant_types: list[GrantType] = Field(..., description="List of allowed grant types")
    redirect_uris: list[HttpUrl] = Field(..., description="List of redirect URIs")
    response_types: list[ResponseType] = Field(
        ..., description="List of allowed response types"
    )
    scope: str = Field(..., description="Scope of the OAuth client")
    token_endpoint_auth_method: TokenEndpointAuthMethod = Field(
        ..., description="Token endpoint authentication method"
    )

    @field_validator("scope")
    @classmethod
    def validate_scope(cls, value: str) -> str:
        if not value:
            raise ValueError(CustomMessageCode.INVALID_SCOPES.value)
        try:
            scopes = value.split("+")
            for scope in scopes:
                if scope not in ScopeSchema.get_member_values():
                    raise ValueError(CustomMessageCode.INVALID_SCOPES.value)
        except Exception as e:
            log.error(f"📛 Error validating scope: {e}")
            raise ValueError(CustomMessageCode.INVALID_SCOPES.value)
        return value

    @field_validator("client_name")
    @classmethod
    def validate_client_name(cls, value: str) -> str:
        if not value or not value.strip():
            raise ValueError(CustomMessageCode.INVALID_CLIENT_NAME.value)
        return value
