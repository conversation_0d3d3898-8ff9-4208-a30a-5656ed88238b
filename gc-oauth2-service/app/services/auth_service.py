import time
from datetime import datetime, timezone

from configuration.settings import configuration
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from schemas.oauth_schema import CreateOauthClient
from sqlalchemy import delete, select
from sqlalchemy.exc import <PERSON><PERSON><PERSON>rror, OperationalError
from werkzeug.security import gen_salt

from gc_dentist_shared.central_models import OAuth2Client, OAuth2Token
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.logger.config import log


class OAuth2ClientService:
    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="dynamic_save_token_internal_client_sync",
    )
    def dynamic_save_token_internal_client_sync(token: dict, client_id: str, **kwargs):
        db_session = CentralDatabase.get_sync_db_session_instance()
        try:
            with db_session.begin():
                condition_list = {
                    OAuth2Token.global_user_id: kwargs.get("global_user_id"),
                    OAuth2Token.clinic_patient_id: kwargs.get("clinic_patient_id"),
                    OAuth2Token.clinic_doctor_id: kwargs.get("clinic_doctor_id"),
                    OAuth2Token.tenant_uuid: kwargs.get("tenant_uuid"),
                }
                conditions = [
                    column == value
                    for column, value in condition_list.items()
                    if value is not None
                ]
                if not conditions:
                    raise ValueError(CustomMessageCode.INVALID_CONDITION.title)

                delete_stmt = delete(OAuth2Token).where(*conditions)
                db_session.execute(delete_stmt)

                new_token = OAuth2Token(
                    client_id=client_id,
                    token_type=token.get("token_type"),
                    access_token=token.get("access_token"),
                    refresh_token=token.get("refresh_token"),
                    scope=token.get("scope"),
                    issued_at=int(datetime.now(timezone.utc).timestamp()),
                    refresh_token_expires_at=int(time.time())
                    + (configuration.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60),
                    expires_in=token.get("expires_in"),
                    **kwargs,
                )
                db_session.add(new_token)
        except Exception as e:
            log.error(f"❌ Error saving token: {str(e)}")
            raise e
        finally:
            db_session.close()

    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_internal_client_by_client_id_sync",
    )
    def get_internal_client_by_client_id_sync(db_session, client_id: str):
        result = db_session.execute(
            select(OAuth2Client).where(
                OAuth2Client.client_id == client_id, OAuth2Client.is_active.is_(True)
            )
        )
        return result.scalar_one_or_none()

    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="create_oauth_client",
    )
    def create_oauth_client_sync(obj: CreateOauthClient) -> None:
        db_session = CentralDatabase.get_sync_db_session_instance()
        try:
            with db_session.begin():

                client_id = gen_salt(24)
                client_id_issued_at = int(time.time())
                client = OAuth2Client(
                    name=obj.client_name,
                    client_id=client_id,
                    client_id_issued_at=client_id_issued_at,
                )
                client.set_client_metadata(obj.model_dump(mode="json"))
                client.client_secret = gen_salt(48)

                db_session.add(client)
        except Exception as e:
            log.error(f"❌ Error creating OAuth client: {str(e)}")
            raise e
        finally:
            db_session.close()
