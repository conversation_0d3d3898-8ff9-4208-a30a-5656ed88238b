from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase, TenantDatabase
from schemas.auth_schema import (
    LoginObjectValid,
    LoginOTPRequest,
    LoginOTPVerifyRequest,
    LoginRequest,
    LoginUsernamePasswordValid,
    OAuthTokenInternalResponse,
)
from services.auth_service import OAuth2ClientService
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError

from gc_dentist_shared.central_models import (
    GlobalUser,
    TenantClinic,
    TenantPatientUserMapping,
)
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token
from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.common.twilio import TwilioService
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import PatientUser


class UserPatientService:
    def login_with_phone_otp(self, obj: LoginOTPRequest):
        """
        Login with phone number and country code.
        This method is used to authenticate a patient user using their phone number and country code.
        """
        valid_data = self.validate_data_login_otp(obj)
        self.send_otp_to_phone(valid_data.phone_number)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="validate_data_login_otp",
    )
    def validate_data_login_otp(self, obj: LoginOTPRequest) -> LoginObjectValid:
        """
        Validate the data for login OTP.
        This method checks if the phone number and country code are valid.
        """
        phone_number = format_phone_number(
            phone=obj.phone_number, country_code=obj.country_code
        )
        if not phone_number:
            log.error("❌ Invalid phone number format")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_PHONE_NUMBER.title,
                message_code=CustomMessageCode.INVALID_PHONE_NUMBER.code,
            )

        session = CentralDatabase.get_sync_db_session_instance()
        aes_gcm = AesGCMRotation(configuration)
        result = session.execute(
            select(GlobalUser).where(
                GlobalUser.username_hash == aes_gcm.sha256_hash(phone_number),
                GlobalUser.is_active.is_(True),
            )
        )
        user = result.scalar_one_or_none()
        if not user:
            log.error("❌ User not found for the given phone number")
            raise CustomValueError(
                message=CustomMessageCode.USER_NOT_FOUND.title,
                message_code=CustomMessageCode.USER_NOT_FOUND.code,
            )
        result_tenant_uuids = session.execute(
            select(TenantPatientUserMapping.tenant_uuid).where(
                TenantPatientUserMapping.global_user_id == user.id,
            )
        )
        tenant_uuids = result_tenant_uuids.scalars().all()
        if not tenant_uuids:
            log.error("❌ No tenant UUIDs found for the user")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_USER.title,
                message_code=CustomMessageCode.INVALID_USER.code,
            )
        internal_client = OAuth2ClientService.get_internal_client_by_client_id_sync(
            session, client_id=obj.client_id
        )
        if not internal_client:
            log.error("❌ Internal client not found")
            raise CustomValueError(
                message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
            )

        return LoginObjectValid(
            phone_number=phone_number,
            tenant_uuids=[str(tenant_uuid) for tenant_uuid in tenant_uuids],
            user=user,
            internal_client=internal_client,
        )

    def send_otp_to_phone(self, phone_number: str):
        """
        Send OTP to the phone number.
        This method is a placeholder for sending OTP to the phone number.
        """
        twilio_service = TwilioService.get_client(configuration)
        twilio_service.send_verification_otp(to=phone_number)
        return True

    def verify_phone_otp(self, obj: LoginOTPVerifyRequest) -> LoginObjectValid:
        """
        Verify the OTP sent to the phone number.
        This method checks if the OTP is valid for the given phone number.
        """
        valid_data = self.validate_data_login_otp(
            LoginOTPRequest(
                phone_number=obj.phone_number,
                country_code=obj.country_code,
                client_id=obj.client_id,
            )
        )

        twilio_service = TwilioService.get_client(configuration)
        verification = twilio_service.verify_sms_otp(
            to=valid_data.phone_number, code=obj.otp
        )
        if not verification:
            log.error("❌ OTP verification failed")
            raise CustomValueError(
                message=CustomMessageCode.OTP_VERIFICATION_FAILED.title,
                message_code=CustomMessageCode.OTP_VERIFICATION_FAILED.code,
            )
        return self.create_token_for_patient(valid_data)

    def create_token_for_patient(
        self, valid_data: LoginObjectValid
    ) -> OAuthTokenInternalResponse:
        user = valid_data.user
        internal_client = valid_data.internal_client
        data_token = encode_jwt_token(
            configuration.RSA_KEY_MANIFEST.get("current_kid"),
            configuration.JWT_RSA_PRIVATE_KEY,
            configuration.JWT_ALGORITHM,
            configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
            "",
            internal_client,
            user,
            " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
        )
        OAuth2ClientService.dynamic_save_token_internal_client_sync(
            data_token,
            internal_client.client_id,
            global_user_id=user.id,
        )
        return OAuthTokenInternalResponse(
            access_token=data_token.get("access_token"),
            refresh_token=data_token.get("refresh_token"),
            token_type=data_token.get("token_type"),
            expires_in=data_token.get("expires_in"),
            tenant_uuids=valid_data.tenant_uuids,
        )

    def login_patient_username_password(
        self, obj: LoginRequest
    ) -> OAuthTokenInternalResponse:
        """
        Login patient using username and password.
        This method is used to authenticate a patient user using their username and password.
        """
        valid_data = self.validate_data_login_username_password(obj)
        user = valid_data.user
        internal_client = valid_data.internal_client
        tenant_uuid = valid_data.tenant_uuid

        data_token = encode_jwt_token(
            configuration.RSA_KEY_MANIFEST.get("current_kid"),
            configuration.JWT_RSA_PRIVATE_KEY,
            configuration.JWT_ALGORITHM,
            configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
            "",
            internal_client,
            user,
            " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
        )
        OAuth2ClientService.dynamic_save_token_internal_client_sync(
            data_token,
            internal_client.client_id,
            clinic_patient_id=user.id,
            tenant_uuid=tenant_uuid,
        )
        return OAuthTokenInternalResponse(
            access_token=data_token.get("access_token"),
            refresh_token=data_token.get("refresh_token"),
            token_type=data_token.get("token_type"),
            expires_in=data_token.get("expires_in"),
            tenant_uuids=[tenant_uuid],
        )

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="validate_data_login_username_password",
    )
    def validate_data_login_username_password(
        self, obj: LoginRequest
    ) -> LoginUsernamePasswordValid:
        """
        Validate the data for login using username and password.
        This method checks if the username and password are valid.
        """
        token_db = None
        try:
            session_central = CentralDatabase.get_sync_db_session_instance()
            result = session_central.execute(
                select(TenantClinic).where(
                    TenantClinic.tenant_slug == obj.tenant_slug,
                )
            )
            tenant_clinic = result.scalar_one_or_none()
            if not tenant_clinic:
                log.error("❌ Tenant not found for the given slug")
                raise CustomValueError(
                    message=CustomMessageCode.TENANT_NOT_FOUND.title,
                    message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
                )
            token_db = set_current_db_name(tenant_clinic.db_name)

            session = next(TenantDatabase.get_sync_db_session())
            patient_user = session.execute(
                select(PatientUser).where(
                    PatientUser.username == obj.username,
                    PatientUser.status.is_(True),
                )
            )
            patient_user = patient_user.scalar_one_or_none()
            if not patient_user:
                log.error("❌ Patient user not found")
                raise CustomValueError(
                    message=CustomMessageCode.USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.USER_NOT_FOUND.code,
                )

            if not patient_user.validate_password(
                plain_password=obj.password,
                tenant_uuid=str(tenant_clinic.tenant_uuid),
            ):
                log.error("❌ Invalid password for the patient user")
                raise CustomValueError(
                    message=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
                    message_code=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
                )

            internal_client = OAuth2ClientService.get_internal_client_by_client_id_sync(
                session_central, client_id=obj.client_id
            )
            if not internal_client:
                log.error("❌ Internal client not found")
                raise CustomValueError(
                    message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                    message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
                )

            return LoginUsernamePasswordValid(
                tenant_uuid=str(tenant_clinic.tenant_uuid),
                user=patient_user,
                internal_client=internal_client,
            )

        except (DBAPIError, OperationalError) as e:
            log.error(f"❌ Database error during login validation: {str(e)}")
            raise e
        except Exception as e:
            log.error(f"❌ Error setting current database: {str(e)}")
            raise e
        finally:
            if token_db:
                reset_current_db_name(token_db)
