from typing import Optional

from authlib.jose.errors import ExpiredTokenError, InvalidTokenError
from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import GRANT_TYPE_REFRESH_TOKEN
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase, TenantDatabase
from schemas.auth_schema import (
    OAuthTokenInternalResponse,
    OAuthTokenResponse,
    RefreshTokenRequest,
    ValidRefreshTokenRequest,
)
from services.auth_service import OAuth2ClientService
from services.key_manager_service import KeyManager
from services.role_service import RoleService
from sqlalchemy import delete, select, text
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.orm import Session

from gc_dentist_shared.central_models import (
    GlobalUser,
    OAuth2Token,
    TenantPatientUserMapping,
)
from gc_dentist_shared.core.common.jwt_token import decode_jwt_token, encode_jwt_token
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import DoctorUser, PatientUser


class OAuth2Service:
    def refresh_token(self, obj: RefreshTokenRequest):
        db_session = CentralDatabase.get_sync_db_session_instance()
        try:
            with db_session.begin():
                valid_data = self.validate_data_refresh_token(
                    db_session=db_session, obj=obj
                )
                token_obj = valid_data.token
                internal_client = valid_data.internal_client

                user_id_map = {
                    "global_user_id": lambda: self.flow_global_user_id(
                        db_session=db_session,
                        global_user_id=token_obj.global_user_id,
                        internal_client=internal_client,
                    ),
                    "clinic_patient_id": lambda: self.flow_clinic_patient_id(
                        db_session=db_session,
                        clinic_patient_id=token_obj.clinic_patient_id,
                        tenant_uuid=token_obj.tenant_uuid,
                        internal_client=internal_client,
                    ),
                    "clinic_doctor_id": lambda: self.flow_clinic_doctor_id(
                        db_session=db_session,
                        clinic_doctor_id=token_obj.clinic_doctor_id,
                        tenant_uuid=token_obj.tenant_uuid,
                        internal_client=internal_client,
                    ),
                }

                for key, func in user_id_map.items():
                    if getattr(token_obj, key):
                        return func()

                log.error("❌ No valid user ID found in the token")
                raise CustomValueError(
                    message=CustomMessageCode.INVALID_REFRRESH_TOKEN.title,
                    message_code=CustomMessageCode.INVALID_REFRRESH_TOKEN.code,
                )
        except CustomValueError as e:
            log.error(f"❌ Custom error occurred: {str(e)}")
            raise e
        except Exception as e:
            log.error(f"❌ Error refreshing token: {str(e)}")
            raise e
        finally:
            db_session.close()

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="OAuth2Service.logout",
    )
    def logout(self, access_token: str, client_id: str) -> None:
        """
        Revoke (delete) token by access_token.
        - If client_id is provided, ensure the token belongs to that client.
        - Idempotent: success even if token not found.
        """
        db_session = next(CentralDatabase.get_sync_db_session())
        try:
            key_manager = KeyManager()
            _ = decode_jwt_token(
                key_manager.get_key_by_access_token(access_token=access_token),
                configuration.JWT_ALGORITHM,
                access_token,
            )

            with db_session.begin():
                client = OAuth2ClientService.get_internal_client_by_client_id_sync(
                    db_session, client_id
                )
                if not client:
                    raise CustomValueError(
                        message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                        message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
                    )

                delete_stmt = delete(OAuth2Token).where(
                    OAuth2Token.access_token == access_token,
                    OAuth2Token.client_id == client.client_id,
                )
                db_session.execute(delete_stmt)

        except (ExpiredTokenError, InvalidTokenError) as e:
            log.error(f"❌ Token has expired or is invalid: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_TOKEN.title,
                message_code=CustomMessageCode.INVALID_TOKEN.code,
            )
        except CustomValueError as e:
            log.error(f"❌ Logout by access token failed: {str(e)}")
            raise e
        except Exception as e:
            log.error(f"❌ Exception logout by access token failed: {str(e)}")
            raise e
        finally:
            db_session.close()

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="OAuth2Service.flow_clinic_patient_id",
    )
    def flow_clinic_patient_id(
        self,
        db_session: Session,
        clinic_patient_id: int,
        tenant_uuid: str,
        internal_client,
    ) -> OAuthTokenInternalResponse:
        db_name = self.get_db_name_by_tenant_uuid(
            db_session=db_session,
            tenant_uuid=tenant_uuid,
        )
        token_db = set_current_db_name(db_name)
        tenant_session = next(TenantDatabase.get_sync_db_session())
        try:
            patient_user = tenant_session.execute(
                select(PatientUser).where(
                    PatientUser.id == clinic_patient_id,
                    PatientUser.status.is_(True),
                )
            )
            patient_user = patient_user.scalar_one_or_none()
            if not patient_user:
                log.error("❌ Patient user not found")
                raise CustomValueError(
                    message=CustomMessageCode.USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.USER_NOT_FOUND.code,
                )
            return self.create_token(
                user=patient_user,
                internal_client=internal_client,
                tenant_uuids=[tenant_uuid],
                clinic_patient_id=clinic_patient_id,
                tenant_uuid=tenant_uuid,
            )
        except Exception as e:
            log.error(f"❌ Error fetching patient user: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token_db)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="OAuth2Service.flow_clinic_doctor_id",
    )
    def flow_clinic_doctor_id(
        self,
        db_session: Session,
        clinic_doctor_id: int,
        tenant_uuid: str,
        internal_client,
    ) -> OAuthTokenInternalResponse:
        db_name = self.get_db_name_by_tenant_uuid(
            db_session=db_session,
            tenant_uuid=tenant_uuid,
        )
        token_db = set_current_db_name(db_name)
        tenant_session = next(TenantDatabase.get_sync_db_session())
        try:
            doctor_user = tenant_session.execute(
                select(DoctorUser).where(
                    DoctorUser.id == clinic_doctor_id,
                    DoctorUser.status.is_(True),
                )
            )
            doctor_user = doctor_user.scalar_one_or_none()
            if not doctor_user:
                log.error("❌ Doctor user not found")
                raise CustomValueError(
                    message=CustomMessageCode.USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.USER_NOT_FOUND.code,
                )

            role_key_ids = RoleService().get_doctor_role_key_ids(
                doctor_user_id=doctor_user.id
            )
            token = self.create_token(
                user=doctor_user,
                internal_client=internal_client,
                tenant_uuids=[tenant_uuid],
                clinic_doctor_id=clinic_doctor_id,
                tenant_uuid=tenant_uuid,
                role_key_ids=role_key_ids,
            )
            return OAuthTokenResponse(
                **token.model_dump(),
                tenant_uuid=tenant_uuid,
            )
        except Exception as e:
            log.error(f"❌ Error fetching doctor user: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token_db)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="OAuth2Service.flow_global_user_id",
    )
    def flow_global_user_id(
        self,
        db_session: Session,
        global_user_id: int,
        internal_client: str,
    ) -> OAuthTokenInternalResponse:
        result_tenant_uuids = db_session.execute(
            select(TenantPatientUserMapping.tenant_uuid).where(
                TenantPatientUserMapping.global_user_id == global_user_id,
            )
        )
        tenant_uuids = result_tenant_uuids.scalars().all()
        tenant_uuids = [str(tenant_uuid) for tenant_uuid in tenant_uuids]
        if not tenant_uuids:
            log.error("❌ No tenant UUIDs found for the user")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_USER.title,
                message_code=CustomMessageCode.INVALID_USER.code,
            )

        user = db_session.execute(
            select(GlobalUser).where(
                GlobalUser.id == global_user_id,
                GlobalUser.is_active.is_(True),
            )
        ).scalar_one_or_none()
        if not user:
            log.error("❌ User not found or inactive")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_USER.title,
                message_code=CustomMessageCode.INVALID_USER.code,
            )

        return self.create_token(
            user=user,
            internal_client=internal_client,
            tenant_uuids=tenant_uuids,
            global_user_id=global_user_id,
        )

    def create_token(
        self,
        user,
        internal_client,
        tenant_uuids: Optional[list[str]] = None,
        role_key_ids: Optional[list[int]] = None,
        **kwargs,
    ) -> OAuthTokenInternalResponse:
        data_token = encode_jwt_token(
            configuration.RSA_KEY_MANIFEST.get("current_kid"),
            configuration.JWT_RSA_PRIVATE_KEY,
            configuration.JWT_ALGORITHM,
            configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
            "",
            internal_client,
            user,
            " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
            role_key_ids,
        )
        OAuth2ClientService.dynamic_save_token_internal_client_sync(
            data_token, internal_client.client_id, **kwargs
        )
        return OAuthTokenInternalResponse(
            access_token=data_token.get("access_token"),
            refresh_token=data_token.get("refresh_token"),
            token_type=data_token.get("token_type"),
            expires_in=data_token.get("expires_in"),
            tenant_uuids=tenant_uuids or [],
        )

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="OAuth2Service.validate_data_refresh_token",
    )
    def validate_data_refresh_token(
        self, db_session: Session, obj: RefreshTokenRequest
    ) -> ValidRefreshTokenRequest:
        if obj.grant_type != GRANT_TYPE_REFRESH_TOKEN:
            log.error("❌ Invalid grant type")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_GRANT_TYPE.title,
                message_code=CustomMessageCode.INVALID_GRANT_TYPE.code,
            )

        internal_client = OAuth2ClientService.get_internal_client_by_client_id_sync(
            db_session, client_id=obj.client_id
        )
        if not internal_client:
            log.error("❌ Internal client not found")
            raise CustomValueError(
                message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
            )

        token = db_session.execute(
            select(OAuth2Token).where(
                OAuth2Token.client_id == obj.client_id,
                OAuth2Token.refresh_token == obj.refresh_token,
                OAuth2Token.refresh_token_revoked_at == 0,
            )
        ).scalar_one_or_none()

        if not token or token.is_refresh_token_expired():
            log.error("❌ Token not found")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_REFRRESH_TOKEN.title,
                message_code=CustomMessageCode.INVALID_REFRRESH_TOKEN.code,
            )

        return ValidRefreshTokenRequest(token=token, internal_client=internal_client)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="OAuth2Service.get_db_name_by_tenant_uuid",
    )
    def get_db_name_by_tenant_uuid(
        self,
        db_session: Session,
        tenant_uuid: str,
    ) -> str:
        result = db_session.execute(
            text("SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"),
            {"tenant_uuid": tenant_uuid},
        )
        db_name = result.scalar_one_or_none()
        if not db_name:
            log.error("❌ Tenant not found for the given UUID")
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )
        return db_name
