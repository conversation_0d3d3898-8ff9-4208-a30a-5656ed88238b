from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from sqlalchemy import select
from sqlalchemy.exc import DB<PERSON><PERSON>rror, OperationalError

from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import DoctorRole


class RoleService:
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_doctor_role_key_ids",
    )
    def get_doctor_role_key_ids(self, doctor_user_id: int) -> list[int]:
        tenant_db_session = next(TenantDatabase.get_sync_db_session())
        try:
            stmt = select(DoctorRole.role_key_id).where(
                DoctorRole.doctor_user_id == doctor_user_id,
                DoctorRole.delete_flag.is_(False),
            )
            doctor_role_key_ids = tenant_db_session.execute(stmt).scalars().all()
            if not doctor_role_key_ids:
                raise CustomValueError(
                    message=CustomMessageCode.DOCTOR_ROLE_NOT_FOUND.title,
                    message_code=CustomMessageCode.DOCTOR_ROLE_NOT_FOUND.code,
                )

            return doctor_role_key_ids

        except CustomValueError as e:
            log.error(f"❌ Doctor role not found for doctor_user_id: {doctor_user_id}")
            raise e

        except (DBAPIError, OperationalError) as e:
            log.error(f"❌ Database error while getting doctor role: {str(e)}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while getting doctor role: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

        finally:
            tenant_db_session.close()
