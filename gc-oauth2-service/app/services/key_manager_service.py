# app.py
import base64
import json

import jwt
from configuration.settings import configuration
from core.messages import CustomMessageCode
from cryptography.hazmat.primitives import serialization

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log


class KeyManager:
    def __init__(self):
        self.manifest = configuration.RSA_KEY_MANIFEST
        self.private_keys = {}
        self.public_keys = {}
        self._load_all_keys()

    def _load_manifest(self, path):
        with open(path) as f:
            return json.load(f)

    def _load_all_keys(self):
        for kid, key_info in self.manifest["keys"].items():
            if key_info["status"] == "active":
                with open(key_info["private_path"], "rb") as f:
                    self.private_keys[kid] = serialization.load_pem_private_key(
                        f.read(), password=None
                    )
                with open(key_info["public_path"], "rb") as f:
                    self.public_keys[kid] = serialization.load_pem_public_key(f.read())

    def get_item_on_header_access_token(self, access_token: str, key: str):
        try:
            header = jwt.get_unverified_header(access_token)
            return header.get(key, None)
        except Exception as e:
            log.error(f"❌ Failed to get KID from access token: {e}")
            return None

    def get_key_by_access_token(self, access_token: str):
        kid = self.get_item_on_header_access_token(access_token, "kid")
        if kid not in self.public_keys:
            log.error(f"❌ KID {kid} not found in private keys.")
            raise CustomValueError(
                message=CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.title,
                message_code=CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.code,
            )
        return self.public_keys[kid]

    def get_current_signing_key(self):
        current_kid = self.manifest["current_kid"]
        if current_kid not in self.private_keys:
            log.error(f"❌ Current KID {current_kid} not found in private keys.")
            raise CustomValueError(
                message=CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.title,
                message_code=CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.code,
            )
        return self.private_keys[current_kid], current_kid

    def get_all_active_public_jwks(self):
        jwks = []
        for kid, public_key in self.public_keys.items():
            public_numbers = public_key.public_numbers()
            jwks.append(
                {
                    "kty": "RSA",
                    "use": "sig",
                    "kid": kid,
                    "alg": "RS256",
                    "n": self._int_to_base64url(public_numbers.n),
                    "e": self._int_to_base64url(public_numbers.e),
                }
            )
        return {"keys": jwks}

    def _int_to_base64url(self, n):
        num_bytes = n.to_bytes((n.bit_length() + 7) // 8, "big")
        return base64.urlsafe_b64encode(num_bytes).rstrip(b"=").decode("ascii")
