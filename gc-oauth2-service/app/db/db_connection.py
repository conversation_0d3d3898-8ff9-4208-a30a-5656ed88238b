from typing import <PERSON><PERSON><PERSON>, Generator, Optional

from configuration.context.tenant_context import get_current_db_name
from configuration.settings import Settings
from sqlalchemy import URL, Engine, create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker

from gc_dentist_shared.core.exception_handler.custom_exception import (
    ExceptionNotFoundTenantError,
)
from gc_dentist_shared.core.logger.config import log


class CentralDatabase:
    _engine = None
    _sessionmaker: Optional[async_sessionmaker] = None

    @classmethod
    def get_url_db_sync(cls, db_name: str) -> str:
        return URL.create(
            drivername="postgresql",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def get_url(cls):
        return URL.create(
            drivername="postgresql+asyncpg",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=Settings().POSTGRES_GLOBAL_DB_NAME,
        )

    @classmethod
    def get_async_engine(cls):
        if cls._engine is None:
            cls._engine = create_async_engine(
                cls.get_url(),
                echo=Settings().DB_ECHO,
                future=True,
                pool_pre_ping=True,
            )
        return cls._engine

    @classmethod
    def get_sessionmaker(cls) -> async_sessionmaker:
        if cls._sessionmaker is None:
            engine = cls.get_async_engine()
            cls._sessionmaker = async_sessionmaker(
                bind=engine, expire_on_commit=False, autoflush=False
            )
        return cls._sessionmaker

    @classmethod
    async def get_session_maker_factory(cls) -> AsyncSession:
        async_session = cls.get_sessionmaker()
        yield async_session

    @classmethod
    async def get_db_session(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker()
        async_session: AsyncSession = session_maker()  # create session from factory
        try:
            yield async_session
        except Exception as e:
            # log.error(f"❌ Error in database session: {e}")
            raise e
        finally:
            await async_session.close()

    @classmethod
    async def get_instance_db(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker()
        async_session: AsyncSession = session_maker()
        return async_session

    # Config for database sync using flask
    @classmethod
    def get_url_db_central_sync(cls) -> str:
        return URL.create(
            drivername="postgresql+psycopg2",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=Settings().POSTGRES_GLOBAL_DB_NAME,
        )

    @classmethod
    def get_sync_session_maker_factory(cls):
        # --- Config for Flask (Sync) ---
        sync_engine = create_engine(
            cls.get_url_db_central_sync(),
            echo=Settings().DB_ECHO,
            future=True,
            pool_pre_ping=True,
        )
        return sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

    @classmethod
    def get_sync_db_session(cls):
        factory = cls.get_sync_session_maker_factory()
        db = factory()
        try:
            yield db
        finally:
            db.close()

    @classmethod
    def get_sync_db_session_instance(cls):
        factory = cls.get_sync_session_maker_factory()
        db = factory()
        return db


class TenantDatabase:
    """
    Manages database connections for tenant-specific databases.
    Provides synchronous database connection methods.
    """

    # Store engines and sessionmakers for different tenants
    _sync_engines: ClassVar[dict[str, Engine]] = {}
    _sync_sessionmakers: ClassVar[dict[str, sessionmaker]] = {}

    @classmethod
    def get_url_db_sync(cls, db_name: str) -> URL:
        """Create SQLAlchemy URL for synchronous database connection."""
        return URL.create(
            drivername="postgresql",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def get_sync_engine_for_tenant(cls, db_name: str) -> Engine:
        """Get or create a synchronous SQLAlchemy engine for a tenant database."""
        if db_name not in cls._sync_engines:
            db_url = cls.get_url_db_sync(db_name)
            cls._sync_engines[db_name] = create_engine(
                db_url,
                echo=Settings().DB_ECHO,
                future=True,
                pool_pre_ping=True,
            )
        return cls._sync_engines[db_name]

    @classmethod
    def get_sync_sessionmaker_for_tenant(cls) -> sessionmaker:
        """Get or create a synchronous session factory for the current tenant database."""
        db_name = get_current_db_name()
        if not db_name:
            log.error("❌ No tenant database name found in context")
            raise ExceptionNotFoundTenantError(
                "No tenant database name found in context"
            )

        if db_name not in cls._sync_sessionmakers:
            engine = cls.get_sync_engine_for_tenant(db_name)
            cls._sync_sessionmakers[db_name] = sessionmaker(
                bind=engine, expire_on_commit=False, autoflush=False
            )
        return cls._sync_sessionmakers[db_name]

    @classmethod
    def get_sync_db_session(cls) -> Generator[Session, None, None]:
        """Yield a synchronous database session for the current tenant database."""
        session_factory = cls.get_sync_sessionmaker_for_tenant()
        db_session = session_factory()
        try:
            yield db_session
        finally:
            db_session.close()

    @classmethod
    def get_sync_db_session_instance(cls) -> Session:
        """Return a synchronous database session for the current tenant database."""
        session_factory = cls.get_sync_sessionmaker_for_tenant()
        return session_factory()
