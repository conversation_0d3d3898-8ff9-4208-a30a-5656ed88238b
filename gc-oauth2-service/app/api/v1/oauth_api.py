from auth_server.oauth2 import authorization
from core.common.api_response import ApiR<PERSON>ponse
from core.messages import CustomMessageCode
from flask import (
    Blueprint,
    jsonify,
    redirect,
    render_template,
    request,
    session,
    url_for,
)
from schemas.oauth_schema import CreateOauthClient
from services.auth_service import OAuth2ClientService
from services.key_manager_service import KeyManager
from services.user_service import get_global_user_by_id, get_global_user_by_name

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

bp = Blueprint("oauth", __name__)


@bp.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form.get("username")
        user = get_global_user_by_name(username)
        if user:
            session["user_id"] = user.id
            next_url = request.args.get("next", "/")
            return redirect(next_url)
    return render_template("login.html")


@bp.route("/oauth/authorize", methods=["GET", "POST"])
def authorize():
    user_id = session.get("user_id")
    user = get_global_user_by_id(user_id)
    if not user:
        return redirect(url_for("oauth.login", next=request.url))

    if request.method == "GET":
        grant = authorization.get_consent_grant(request, end_user=user)
        return render_template("authorize.html", grant=grant, user=user)

    confirm = request.form.get("confirm")
    grant_user = user if confirm == "yes" else None
    return authorization.create_authorization_response(request, grant_user=grant_user)


@bp.route("/oauth/token", methods=["POST"])
def issue_token():
    return authorization.create_token_response()


@bp.route("/oauth/revoke", methods=["POST"])
def revoke_token():
    return authorization.create_endpoint_response("revocation")


@bp.route("/create-client", methods=["POST"])
def create_client():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                status_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )
        data = CreateOauthClient(**json_data)
        service = OAuth2ClientService()
        service.create_oauth_client_sync(data)
        return ApiResponse.success()
    except CustomValueError as e:
        log.error(f"❌ Refresh token failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except ValueError as e:
        log.error(f"❌ Refresh token valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Refresh token exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )


@bp.route("/.well-known/jwks.json", methods=["GET"])
def jwks():
    key_manager = KeyManager()
    return jsonify(key_manager.get_all_active_public_jwks())
