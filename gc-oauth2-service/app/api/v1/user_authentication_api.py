from http import HTTPStatus

from core.common.api_response import ApiResponse
from core.constants import TOKEN_PREFIX
from core.messages import CustomMessageCode
from flask import Blueprint, request
from schemas.auth_schema import (
    <PERSON><PERSON><PERSON><PERSON>PasswordRequest,
    DoctorLoginOTPRequest,
    DoctorLoginRequest,
    LoginOTPRequest,
    LoginOTPVerifyRequest,
    LoginRequest,
    LogoutRequest,
    RefreshTokenRequest,
)
from services.doctor_auth_service import Doctor<PERSON>uthService
from services.oauth2_service import OAuth2Service
from services.user_patient_service import UserPatientService

from gc_dentist_shared.core.constants import AUTHORIZATION_HEADER, X_TENANT_SLUG
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

bp = Blueprint("users", __name__)


@bp.route("/refresh-token", methods=["POST"])
def refresh_token():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )
        data = RefreshTokenRequest(**json_data)
        auth_service = OAuth2Service()
        response = auth_service.refresh_token(data)
        return ApiResponse.success(data=response.model_dump())
    except CustomValueError as e:
        log.error(f"❌ Refresh token failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Refresh token valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Refresh token exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/patient/send-otp", methods=["POST"])
def login_phone_otp():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        login_data = LoginOTPRequest(**json_data)
        auth_service = UserPatientService()
        auth_service.login_with_phone_otp(login_data)
        return ApiResponse.success(
            data={
                "message": CustomMessageCode.OTP_SENT_SUCCESS.title,
            },
            message=CustomMessageCode.OTP_SENT_SUCCESS.title,
            message_code=CustomMessageCode.OTP_SENT_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(f"❌ Login OTP failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Patient login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Login OTP exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/patient/verify-otp", methods=["POST"])
def verify_phone_otp():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        login_data = LoginOTPVerifyRequest(**json_data)
        auth_service = UserPatientService()
        response = auth_service.verify_phone_otp(login_data)
        return ApiResponse.success(data=response.model_dump())
    except CustomValueError as e:
        log.error(f"❌ Verify OTP failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Patient login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Verify OTP exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/doctor/send-otp", methods=["POST"])
def login_doctor():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        tenant_slug = request.headers.get(X_TENANT_SLUG)
        if not tenant_slug:
            return ApiResponse.error(
                message=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
                message_code=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
                status_code=HTTPStatus.UNAUTHORIZED.value,
            )

        login_request_data = DoctorLoginRequest(**json_data)
        doctor_auth_service = DoctorAuthService()
        response = doctor_auth_service.authenticate_user(
            tenant_slug=tenant_slug, obj=login_request_data
        )

        return ApiResponse.success(
            data=response.model_dump(),
            message=CustomMessageCode.SEND_MAIL_OTP_SUCCESS.title,
            message_code=CustomMessageCode.SEND_MAIL_OTP_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(f"❌ Login for doctor by sending OTP via mail failed: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Patient login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(
            f"❌ Login for doctor by sending OTP via mail failed exception: {str(e)}"
        )
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/doctor/verify-otp", methods=["POST"])
def verify_otp():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        tenant_slug = request.headers.get(X_TENANT_SLUG)
        if not tenant_slug:
            return ApiResponse.error(
                message=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
                message_code=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
                status_code=HTTPStatus.UNAUTHORIZED.value,
            )

        login_data = DoctorLoginOTPRequest(**json_data)
        doctor_auth_service = DoctorAuthService()
        response = doctor_auth_service.verify_otp(
            tenant_slug=tenant_slug, obj=login_data
        )

        return ApiResponse.success(data=response.model_dump())
    except CustomValueError as e:
        log.error(f"❌ Verify mail OTP failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Patient login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Verify mail OTP exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/doctor", methods=["POST"])
def login_doctor_username_password():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        tenant_slug = request.headers.get(X_TENANT_SLUG)
        if not tenant_slug:
            return ApiResponse.error(
                message=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
                message_code=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
                status_code=HTTPStatus.UNAUTHORIZED.value,
            )

        login_request_data = DoctorLoginRequest(**json_data)
        doctor_auth_service = DoctorAuthService()
        response = doctor_auth_service.login_doctor_username_password(
            tenant_slug=tenant_slug, obj=login_request_data
        )
        return ApiResponse.success(data=response.model_dump())

    except CustomValueError as e:
        log.error(f"❌ Doctor login failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Doctor login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Doctor login exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/patient", methods=["POST"])
def login_patient_username_password():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )
        auth_service = UserPatientService()
        json_data = LoginRequest(**json_data)
        response = auth_service.login_patient_username_password(json_data)
        return ApiResponse.success(data=response.model_dump())
    except CustomValueError as e:
        log.error(f"❌ Patient login failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Patient login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Patient login exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/logout", methods=["POST"])
def logout():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        authorization_header = request.headers.get(AUTHORIZATION_HEADER, "")
        authorization_parts = authorization_header.split()

        if len(authorization_parts) != 2 or authorization_parts[0] != TOKEN_PREFIX:
            raise CustomValueError(
                message=CustomMessageCode.INVALID_AUTHORIZATION_HEADER.title,
                message_code=CustomMessageCode.INVALID_AUTHORIZATION_HEADER.code,
            )
        access_token = authorization_parts[1]

        data = LogoutRequest(**json_data)
        auth_service = OAuth2Service()
        auth_service.logout(
            access_token=access_token,
            client_id=data.client_id,
        )

        return ApiResponse.success()

    except CustomValueError as e:
        log.error(f"❌ Logout failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ Logout validation error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Logout exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/forgot-password/doctor/send-otp", methods=["POST"])
def forgot_password():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        tenant_slug = request.headers.get(X_TENANT_SLUG)
        if not tenant_slug:
            return ApiResponse.error(
                message=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
                message_code=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
                status_code=HTTPStatus.UNAUTHORIZED.value,
            )
        forgot_password_data = DoctorForgotPasswordRequest(**json_data)
        doctor_auth_service = DoctorAuthService()
        doctor_auth_service.forgot_password(
            tenant_slug=tenant_slug, obj=forgot_password_data
        )
        return ApiResponse.success(
            message=CustomMessageCode.FORGOT_PASSWORD_OTP_SENT_SUCCESS.title,
            message_code=CustomMessageCode.FORGOT_PASSWORD_OTP_SENT_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(f"❌ Forgot password for doctor failed: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.FORBIDDEN.value,
        )
    except ValueError as e:
        log.error(f"❌ Forgot password for doctor valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ Forgot password for doctor exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
