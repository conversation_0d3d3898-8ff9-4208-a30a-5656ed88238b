swagger: "2.0"

info:
  title: Noda Oauth API
  description: This is a API that provides a greeting message.
  version: "1.0.0"

produces:
  - application/json

tags:
  - name: "Oauth Patient"
    description: "Endpoints related to patient management"
  - name: "<PERSON>auth Doctor"
    description: "Endpoints related to doctor management"
  - name: "Oauth Admin"
    description: "Endpoints related to admin management"

paths:
  /login/patient/send-otp:
    post:
      summary: "Patient Login with Phone OTP"
      description: "Login endpoint for patients using phone OTP"
      tags:
        - "Oauth Patient"
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              phone_number:
                type: string
                example: "**********"
              country_code:
                type: string
                example: "+81"
              "client_id":
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/BaseResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

  /login/patient/verify-otp:
    post:
      summary: "Verify Phone OTP for Patient Login"
      description: "Endpoint to verify phone OTP for patient login"
      tags:
        - "Oauth Patient"
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              phone_number:
                type: string
                example: "**********"
              country_code:
                type: string
                example: "+81"
              otp:
                type: string
                example: "123456"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/TokenResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"
  
  /login/patient:
    post:
      summary: "Patient Login with Username and Password"
      description: "Login endpoint for patients using username and password"
      tags:
        - "Oauth Patient"
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              username:
                type: string
                example: "patient_username"
              password:
                type: string
                example: "patient_password"
              tenant_slug:
                type: string
                example: "tenant_slug_example"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/TokenResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

  /login/doctor:
    post:
      summary: "Doctor Login with Username and Password"
      description: "Login endpoint for doctor login using username and password"
      tags:
        - "Oauth Doctor"
      parameters:
        - in: header
          name: X-Tenant-Slug
          required: true
          type: string
          description: "Tenant slug for the clinic"
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              username:
                type: string
                example: "doctor_username"
              password:
                type: string
                example: "doctor_password"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/TokenResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

  /login/doctor/send-otp:
    post:
      summary: "Doctor Login with Username and Password"
      description: "Login endpoint for doctors using username and password"
      tags:
        - "Oauth Doctor"
      parameters:
        - in: header
          name: X-Tenant-Slug
          required: true
          type: string
          description: "Tenant slug for the clinic"
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              username:
                type: string
                example: "doctor_username"
              password:
                type: string
                example: "doctor_password"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/BaseResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

  /login/doctor/verify-otp:
    post:
      summary: "Verify OTP for Doctor Login"
      description: "Endpoint to verify OTP for doctor login"
      tags:
        - "Oauth Doctor"
      parameters:
        - in: header
          name: X-Tenant-Slug
          required: true
          type: string
          description: "Tenant slug for the clinic"
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              user_id:
                type: integer
                example: 1
              otp:
                type: string
                example: "123456"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/TokenResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

  /refresh-token:
    post:
      summary: "Refresh Access Token"
      description: "Endpoint to refresh access token using refresh token"
      tags:
        - "Oauth Patient"
        - "Oauth Doctor"
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              grant_type:
                type: string
                example: "refresh_token"
              refresh_token:
                type: string
                example: "your_refresh_token"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/TokenResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"
 
  /logout:
    post:
      summary: "Logout User"
      description: "Endpoint to logout user and invalidate tokens"
      tags:
        - "Oauth Patient"
        - "Oauth Doctor"
      parameters:
        - in: header
          name: Authorization
          required: true
          type: string
          description: "Bearer token for authentication"
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/BaseResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"
  
  /create-client:
    post:
      summary: "Create OAuth Client"
      description: "Endpoint to create a new OAuth client"
      tags:
        - "Oauth Admin"
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              client_name:
                type: string
                example: "New Client"
              client_uri:
                type: string
                example: "https://example.com/callback"
              grant_types:
                type: array
                items:
                  type: string
                example: 
                  - "authorization_code"
                  - "refresh_token"
              redirect_uris:
                type: array
                items:
                  type: string
                example:
                  - "https://example.com/callback"
              response_types:
                type: array
                items:
                  type: string
                example:
                  - "code"
                  - "token"
              scope:
                type: string
                example: "profile+email"
              token_endpoint_auth_method:
                type: string
                example: "client_secret_post"
      responses:
        "200":
          $ref: "#/definitions/BaseResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

  /forgot-password/doctor/send-otp:
    post:
      summary: "Send OTP for Password Reset"
      description: "Endpoint to send OTP to user's email for password reset"
      tags:
        - "Oauth Doctor"
      parameters:
        - in: header
          name: X-Tenant-Slug
          required: true
          type: string
          description: "Tenant slug for the clinic"
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              username:
                type: string
                example: "user_username"
              client_id:
                type: string
                example: "your_client_id"
      responses:
        "200":
          $ref: "#/definitions/BaseResponse"
        "400":
          $ref: "#/definitions/BaseResponse"
        "401":
          $ref: "#/definitions/BaseResponse"

definitions:
  BaseResponse:
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          additionalProperties: false
        message:
          type: string
          example: "Operation successful"
        messageCode:
          type: string
          example: "2000"
        messageErrors:
          type: string
          example: "Errors message or Null"

  TokenResponse:
    description: Response containing access and refresh tokens
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            access_token:
              type: string
              example: "your_access_token"
            refresh_token:
              type: string
              example: "your_refresh_token"
            expires_in:
              type: integer
              example: 3600
            tenant_uuids:
              type: array
              items:
                type: string
                example: "tenant_uuid_example"
            token_type:
              type: string
              example: "Bearer"
        message:
          type: string
          example: "Operation successful"
        messageCode:
          type: string
          example: "2000"
        messageErrors:
          type: string
          example: "Errors message or Null"
