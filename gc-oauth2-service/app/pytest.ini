[pytest]
pythonpath = .
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
env =
    PYTHONPATH = .
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore::FutureWarning
    ignore::PendingDeprecationWarning
    ignore::ImportWarning
    ignore::ResourceWarning
    ignore::UnicodeWarning
    ignore::BytesWarning
    ignore::SyntaxWarning
    ignore::DeprecationWarning:.*
    ignore::UserWarning:.*
    ignore::RuntimeWarning:.*
    ignore::FutureWarning:.*
    ignore::PendingDeprecationWarning:.*
    ignore::ImportWarning:.*
    ignore::ResourceWarning:.*
    ignore::UnicodeWarning:.*
    ignore::BytesWarning:.*
    ignore::SyntaxWarning:.*
