#!/usr/bin/env python3
from typing import Optional

from enums.mail_enum import MailTemplateCategoryEnum
from fastapi_mail import MessageSchema
from pydantic import BaseModel, ConfigDict, EmailStr, Field


class MailRequest(MessageSchema):
    model_config = ConfigDict(extra="ignore")

    reply_to: Optional[EmailStr] = Field(
        default=None, description="Optional reply-to address"
    )
    template_name: Optional[str] = Field(
        default=None, description="Optional template name for rendering content"
    )
    template_variables: Optional[dict] = Field(
        default=None,
        description="Optional dictionary of variables to render in the template",
    )
    category: MailTemplateCategoryEnum
    data_bidding: dict


class MailTemplateSchema(BaseModel):
    category: MailTemplateCategoryEnum
    data_bidding: dict
