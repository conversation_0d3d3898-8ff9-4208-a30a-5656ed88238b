from typing import Any, ClassVar

from enums.master_enum import MasterModelEnum
from pydantic import BaseModel, Field


class MasterRequestData(BaseModel):
    master_names: list[MasterModelEnum] = Field(
        ..., description="List of master model names to fetch data for"
    )


class MasterItemsrequestData(BaseModel):
    master_model: Any = Field(..., description="Name of the master model")
    items: list[str] = Field(default=[], description="List of items to filter")
    filter: dict = Field(default={}, description="Filter condition for the items")

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "master_model": "ExampleMasterModel",
                "items": ["item1", "item2"],
                "filter": {"key": "value"},
            }
        }
