from uuid import UUID

from core.messages import CustomMessageCode
from enums.form_flows import (
    EditActionEnum,
    FormFlowType,
    FormItemFieldType,
    FormItemSide,
)
from pydantic import BaseModel, Field, field_validator, model_validator


class FormItemCreate(BaseModel):
    label: str
    sub_label: str | None = None
    field_type: FormItemFieldType
    item_side: FormItemSide | None = None
    required: bool = False
    is_favorite: bool = False
    extra_data: dict | None = None
    order_index: int


class FormItemGroupCreate(BaseModel):
    title: str
    description: str | None = None
    display_type: int | None = None
    order_index: int

    items: list[FormItemCreate] = Field(
        ...,
        min_length=1,
        description=CustomMessageCode.FORM_FLOW_ERROR_EMPTY_GROUP_ITEMS.description,
    )

    @field_validator("items")
    @classmethod
    def validate_unique_item_order(
        cls, v: list[FormItemCreate]
    ) -> list[FormItemCreate]:
        indices = [item.order_index for item in v]
        if len(indices) != len(set(indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_GROUP_ITEM_ORDER.title
            )
        return v


class FormCreate(BaseModel):
    form_name: str
    description: str | None = None
    order_index: int
    items: list[FormItemCreate] = []  # Single items
    groups: list[FormItemGroupCreate] = []

    @model_validator(mode="after")
    def validate_unique_content_order(self) -> "FormCreate":
        if not self.items and not self.groups:
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_EMPTY_CONTENT_ORDER.title
            )

        group_indices = [g.order_index for g in self.groups]
        item_indices = [i.order_index for i in self.items]
        combined_indices = group_indices + item_indices

        if len(combined_indices) != len(set(combined_indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_FORM_CONTENT_ORDER.title
            )
        return self


class FormFlowsCreate(BaseModel):
    flow_name: str
    flow_type: FormFlowType
    description: str | None = None
    version: str | None = None

    forms: list[FormCreate] = Field(
        ...,
        min_length=1,
        description=CustomMessageCode.FORM_FLOW_ERROR_MINIMUM_FORMS.description,
    )

    @field_validator("forms")
    @classmethod
    def validate_unique_form_order(cls, v: list[FormCreate]) -> list[FormCreate]:
        indices = [form.order_index for form in v]
        if len(indices) != len(set(indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_FORM_ORDER.title
            )
        return v


class EditFormFlowBase(BaseModel):
    uuid: str | None = None
    action: EditActionEnum

    @model_validator(mode="after")
    def check_uuid_is_required_for_edit_actions(self) -> "EditFormFlowBase":
        try:
            if self.uuid:
                UUID(self.uuid)
        except ValueError:
            raise ValueError(CustomMessageCode.FORM_FLOW_ERROR_UUID_INVALID.title)

        if self.action == EditActionEnum.UPDATE and self.uuid is None:
            raise ValueError(CustomMessageCode.FORM_FLOW_ERROR_UUID_REQUIRED.title)

        if self.action == EditActionEnum.CREATE and self.uuid:
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_UUID_SHOULD_NOT_EXIST_ON_CREATE.title
            )

        return self


class FormItemUpdate(EditFormFlowBase):
    label: str
    sub_label: str | None = None
    field_type: FormItemFieldType
    item_side: FormItemSide | None = None
    required: bool = False
    is_favorite: bool = False
    extra_data: dict | None = None
    order_index: int


class FormItemGroupUpdate(EditFormFlowBase):
    title: str
    description: str | None = None
    display_type: int | None = None
    order_index: int
    items: list[FormItemUpdate] = Field(
        ...,
        min_length=1,
        description=CustomMessageCode.FORM_FLOW_ERROR_EMPTY_GROUP_ITEMS.description,
    )

    @field_validator("items")
    @classmethod
    def validate_unique_item_order(
        cls, v: list[FormItemUpdate]
    ) -> list[FormItemUpdate]:
        indices = [item.order_index for item in v]
        if len(indices) != len(set(indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_GROUP_ITEM_ORDER.title
            )
        return v


class FormUpdate(EditFormFlowBase):
    form_name: str
    description: str | None = None
    order_index: int
    items: list[FormItemUpdate] = []
    groups: list[FormItemGroupUpdate] = []

    @model_validator(mode="after")
    def check_form_update_logic(self) -> "FormUpdate":
        if not self.items and not self.groups:
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_EMPTY_CONTENT_ORDER.title
            )

        group_indices = [g.order_index for g in self.groups]
        item_indices = [i.order_index for i in self.items]
        combined_indices = group_indices + item_indices

        if len(combined_indices) != len(set(combined_indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_FORM_CONTENT_ORDER.title
            )
        return self


class FormFlowUpdate(BaseModel):
    flow_name: str
    flow_type: FormFlowType
    description: str | None = None
    version: str

    forms: list[FormUpdate] = Field(
        ...,
        min_length=1,
        description=CustomMessageCode.FORM_FLOW_ERROR_MINIMUM_FORMS.description,
    )

    @model_validator(mode="after")
    def check_form_flow_update_logic(self) -> "FormFlowUpdate":
        indices = [form.order_index for form in self.forms]
        if len(indices) != len(set(indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_FORM_ORDER.title
            )

        return self
