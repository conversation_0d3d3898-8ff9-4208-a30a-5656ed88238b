from typing import ClassVar

from core.messages import CustomMessageCode
from enums.s3_enum import PrefixNameEnum, RoleEnum
from pydantic import BaseModel, field_validator


class LoginRequestSchema(BaseModel):
    username: str
    password: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "username": "john_doe",
                "password": "secure_password",  # pragma: allowlist secret
            }  # pragma: allowlist secret
        }  # pragma: allowlist secret


class TenantInfoSchema(BaseModel):
    db_name: str
    tenant_uuid: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "db_name": "tenant_db",
                "tenant_uuid": "123e4567-e89b-12d3-a456-************",
            }
        }


class S3GeneratedPresignedUrlRequest(BaseModel):
    file_names: list[str]
    prefix_name: PrefixNameEnum
    role: RoleEnum
    id: int

    @field_validator("file_names")
    @classmethod
    def validate_file_names(cls, value):
        if not value or not all(
            isinstance(name, str)
            and name.strip()
            and "." in name
            and not name.startswith(".")
            and not name.endswith(".")
            for name in value
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_FILE_NAMES.title)
        return value
