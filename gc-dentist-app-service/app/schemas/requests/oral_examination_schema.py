from datetime import datetime
from typing import Optional

from core.messages import CustomMessageCode
from enums.oral_examination_enum import PositionEnum, TeethNameEnum, ToothType
from pydantic import BaseModel, Field, HttpUrl, field_validator, model_validator

from gc_dentist_shared.core.common.timezone import Timestamp
from gc_dentist_shared.core.common.utils import (
    ValidateDateString,
    convert_datetime_with_timezone,
)


class ToothPoint(BaseModel):
    inspection: Optional[str] = Field(None, description="Inspection")
    pocket_depth_mm: Optional[float] = Field(None, ge=0, le=15)
    recession_mm: Optional[float] = Field(None, ge=0, le=10)
    clinical_attachment_loss_mm: Optional[float] = Field(None, ge=0)
    bleeding: Optional[bool] = None
    plaque: Optional[bool] = None
    mobility: Optional[int] = Field(None, ge=0, le=3)
    furcation_involvement: Optional[int] = Field(None, ge=0, le=3)
    suppuration: Optional[bool] = None
    calculus: Optional[bool] = None

    # Metadata
    image_url: Optional[HttpUrl] = None
    image_note: Optional[str] = None
    diagnosis_code: Optional[str] = None
    evaluation_result: Optional[str] = None
    note: Optional[str] = None
    extra_data: Optional[dict[str, str]] = None


class WholeToothData(ToothPoint):
    pass


class ToothData(BaseModel):
    tooth_type: ToothType
    is_treatment: Optional[bool] = False
    extra_data: Optional[dict[str, str]] = None

    whole_tooth: Optional[WholeToothData] = None
    points: Optional[dict[PositionEnum, ToothPoint]] = None

    note: Optional[str] = None
    treatment: Optional[list[str]] = None

    # Ensure only one of whole_tooth or points is provided
    def __init__(self, **data):
        super().__init__(**data)
        if self.whole_tooth and self.points:
            raise ValueError(
                CustomMessageCode.VALUE_ERROR_INVALID_WHOLE_TOOTH_OR_POINT.title
            )


class OralExaminationData(BaseModel):
    """
    Schema for the teeth data in a dental visit.
    """

    teeth: dict[TeethNameEnum, ToothData]
    general_image_url: Optional[HttpUrl] = None
    extra_data: Optional[dict] = None


class CreateIntraoralExaminationRequest(BaseModel):
    """
    Request schema for Create IntraOral Examination data.
    """

    patient_user_id: int = Field(..., description="Patient user ID")
    medical_history_id: int = Field(..., description="Medical history ID")
    examination_date: Timestamp = Field(
        ..., description="Date of the examination in ISO format"
    )
    intraoral_examination: OralExaminationData

    note: Optional[str] = Field(None, description="Note for the intraoral examination")
    memo_path: Optional[str] = Field(None, description="Path S3 to the memo file")
    tooth_type: Optional[ToothType] = Field(None, description="Type of tooth examined")

    @field_validator("examination_date")
    @classmethod
    def validate_examination_date_not_past(cls, value):
        if value is not None:
            visit_date = value.date()
            today = convert_datetime_with_timezone(datetime.now().astimezone()).date()
            if visit_date < today:
                raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_DATE_PAST.title)
        return value


class UpdateIntraoralExaminationRequest(BaseModel):
    """
    Request schema for Update IntraOral Examination data.
    """

    examination_date: Optional[Timestamp] = Field(
        None, description="Date of the examination in ISO format"
    )
    intraoral_examination: Optional[OralExaminationData] = Field(
        None, description="Oral examination data"
    )

    note: Optional[str] = Field(None, description="Note for the intraoral examination")
    memo_path: Optional[str] = Field(None, description="Path S3 to the memo file")
    tooth_type: Optional[ToothType] = Field(None, description="Type of tooth examined")

    @field_validator("examination_date")
    @classmethod
    def validate_examination_date_not_past(cls, value):
        if value is not None:
            visit_date = value.date()
            today = convert_datetime_with_timezone(datetime.now().astimezone()).date()
            if visit_date < today:
                raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_DATE_PAST.title)
        return value

    @model_validator(mode="after")
    def validate_at_least_one_field(self):
        if not any(
            getattr(self, field) is not None for field in self.__class__.model_fields
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_AT_LEAST_ONE_FIELD.title)
        return self


class FilterIntraoralExaminationRequest(BaseModel):
    """
    Request schema for filtering IntraOral Examination data.
    """

    oral_examination_id: Optional[int] = Field(None, description="Oral examination ID")
    patient_user_id: Optional[int] = Field(None, description="Patient user ID")
    medical_history_id: Optional[int] = Field(None, description="Medical history ID")
    examination_date: Optional[ValidateDateString] = Field(
        None, description="Date of the examination"
    )

    @model_validator(mode="after")
    def validate_at_least_one_field(self):
        if not any(
            getattr(self, field) is not None for field in self.__class__.model_fields
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_AT_LEAST_ONE_FIELD.title)
        return self
