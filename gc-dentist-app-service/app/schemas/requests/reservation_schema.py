from datetime import date, time
from typing import Optional

from enums.reservation_system_enum import ReservationSystemEnum
from pydantic import BaseModel, Json


class SyncDataReservationToPatientWaiting(BaseModel):
    source: Optional[ReservationSystemEnum] = None
    target_date: Optional[date] = None


class CreateReservation(BaseModel):
    external_reservation_code: str
    patient_user_id: Optional[int] = None
    external_patient_id: str
    source: str
    extra_data: Json
    schedule_visit_date: date
    schedule_visit_time: time
    schedule_visit_minutes: Optional[int] = None
    note: Optional[str] = None
    status: int


class GetListExternalReservation(BaseModel):
    source: ReservationSystemEnum
    target_date: Optional[date] = None
