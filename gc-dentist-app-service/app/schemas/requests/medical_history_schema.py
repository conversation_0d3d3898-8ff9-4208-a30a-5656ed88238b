from datetime import datetime
from typing import Optional

from core.messages import CustomMessageCode
from enums.medical_history_enum import MedicalHistoryStatus
from pydantic import BaseModel, Field, field_validator, model_validator

from gc_dentist_shared.core.common.timezone import Timestamp
from gc_dentist_shared.core.common.utils import (
    ValidateDateString,
    convert_datetime_with_timezone,
)


class MedicalHistoryCreate(BaseModel):
    patient_user_id: int = Field(..., description="Patient user ID")
    patient_waiting_id: int = Field(..., description="Patient waiting ID")
    doctor_user_ids: list[int] = Field(..., description="List of doctor user IDs")
    status: MedicalHistoryStatus = Field(
        default=MedicalHistoryStatus.PROCESSING.value,
        description="Status medical history",
    )
    visit_start_datetime: Timestamp = Field(
        ..., description="Start datetime of the visit in ISO format"
    )

    @field_validator("visit_start_datetime")
    @classmethod
    def validate_visit_date_not_past(cls, value):
        if value is not None:
            visit_date = value.date()
            today = convert_datetime_with_timezone(datetime.now().astimezone()).date()
            if visit_date < today:
                raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_DATE_PAST.title)
        return value


class MedicalHistoryUpdate(BaseModel):
    doctor_user_ids: Optional[list[int]] = Field(
        None, description="List of doctor user IDs"
    )
    status: Optional[MedicalHistoryStatus] = Field(
        None, description="Status of the medical history"
    )
    visit_start_datetime: Optional[Timestamp] = Field(
        None, description="Start datetime of the visit in ISO format"
    )

    @model_validator(mode="after")
    def validate_at_least_one_field(self):
        if not any(
            getattr(self, field) is not None for field in self.__class__.model_fields
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_AT_LEAST_ONE_FIELD.title)
        return self

    @field_validator("visit_start_datetime")
    @classmethod
    def validate_visit_date_not_past(cls, value):
        if value is not None:
            visit_date = value.date()
            today = convert_datetime_with_timezone(datetime.now().astimezone()).date()
            if visit_date < today:
                raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_DATE_PAST.title)
        return value


class MedicalHistoryFilter(BaseModel):
    medical_history_id: Optional[int] = Field(
        None, description="Filter by medical history ID"
    )
    patient_user_id: Optional[int] = Field(
        None, description="Filter by patient user ID"
    )
    patient_waiting_id: Optional[int] = Field(
        None, description="Filter by patient waiting ID"
    )
    status: Optional[MedicalHistoryStatus] = Field(
        None, description="Filter by status of medical history"
    )
    visit_start_datetime: Optional[ValidateDateString] = Field(
        None, description="Filter by start datetime of the visit in ISO format"
    )

    @model_validator(mode="after")
    def validate_at_least_one_field(self):
        if not any(
            getattr(self, field) is not None for field in self.__class__.model_fields
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_AT_LEAST_ONE_FIELD.title)
        return self
