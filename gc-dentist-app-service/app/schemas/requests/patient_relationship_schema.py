from enums.relationship_enum import RelationshipType
from pydantic import BaseModel, Field


class CreateRelationshipRequest(BaseModel):
    """Request schema for creating relationship request"""

    requester_user_id: int = Field(..., description="Requester user ID")
    target_user_id: int = Field(..., description="Target user ID to link")
    relationship: RelationshipType = Field(
        ...,
        description="1: parent-child, 2: spouse",
    )


class OTPVerificationRequest(BaseModel):
    otp: str = Field(..., description="OTP")
    request_id: int = Field(..., description="Request ID")
    user_id: int = Field(..., description="User ID")
