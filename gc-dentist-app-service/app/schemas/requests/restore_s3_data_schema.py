from datetime import datetime

from pydantic import BaseModel, Field


class RestoreS3DocumentRequest(BaseModel):
    document_uuid: str = Field(..., description="Document UUID")
    version_id: int = Field(..., description="Document version ID")


class RestoreRecord(BaseModel):
    object_key: str = Field(..., description="S3 object key")
    expires_at: datetime = Field(
        ...,
        description="Restore expiry time in ISO-8601 format (e.g., 1970-01-01T00:00:00.000Z)",
    )


class UpdateRestoredStatusRequest(BaseModel):
    records: list[RestoreRecord] = Field(
        ...,
        min_items=1,
        description="List of restore records reported by the Lambda",
    )


class DeleteExpiredRestoredDataRequest(BaseModel):
    records: list[RestoreRecord] = Field(
        ...,
        min_items=1,
        description="List of expired restore records reported by the Lambda (ObjectRestore:Delete)",
    )
