from datetime import datetime
from typing import Any, Optional

from core.messages import CustomMessageCode
from enums.patient_enum import Gender
from pydantic import BaseModel, Field, field_validator

from gc_dentist_shared.core.common.utils import (
    CustomEmailStr,
    PhoneNumberExistCountryCode,
    ValidateDateString,
    convert_datetime_with_timezone,
)
from gc_dentist_shared.core.common.validator import password_validator


class ManagerTenant(BaseModel):
    phone: str = Field(
        ...,
        min_length=10,
        max_length=11,
        strip_whitespace=True,
        description="Phone number of the clinic admin",
    )
    country_code: str = Field(
        ...,
        min_length=2,
        max_length=3,
        strip_whitespace=True,
        description="Country code of the clinic admin's phone number",
    )
    gender: Gender = Field(..., description="Gender for clinic admin")
    date_of_birth: ValidateDateString = Field(
        ...,
        description="Date of birth for clinic admin",
    )
    first_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="First name of the clinic admin",
    )
    last_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Last name of the clinic admin",
    )
    username: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Username for the clinic admin",
    )
    password: str = Field(
        ...,
        min_length=8,
        strip_whitespace=True,
        description="Password for the clinic admin",
    )
    required_change_password: bool = Field(
        True,
        description="Does the clinic admin need to change their password on first login?",
    )
    is_active: bool = Field(True, description="Is the clinic currently active?")

    @field_validator("username")
    @classmethod
    def validate_username(cls, value: str) -> str:
        value = value.strip()
        if not value:
            raise ValueError("Username cannot be empty or whitespace.")
        return value

    @field_validator("password")
    @classmethod
    def validate_password(cls, value: str) -> str:
        return password_validator(value)

    @field_validator("date_of_birth")
    @classmethod
    def validate_date_of_birth(cls, value):
        now = convert_datetime_with_timezone(datetime.now().astimezone()).date()
        if value > now:
            raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_DATE_PAST.title)
        return value


class ClinicInfoSchema(BaseModel):
    clinic_uuid: str = Field(
        ..., strip_whitespace=True, description="UUID of the clinic"
    )
    clinic_slug: str = Field(
        ..., strip_whitespace=True, description="Slug of the clinic"
    )

    clinic_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Name of the clinic",
    )
    phone_number: PhoneNumberExistCountryCode = Field(
        ..., strip_whitespace=True, description="Phone number of the clinic"
    )
    email: CustomEmailStr = Field(
        ..., strip_whitespace=True, description="Email address of the clinic"
    )
    address_1: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_1 of the clinic",
    )
    address_2: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_2 of the clinic",
    )
    address_3: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_3 of the clinic",
    )
    latitude: Optional[str] = Field(
        None,
        strip_whitespace=True,
        description="Latitude of the clinic location",
    )
    longitude: Optional[str] = Field(
        None,
        strip_whitespace=True,
        description="Longitude of the clinic location",
    )
    logo_url: Optional[str] = Field(
        None, strip_whitespace=True, description="Logo URL of the clinic"
    )
    opening_hours: Optional[dict[str, Any]] = Field(
        None, description="Opening hours of the clinic in JSON format"
    )

    manager_info: ManagerTenant
