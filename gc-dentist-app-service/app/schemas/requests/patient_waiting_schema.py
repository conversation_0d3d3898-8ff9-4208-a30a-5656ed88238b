from datetime import date, time
from typing import Optional, Union

from core.messages import CustomMessageCode
from enums.patient_enum import EmergencyFlag, PatientWaitingStatus
from pydantic import BaseModel, Field, field_validator, model_validator

from gc_dentist_shared.core.common.timezone import Timestamp
from gc_dentist_shared.core.common.utils import ValidateDateString


class PatientWaitingCreate(BaseModel):
    patient_user_id: int = Field(..., description="Patient user ID")
    reservation_id: Optional[int] = Field(None, description="Reservation ID")

    emergency_flag: EmergencyFlag = Field(
        EmergencyFlag.NORMAL.value, description="0: Normal, 1: Emergency"
    )
    coming_time: Timestamp = Field(..., description="Patient arrival time")
    status: PatientWaitingStatus = Field(
        default=PatientWaitingStatus.NEW.value, description="Waiting status"
    )

    visit_start_date: date = Field(..., description="Visit start date")
    visit_start_time: time = Field(..., description="Visit start time")
    visit_end_date: Optional[date] = Field(None, description="Visit end date")
    visit_end_time: Optional[time] = Field(None, description="Visit end time")
    assigned_doctors: Optional[list[int]] = Field(
        None, description="List of assigned doctor IDs"
    )
    assigned_room: Optional[str] = Field(None, description="Assigned room name or ID")

    room_number: Optional[int] = Field(default=0, description="Room number")


class PatientWaitingUpdate(BaseModel):
    status: Optional[PatientWaitingStatus] = Field(None, description="Waiting status")
    assigned_doctors: Optional[list[int]] = Field(
        None, description="List of assigned doctor IDs"
    )
    assigned_room: Optional[str] = Field(None, description="Assigned room name or ID")
    room_number: Optional[int] = Field(default=None, description="Room number")

    @model_validator(mode="after")
    def validate_at_least_one_field(self):
        if not any(
            getattr(self, field) is not None for field in self.__class__.model_fields
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_AT_LEAST_ONE_FIELD.title)
        return self


class FilterPatientWaitingSchema(BaseModel):
    target_date: Optional[Union[str, date]] = None
    search: Optional[str] = None

    @field_validator("target_date", mode="before")
    @classmethod
    def validate_target_date(cls, v):
        if v in [None, ""]:
            return None
        return ValidateDateString.validate(v)
