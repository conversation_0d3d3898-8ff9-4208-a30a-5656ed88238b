from pydantic import BaseModel, Field


class NotificationSchema(BaseModel):
    """
    Schema for the notification request.
    """

    title: str = Field(..., description="Title of the notification")
    data: dict = Field(..., description="Data payload for the notification")
    body: str = Field(..., description="Body text of the notification")
    tokens: list[str] = Field(
        ..., description="List of FCM tokens to send the notification to"
    )
