from datetime import datetime
from typing import Optional

from enums.patient_enum import Gender
from pydantic import BaseModel, Field, field_validator, model_validator

from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.common.utils import (
    CustomEmailStr,
    CustomPhoneNumber,
    ValidateDateString,
    convert_datetime_with_timezone,
    is_katakana,
)


class CreatePatientProfile(BaseModel):
    last_name: str = Field(..., min_length=1)
    first_name: str = Field(..., min_length=1)
    last_name_kana: str | None = Field(default=None, min_length=1)
    first_name_kana: str | None = Field(default=None, min_length=1)

    home_phone: str | None = None
    phone: str | None = None
    email: CustomEmailStr | None = None
    country_code: str | None = None

    gender: Gender
    date_of_birth: ValidateDateString
    prefecture_id: int | None = Field(default=None, gt=0)
    postal_code: str | None = Field(default=None, min_length=1)

    address_1: str | None = None
    address_2: str | None = None
    address_3: str | None = None
    parent_name: str | None = None

    @field_validator("postal_code")
    @classmethod
    def normalize_and_validate_postal_code(cls, v: str | None) -> str | None:
        if v is None:
            return None

        normalized_v = v.replace("-", "")

        if len(normalized_v) > 8:
            raise ValueError("Postal code cannot exceed 8 characters")

        return normalized_v

    @field_validator("last_name_kana", "first_name_kana")
    @classmethod
    def validate_kana(cls, v: str | None) -> str | None:
        if v is None:
            return None

        if not is_katakana(v):
            raise ValueError("Invalid kana format. Only Katakana are allowed")
        return v

    @field_validator("date_of_birth")
    @classmethod
    def validate_date_of_birth(cls, v: str) -> ValidateDateString:
        now_tz = convert_datetime_with_timezone(datetime.now().astimezone())
        if v >= now_tz.date():
            raise ValueError("Date of birth cannot be in the future")

        return v


class CreatePatientPayloads(BaseModel):
    patient_no: str = Field(
        ..., min_length=11, max_length=11, description="11-digit clinical number"
    )
    is_adult: bool = Field(default=False)
    profile: CreatePatientProfile

    @model_validator(mode="after")
    def validate_phone_for_adult(self) -> "CreatePatientPayloads":
        if self.is_adult and self.profile.phone is None:
            raise ValueError("Phone is required for adult patients")
        return self

    @model_validator(mode="after")
    def validate_profile_phone(self) -> "CreatePatientPayloads":
        phone = self.profile.phone
        country_code = self.profile.country_code
        if country_code is None:
            return self
        if phone is not None:
            if not (10 <= len(phone) <= 11):
                raise ValueError("Phone number must be 10 or 11 digits")
            try:
                _ = format_phone_number(phone=phone, country_code=country_code)
            except (ValueError, Exception):
                raise ValueError("Invalid phone number")
        return self


class UpdatePatientProfile(BaseModel):
    last_name: str | None = Field(default=None, min_length=1)
    first_name: str | None = Field(default=None, min_length=1)
    last_name_kana: str | None = Field(default=None, min_length=1)
    first_name_kana: str | None = Field(default=None, min_length=1)

    home_phone: CustomPhoneNumber | None = None
    email: CustomEmailStr | None = None

    gender: Gender | None = None
    # phone: CustomPhoneNumber | None = None
    # date_of_birth: Optional[ValidateDateString] = None
    prefecture_id: int | None = Field(default=None, gt=0)
    postal_code: str | None = Field(default=None, min_length=1)

    address_1: str | None = None
    address_2: str | None = None
    address_3: str | None = None
    parent_name: str | None = None

    @field_validator("postal_code")
    @classmethod
    def normalize_and_validate_postal_code(cls, v: str | None) -> str | None:
        if v is None:
            return None

        normalized_v = v.replace("-", "")

        if len(normalized_v) > 8:
            raise ValueError("Postal code cannot exceed 8 characters")

        return normalized_v


class UpdatePatientPayloads(BaseModel):
    is_adult: bool | None = Field(default=False)
    profile: UpdatePatientProfile | None = None


class UpdateOrCreatePatientProfile(BaseModel):
    last_name: str = Field(..., min_length=1)
    first_name: str = Field(..., min_length=1)
    last_name_kana: Optional[str] = Field(default=None, min_length=1)
    first_name_kana: Optional[str] = Field(default=None, min_length=1)

    home_phone: Optional[CustomPhoneNumber] = None
    phone: Optional[CustomPhoneNumber] = None
    email: Optional[CustomEmailStr] = None
    country_code: Optional[str] = None

    gender: Gender
    date_of_birth: ValidateDateString
    prefecture_id: Optional[int] = Field(default=None, gt=0)
    postal_code: Optional[str] = Field(default=None, min_length=1)
    patient_user_id: Optional[int] = None
    external_patient_id: Optional[str] = None


class CreatePatientFromExternalSystem(BaseModel):
    is_adult: bool = Field(default=True)
    username: str = Field(..., min_length=3, max_length=30)
    patient_no: str = Field(..., min_length=3, max_length=30)
    external_patient_id: str = Field(..., min_length=1, max_length=50)
    source: str = Field(..., min_length=1, max_length=20)
    password: Optional[str] = Field(default=None, min_length=6, max_length=30)
