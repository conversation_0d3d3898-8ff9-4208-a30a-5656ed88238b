from typing import Optional

from pydantic import BaseModel, Field


class CreateMedicalTemplatePayload(BaseModel):
    name: str = Field(..., min_length=1, description="Name of the medical template")
    page_data: dict = Field(..., description="Data of the page")
    size: Optional[int] = Field(default=None, description="Size of the template")
    tag: Optional[int] = Field(default=None, description="Tag of the template")
    document_group_id: Optional[int] = Field(
        default=None, description="Document group id"
    )


class UpdateMedicalTemplatePayload(BaseModel):
    name: Optional[str] = Field(default=None, description="Name of the template")
    page_data: Optional[dict] = Field(default=None, description="Data of the page")
    size: Optional[int] = Field(default=None, description="Size of the template")
    tag: Optional[int] = Field(default=None, description="Tag of the template")
    document_group_id: Optional[int] = Field(
        default=None, description="Document group id"
    )
