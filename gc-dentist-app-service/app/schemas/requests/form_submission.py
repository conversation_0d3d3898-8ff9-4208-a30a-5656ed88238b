from typing import Any

from core.messages import CustomMessageCode
from enums.form_flows import FormFlowType, FormItemFieldType, FormItemSide
from pydantic import BaseModel, Field, field_validator, model_validator

from gc_dentist_shared.core.common.utils import UUIDString


class SubmittedFormItem(BaseModel):
    answer: Any
    uuid: UUIDString
    label: str
    sub_label: str | None = None
    field_type: FormItemFieldType
    item_side: FormItemSide | None = None
    required: bool = False
    is_favorite: bool = False
    extra_data: dict | None = None
    order_index: int


class SubmittedFormItemGroup(BaseModel):
    uuid: UUIDString
    title: str
    description: str | None = None
    display_type: int | None = None
    order_index: int
    items: list[SubmittedFormItem] = Field(
        ...,
        min_length=1,
        description=CustomMessageCode.FORM_FLOW_ERROR_EMPTY_GROUP_ITEMS.description,
    )

    @field_validator("items")
    @classmethod
    def validate_unique_item_order(
        cls, v: list[SubmittedFormItem]
    ) -> list[SubmittedFormItem]:
        indices = [item.order_index for item in v]
        if len(indices) != len(set(indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_GROUP_ITEM_ORDER.title
            )
        return v


class SubmittedForm(BaseModel):
    uuid: UUIDString
    form_name: str
    description: str | None = None
    order_index: int
    items: list[SubmittedFormItem] = []
    groups: list[SubmittedFormItemGroup] = []

    @model_validator(mode="after")
    def check_submitted_form_logic(self) -> "SubmittedForm":
        if not self.items and not self.groups:
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_EMPTY_CONTENT_ORDER.title
            )

        group_indices = [g.order_index for g in self.groups]
        item_indices = [i.order_index for i in self.items]
        combined_indices = group_indices + item_indices

        if len(combined_indices) != len(set(combined_indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_FORM_CONTENT_ORDER.title
            )
        return self


class SubmittedFormFlowData(BaseModel):
    uuid: UUIDString
    flow_name: str
    flow_type: FormFlowType
    description: str | None = None
    version: str | None = None
    forms: list[SubmittedForm] = Field(
        ...,
        min_length=1,
        description=CustomMessageCode.FORM_FLOW_ERROR_MINIMUM_FORMS.description,
    )

    @model_validator(mode="after")
    def check_order_index_logic(self) -> "SubmittedFormFlowData":
        indices = [form.order_index for form in self.forms]
        if len(indices) != len(set(indices)):
            raise ValueError(
                CustomMessageCode.FORM_FLOW_ERROR_DUPLICATE_FORM_ORDER.title
            )

        return self


class FormSubmissionCreate(BaseModel):
    form_flow_uuid: UUIDString
    doctor_user_id: int
    patient_user_id: int
    form_flow_data: SubmittedFormFlowData

    @model_validator(mode="after")
    def check_uuid_consistency(self) -> "FormSubmissionCreate":
        if self.form_flow_uuid != self.form_flow_data.uuid:
            raise ValueError(
                CustomMessageCode.FORM_SUBMISSION_ERROR_UUID_MISMATCH.title
            )

        return self


class AnswerUpdate(BaseModel):
    item_uuid: UUIDString
    answer: Any


class FormSubmissionUpdate(BaseModel):
    answers: list[AnswerUpdate]
