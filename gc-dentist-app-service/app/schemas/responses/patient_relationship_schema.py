from enums.relationship_enum import RelationshipType
from pydantic import BaseModel, ConfigDict


class PatientRelationshipDetail(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    patient_id: int
    first_name: str
    last_name: str
    first_name_kana: str
    last_name_kana: str
    patient_no: str
    relationship: RelationshipType


class PatientRelationshipSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    patient_no: str
    first_name: str
    last_name: str
    first_name_kana: str
    last_name_kana: str
    main_patients: list[PatientRelationshipDetail] = []
    sub_patients: list[PatientRelationshipDetail] = []


class PatientRelationshipListSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    patient_no: str
    first_name: str
    last_name: str
    first_name_kana: str
    last_name_kana: str
    gender: int | None = None
    main_patients_count: int = 0
    sub_patients_count: int = 0
