from typing import Optional

from pydantic import BaseModel, Field

from gc_dentist_shared.core.common.timezone import Timestamp


class IntraoralExaminationResponse(BaseModel):
    """Response schema for Create IntraOral Examination data."""

    oral_examination_id: int


class OralExaminationData(BaseModel):
    """Schema for Oral Examination data."""

    id: int = Field(..., description="ID of the oral examination")
    patient_user_id: int = Field(..., description="ID of the patient user")
    medical_history_id: int = Field(..., description="ID of the medical history")
    examination_date: Timestamp = Field(..., description="Date of the examination")
    note: Optional[str] = Field(None, description="Note for the oral examination")
    memo_path: Optional[str] = Field(None, description="Path to the memo file in S3")
    tooth_type: Optional[str] = Field(None, description="Type of tooth examined")

    data: Optional[dict] = Field(
        None, description="Details of the oral examination data"
    )


class OralExaminationResponse(BaseModel):
    results: list[OralExaminationData]
