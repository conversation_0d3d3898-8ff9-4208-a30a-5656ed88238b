from typing import Optional

from pydantic import BaseModel, Field


class MedicalTemplateSchema(BaseModel):
    id: int = Field(..., description="Primary key ID")
    name: str = Field(..., description="Name of the document template")
    page_data: dict = Field(..., description="Data of the page")
    status: Optional[int] = Field(
        default=None, description="Status of the document template"
    )
    size: Optional[int] = Field(
        default=None, description="Size of the document in bytes"
    )
    tag: Optional[int] = Field(default=None, description="Tag of the template")
    document_group_id: Optional[int] = Field(
        default=None, description="Document group id"
    )


class MedicalTemplateSummarySchema(BaseModel):
    id: int = Field(..., description="Primary key ID")
    name: str = Field(..., description="Name of the document template")
    size: Optional[int] = Field(
        default=None, description="Size of the document in bytes"
    )
    tag: Optional[int] = Field(default=None, description="Tag of the template")
    document_group_id: Optional[int] = Field(
        default=None, description="Document group id"
    )


class ListMedicalTemplateGroupSchema(BaseModel):
    """Grouped list item by document group with aggregated templates."""

    document_group_id: int = Field(..., description="Document group id")
    document_group_name: str = Field(..., description="Document group name")
    templates: list[MedicalTemplateSummarySchema] = Field(
        default_factory=list, description="Templates belonging to the group"
    )
