import uuid
from typing import Optional

from enums.form_flows import (
    FormFlowType,
    FormFlowVersion,
    FormItemFieldType,
    FormItemSide,
)
from pydantic import BaseModel, field_serializer

from gc_dentist_shared.core.common.timezone import Timestamp


class BaseUUIDResponse(BaseModel):
    uuid: uuid.UUID

    @field_serializer("uuid")
    def serialize_uuid(self, value: uuid.UUID, _info):
        return str(value)


class FormItemResponse(BaseUUIDResponse):
    label: str
    sub_label: Optional[str] = None
    field_type: FormItemFieldType
    item_side: FormItemSide | None = None
    required: bool
    is_favorite: bool
    is_deletable: bool
    extra_data: Optional[dict] = None
    order_index: int


class FormItemGroupResponse(BaseUUIDResponse):
    title: str
    description: Optional[str] = None
    display_type: Optional[int] = None
    order_index: int
    is_deletable: bool
    items: list[FormItemResponse]


class FormResponse(BaseUUIDResponse):
    form_name: str
    description: Optional[str] = None
    order_index: int
    groups: list[FormItemGroupResponse]
    items: list[FormItemResponse]
    is_deletable: bool


class FormFlowDetailResponse(BaseUUIDResponse):
    flow_name: str
    flow_type: FormFlowType
    is_deletable: bool
    description: Optional[str] = None
    version: Optional[str] = FormFlowVersion.V1_0
    forms: list[FormResponse]


class ListFormFlowSchema(BaseUUIDResponse):
    flow_name: str
    flow_type: FormFlowType
    created_at: Timestamp
