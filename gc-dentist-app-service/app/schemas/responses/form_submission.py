import uuid
from typing import Any

from pydantic import BaseModel, ConfigDict


class FormSubmissionDetailSchema(BaseModel):
    form_flow_uuid: uuid.UUID
    doctor_user_id: int
    patient_user_id: int
    form_flow_data: Any


class DoctorProfileInFormSubmissionList(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    last_name: str
    first_name: str
    last_name_kana: str | None = None
    first_name_kana: str | None = None


class PatientProfileInFormSubmissionList(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    last_name: str
    first_name: str
    last_name_kana: str | None = None
    first_name_kana: str | None = None


class FormSubmissionListSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    uuid: uuid.UUID
    form_flow_uuid: uuid.UUID
    doctor_user_id: int
    patient_user_id: int
    flow_name: str
    doctor_profile: DoctorP<PERSON>fileInFormSubmissionList
    patient_profile: PatientProfileInFormSubmissionList
