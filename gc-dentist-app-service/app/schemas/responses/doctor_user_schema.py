from typing import Optional

from configuration.settings import configuration
from core.constants import DISPLAY_DATE_FORMAT, DOCTOR_FIELDS_ENCRYPTED
from pydantic import BaseModel, ConfigDict, field_serializer, model_validator

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.utils import ValidateDateString


class DoctorProfileSchema(BaseModel):
    last_name: str
    first_name: str
    last_name_kana: Optional[str] = None
    first_name_kana: Optional[str] = None
    country_code: str
    phone: str
    email: str
    gender: int
    date_of_birth: ValidateDateString
    prefecture_id: Optional[int] = None
    postal_code: Optional[str] = None
    address_1: Optional[str] = None
    address_2: Optional[str] = None
    address_3: Optional[str] = None
    order_index: int

    @field_serializer("date_of_birth")
    def serialize_dob(self, v) -> str:
        return v.strftime(DISPLAY_DATE_FORMAT)

    @model_validator(mode="before")
    @classmethod
    def validate(cls, values):
        aes_gcm = AesGCMRotation(configuration)
        for field in DOCTOR_FIELDS_ENCRYPTED:
            if values.get(field):
                values[field] = aes_gcm.decrypt_data(values.get(field))

        return values


class GetDoctorSchema(BaseModel):
    username: str
    uuid: str
    profile: DoctorProfileSchema


class ListItemDoctorProfileSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    last_name: str
    first_name: str
    last_name_kana: str | None = None
    first_name_kana: str | None = None
    gender: int | None = None
    date_of_birth: ValidateDateString
    phone: str
    country_code: str
    email: str

    @field_serializer("date_of_birth")
    def serialize_dob(self, v) -> str:
        return v.strftime(DISPLAY_DATE_FORMAT)

    @model_validator(mode="before")
    @classmethod
    def validate(cls, values):
        aes_gcm = AesGCMRotation(configuration)
        for field in DOCTOR_FIELDS_ENCRYPTED:
            if values.get(field):
                values[field] = aes_gcm.decrypt_data(values.get(field))

        return values


class DoctorListSchema(BaseModel):
    id: int
    username: str
    profile: ListItemDoctorProfileSchema
