from datetime import date, datetime
from typing import ClassV<PERSON>, Optional, Union

from configuration.settings import configuration
from core.constants import DISPLAY_DATETIME_FORMAT
from pydantic import BaseModel, ConfigDict, field_validator, model_validator

from gc_dentist_shared.core.common.cloudfront import CloudFront


class GetDocumentSchema(BaseModel):
    id: int
    patient_user_id: int
    name: str
    document_group_id: Optional[int] = None
    data_type: int
    document_data: Union[list[dict], dict]
    # The old preview_document_data is NULL
    preview_document_data: Optional[Union[list[dict], dict]] = None
    examination_date: date
    medical_history_id: Optional[int] = None
    document_uuid: str
    display_mode: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    s3_status: int
    version_id: int
    document_extension: int

    class Config:
        orm_mode = True
        json_encoders: ClassVar[dict] = {
            datetime: lambda v: v.strftime(DISPLAY_DATETIME_FORMAT) if v else None
        }

    @model_validator(mode="before")
    @classmethod
    def build_s3_signed_url(cls, values):
        cloudfront = CloudFront.get_instance(configuration)
        fields = ["document_data", "preview_document_data"]
        version = values.get("version_id")

        for field in fields:
            data = values.get(field)
            if not data or not isinstance(data, dict):
                continue

            for key, path in data.items():
                data[key] = cloudfront.generate_signed_url(
                    file_path=path, version=version
                )

        return values


class DocumentListSchema(BaseModel):
    examination_date: date
    list_document: list[GetDocumentSchema]


class GetDocumentVersionSchema(BaseModel):
    document_uuid: str
    document_data: Union[list[dict], dict]
    preview_document_data: Optional[Union[list[dict], dict]] = None
    version_id: int
    data_type: int
    display_mode: int
    s3_status: int
    is_latest: bool
    patient_user_id: int
    created_at: datetime
    created_by: Optional[int] = None
    updated_by: Optional[int] = None

    class Config:
        orm_mode = True
        json_encoders: ClassVar[dict] = {
            datetime: lambda v: v.strftime(DISPLAY_DATETIME_FORMAT) if v else None
        }


class LibraryListSchema(BaseModel):
    document_id: int
    document_uuid: str
    document_data: Union[list[dict], dict]
    preview_document_data: Optional[Union[list[dict], dict]] = None
    data_type: int
    display_mode: int
    s3_status: int
    version_id: int
    is_latest: bool
    patient_user_id: int
    created_at: datetime
    history_version_ids: Optional[list] = None

    class Config:
        orm_mode = True
        json_encoders: ClassVar[dict] = {
            datetime: lambda v: v.strftime(DISPLAY_DATETIME_FORMAT) if v else None
        }


class DocumentManagementUpdateResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    patient_user_id: int
    name: Optional[str] = None
    status: Optional[int] = None
    data_type: Optional[int] = None
    document_data: Optional[Union[list, dict]] = None
    preview_document_data: Optional[Union[list, dict]] = None
    document_group_id: Optional[int] = None
    medical_history_id: Optional[int] = None
    examination_date: Optional[str] = None
    display_mode: Optional[int] = None
    extra_data: Optional[dict] = None

    @field_validator("examination_date", mode="before")
    @classmethod
    def format_examination_date(cls, v):
        return v.isoformat() if v else None
