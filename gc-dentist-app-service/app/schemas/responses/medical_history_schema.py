from pydantic import BaseModel

from gc_dentist_shared.core.common.utils import ValidateDateString


class MedicalHistoryUpdateResponse(BaseModel):
    medical_history_id: int


class MedicalHistoryFilterData(BaseModel):
    id: int | None = None
    patient_user_id: int | None = None
    patient_waiting_id: int | None = None
    doctor_user_ids: list[int] | None = None
    status: int | None = None
    visit_start_datetime: ValidateDateString | None = None


class MedicalHistoryResponse(BaseModel):
    dataset: list[MedicalHistoryFilterData] = []

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, mode="json", **kwargs)
        dataset = data.get("dataset", [])
        return dataset
