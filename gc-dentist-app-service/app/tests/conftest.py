import asyncio
import os
import uuid
import warnings
from datetime import datetime
from os.path import dirname, join
from pathlib import Path
from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

import psycopg2
import pytest
import pytest_asyncio
from alembic import command
from alembic.config import Config as AlembicConfig
from dotenv import load_dotenv
from httpx import ASGITransport, AsyncClient
from pydantic import PostgresDsn
from sqlalchemy import text
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from starlette.authentication import AuthCredentials
from starlette.middleware.base import BaseHTTPMiddleware

BASE_URL = dirname(__file__)
ENV_FILE = os.environ.get("USE_ENV_FILE", ".env")

if ENV_FILE and len(ENV_FILE) > 0:
    load_dotenv(join(BASE_URL, ENV_FILE), verbose=True, override=True)
else:
    load_dotenv(verbose=True, override=True)


POSTGRES_SERVER = os.getenv("POSTGRES_SERVER_UNIT_TEST")
POSTGRES_PORT = os.getenv("POSTGRES_PORT_UNIT_TEST")
POSTGRES_USER = os.getenv("POSTGRES_USER_UNIT_TEST")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD_UNIT_TEST")
DATABASE_NAME = (
    os.getenv("USE_TENANT_DBNAME_UNIT_TEST")
    or f"tenant_db_unittest_{datetime.now().strftime("%Y%m%d%H%M%S")}"
)

CENTRAL_DATABASE_NAME = (
    os.getenv("USE_CENTRAL_DBNAME_UNIT_TEST")
    or f"central_db_unittest_{datetime.now().strftime("%Y%m%d%H%M%S")}"
)


SQLALCHEMY_TENANT_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=DATABASE_NAME,
    )
)

SQLALCHEMY_CENTRAL_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=CENTRAL_DATABASE_NAME,
    )
)

SQLALCHEMY_ASYNC_TENANT_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql+asyncpg",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=DATABASE_NAME,
    )
)
SQLALCHEMY_ASYNC_CENTRAL_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql+asyncpg",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=CENTRAL_DATABASE_NAME,
    )
)
print("\n DATABASE_NAME TENANT ", DATABASE_NAME)
print(
    "\n SQLALCHEMY_ASYNC_TENANT_DATABASE_URL ",
    SQLALCHEMY_ASYNC_TENANT_DATABASE_URL,
)
print("\n DATABASE_NAME CENTRAL ", CENTRAL_DATABASE_NAME)
print(
    "\n SQLALCHEMY_ASYNC_CENTRAL_DATABASE_URL ",
    SQLALCHEMY_ASYNC_CENTRAL_DATABASE_URL,
)

warnings.filterwarnings("ignore", category=DeprecationWarning, module="swigvarlink")


@pytest.fixture(scope="session", autouse=True)
def override_tenant_db_url():
    from db.db_connection import TenantDatabase

    TenantDatabase._engines = {}
    TenantDatabase._sessionmakers = {}

    test_url = URL.create(
        drivername="postgresql+asyncpg",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        database=DATABASE_NAME,
    )

    with patch.object(TenantDatabase, "get_url", return_value=test_url):
        yield


@pytest.fixture(scope="session", autouse=True)
def override_central_db_url():
    from db.db_connection import CentralDatabase

    CentralDatabase._engine = None
    CentralDatabase._sessionmaker = None

    test_url_cental_db = URL.create(
        drivername="postgresql+asyncpg",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        database=CENTRAL_DATABASE_NAME,
    )

    with patch.object(CentralDatabase, "get_url", return_value=test_url_cental_db):
        yield


@pytest_asyncio.fixture(scope="session")
def create_new_tenant_database():
    if os.getenv("USE_TENANT_DBNAME_UNIT_TEST"):
        print(f"🔁 Skipping DB creation. Using existing database: {DATABASE_NAME}")
        return

    print(f"🛠️  Creating new test tenant database: {DATABASE_NAME}")
    conn = psycopg2.connect(
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=POSTGRES_PORT,
    )
    conn.autocommit = True
    cur = conn.cursor()
    cur.execute(f"CREATE DATABASE {DATABASE_NAME};")
    cur.close()
    conn.close()

    return DATABASE_NAME


@pytest_asyncio.fixture(scope="session")
def create_new_cental_database():
    if os.getenv("USE_CENTRAL_DBNAME_UNIT_TEST"):
        print(
            f"🔁 Skipping DB creation. Using existing database: {CENTRAL_DATABASE_NAME}"
        )
        return

    print(f"🛠️  Creating new test central database: {CENTRAL_DATABASE_NAME}")
    conn_central = psycopg2.connect(
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=POSTGRES_PORT,
    )
    conn_central.autocommit = True
    cur_central = conn_central.cursor()
    cur_central.execute(f"CREATE DATABASE {CENTRAL_DATABASE_NAME};")
    cur_central.close()
    conn_central.close()

    return CENTRAL_DATABASE_NAME


@pytest.fixture(scope="session")
def alembic_config(override_tenant_db_url, create_new_tenant_database):
    """Return Config Alembic With Database UnitTest"""
    base_dir_alembic = (
        Path(__file__).resolve().parent.parent.parent.parent
        / "gc-admin-app-service/app/"
    )
    alembic_tenant_ini_path = str(base_dir_alembic / "alembic_tenant.ini")

    config_tenant = AlembicConfig(alembic_tenant_ini_path)
    config_tenant.set_main_option(
        "script_location", str(base_dir_alembic / "alembic_tenant")
    )
    config_tenant.set_main_option("sqlalchemy.url", SQLALCHEMY_TENANT_DATABASE_URL)

    return config_tenant


@pytest.fixture(scope="session")
def alembic_config_central(override_central_db_url, create_new_cental_database):
    """Return Config Alembic With Database UnitTest"""
    base_dir_alembic = (
        Path(__file__).resolve().parent.parent.parent.parent
        / "gc-admin-app-service/app/"
    )
    alembic_central_ini_path = str(base_dir_alembic / "alembic_admin.ini")
    print("\n\n alembic_central_ini_path ", alembic_central_ini_path)

    config_cental = AlembicConfig(alembic_central_ini_path)
    config_cental.set_main_option(
        "script_location", str(base_dir_alembic / "alembic_admin")
    )
    config_cental.set_main_option("sqlalchemy.url", SQLALCHEMY_CENTRAL_DATABASE_URL)

    return config_cental


@pytest.fixture(scope="session", autouse=True)
def apply_migrations_tenant(alembic_config):
    """Run migration UnitTest Tenant Database"""
    print("🛠️  Applying migrations... ")
    command.upgrade(alembic_config, "head")


@pytest.fixture(scope="session", autouse=True)
def apply_migrations_central(alembic_config_central):
    """Run migration UnitTest Central Database"""
    print("🛠️  Applying migrations... ")
    command.upgrade(alembic_config_central, "head")


@pytest.fixture(scope="session")
def setup_test_db(apply_migrations_tenant, apply_migrations_central):
    """Ensure database migrations are applied before any test"""
    yield


class DummyMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        return await call_next(request)


@pytest_asyncio.fixture(scope="session")
def app():
    from main import create_app

    app = create_app()
    return app


@pytest_asyncio.fixture(scope="module")
def anyio_backend():
    return "asyncio"


@pytest_asyncio.fixture(scope="session")
async def mock_oauth_admin(user_id: int = 1, role_id: int = 1, **kwargs):
    user = SimpleNamespace(id=user_id, role_id=role_id, **kwargs)

    async def mock_authenticate(*args, **kw):
        return AuthCredentials(["authenticated"]), user

    with patch(
        "configuration.middleware.jwt_auth_middleware.JWTAuthMiddleware.authenticate",
        new=AsyncMock(side_effect=mock_authenticate),
    ) as mock_auth:
        yield mock_auth


@pytest_asyncio.fixture(scope="session")
async def async_client(app, mock_oauth_admin):
    transport = ASGITransport(app=app, raise_app_exceptions=True)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac


@pytest_asyncio.fixture(scope="session")
async def async_tenant_session_maker():
    async_engine = create_async_engine(
        SQLALCHEMY_ASYNC_TENANT_DATABASE_URL,
        echo=True,
        future=True,
        pool_pre_ping=True,
    )
    async_session = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )

    yield async_session


@pytest_asyncio.fixture(scope="session")
async def async_tenant_db_session_object():
    async_engine = create_async_engine(
        SQLALCHEMY_ASYNC_TENANT_DATABASE_URL, echo=True, future=True, pool_pre_ping=True
    )
    async_maker = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )
    async_session = async_maker()  # create session from factory
    yield async_session


@pytest_asyncio.fixture(scope="session")
async def async_central_session_maker():
    async_engine = create_async_engine(
        SQLALCHEMY_ASYNC_CENTRAL_DATABASE_URL,
        echo=True,
        future=True,
        pool_pre_ping=True,
    )
    async_session = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )

    yield async_session


@pytest_asyncio.fixture(scope="session")
async def async_central_db_session_object():
    async_engine = create_async_engine(
        SQLALCHEMY_ASYNC_CENTRAL_DATABASE_URL,
        echo=True,
        future=True,
        pool_pre_ping=True,
    )
    async_maker = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )
    async_session = async_maker()  # create session from factory
    yield async_session


@pytest_asyncio.fixture(scope="session")
def event_loop():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def tenant_uuid(async_central_db_session_object: AsyncSession):
    async with async_central_db_session_object.begin():
        result = await async_central_db_session_object.execute(
            text(
                """
                INSERT INTO tenant_clinics (
                    tenant_uuid, tenant_name, tenant_slug, business_number, db_name, plan_id, status
                ) VALUES (
                    :tenant_uuid,
                    :tenant_name,
                    :tenant_slug,
                    :business_number,
                    :db_name,
                    :plan_id,
                    :status
                )
                RETURNING tenant_uuid;
            """
            ),
            {
                "tenant_uuid": str(uuid.uuid4()),
                "tenant_name": str(uuid.uuid4()),
                "tenant_slug": str(uuid.uuid4()),
                "business_number": str(uuid.uuid4()),
                "db_name": DATABASE_NAME,
                "plan_id": 1,
                "status": 40,
            },
        )
        tenant_uuid = result.scalar_one_or_none()
    return str(tenant_uuid)


@pytest_asyncio.fixture(scope="session")
async def tenant_slug(async_central_db_session_object: AsyncSession, tenant_uuid):
    async with async_central_db_session_object.begin():
        result = await async_central_db_session_object.execute(
            text(
                """
                SELECT tenant_slug FROM tenant_clinics
                WHERE tenant_uuid = :tenant_uuid
            """
            ),
            {"tenant_uuid": tenant_uuid},
        )
        tenant_slug = result.scalar_one_or_none()
    return tenant_slug


@pytest_asyncio.fixture(scope="session")
def truncate_table():
    async def _truncate_table(session: AsyncSession, table_name: list[str]):
        async with session.begin():
            for table in table_name:
                await session.execute(text(f"TRUNCATE TABLE {table} CASCADE;"))

    return _truncate_table


@pytest.fixture(scope="session")
def _headers_tenant_uuid(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }
