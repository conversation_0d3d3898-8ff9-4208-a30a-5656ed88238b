from enum import IntEnum, StrEnum


class UnittestFormFlowType(IntEnum):
    INFORMATION = 1
    SURVEY = 3


class UnittestFormItemFieldType(StrEnum):
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    DATETIME = "datetime"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    SELECT = "select"
    RATING = "rating"
    TABLE = "table"


class UnittestFormItemSide(StrEnum):
    DOCTOR = "doctor"
    PATIENT = "patient"


class UnittestEditActionEnum(StrEnum):
    UPDATE = "UPDATE"
    CREATE = "CREATE"
