from tests.helpers.enums.patient import MedicalHistoryStatus

from gc_dentist_shared.tenant_models import MedicalHistory


async def unittest_insert_medical_history(
    db_session,
    patient_user_id: int,
    patient_waiting_id: int,
    status=MedicalHistoryStatus.PROCESSING.value,
    custom_fields=None,
):
    """Insert a medical history record into the database."""
    data = {
        "patient_user_id": patient_user_id,
        "patient_waiting_id": patient_waiting_id,
        "status": status,
    }

    if custom_fields:
        data.update(custom_fields)

    new_history = MedicalHistory(**data)
    db_session.add(new_history)
    await db_session.flush()
    await db_session.refresh(new_history)

    return new_history.id
