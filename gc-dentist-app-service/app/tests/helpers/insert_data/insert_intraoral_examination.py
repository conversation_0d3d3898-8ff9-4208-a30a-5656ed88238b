from datetime import datetime

from gc_dentist_shared.tenant_models import OralExamination


async def unittest_insert_intraoral_examination(
    db_session,
    patient_user_id: int,
    medical_history_id: int,
    tooth_type: str = "adult",
    custom_fields=None,
):
    """Insert an intraoral examination record into the database."""
    data = {
        "patient_user_id": patient_user_id,
        "medical_history_id": medical_history_id,
        "examination_date": datetime.now().astimezone(),
        "note": "string",
        "memo_path": "https://example.com/memo.pdf",
        "tooth_type": tooth_type,
        "intraoral_examination": {
            "teeth": {
                "upper_right_1": {
                    "tooth_type": tooth_type,
                    "points": {
                        "1": {"inspection": "C3"},
                        "2": {
                            "inspection": "Per",
                            "pocket_depth_mm": 15,
                            "recession_mm": 10,
                            "clinical_attachment_loss_mm": 0,
                            "bleeding": True,
                            "plaque": True,
                            "mobility": 3,
                            "furcation_involvement": 3,
                            "suppuration": True,
                            "calculus": True,
                            "image_url": "https://example.com/",
                            "image_note": "string",
                            "diagnosis_code": "string",
                            "evaluation_result": "string",
                            "note": "string",
                            "extra_data": {
                                "additionalProp1": "string",
                                "additionalProp2": "string",
                                "additionalProp3": "string",
                            },
                        },
                        "3": {"inspection": "C1"},
                    },
                    "note": "string",
                    "treatment": ['C"'],
                },
            },
        },
    }

    if custom_fields:
        data.update(custom_fields)

    new_examination = OralExamination(**data)
    db_session.add(new_examination)
    await db_session.flush()
    await db_session.refresh(new_examination)

    return new_examination.id
