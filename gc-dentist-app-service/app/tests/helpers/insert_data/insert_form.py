import uuid
from typing import Optional

from tests.helpers.enums.form_flows import (
    UnittestFormFlowType,
    UnittestFormItemFieldType,
    UnittestFormItemSide,
)

from gc_dentist_shared.tenant_models import Form, FormFlow, FormItem, FormItemGroup


async def insert_form_flow(db, flow_name=None, custom_fields=None) -> str:
    default_name = str(uuid.uuid4())
    form_flow_uuid = str(uuid.uuid4())
    obj_create = {
        "uuid": form_flow_uuid,
        "flow_name": flow_name or default_name,
        "flow_type": UnittestFormFlowType.INFORMATION,
        "description": str(uuid.uuid4()),
        "version": "1.0",
    }
    if custom_fields:
        obj_create.update(custom_fields)

    new_record = FormFlow(**obj_create)
    db.add(new_record)
    await db.flush()
    await db.refresh(new_record)
    return form_flow_uuid


async def insert_form(db, form_flow_uuid: str, custom_fields=None) -> str:
    form_uuid = str(uuid.uuid4())
    obj_create = {
        "uuid": form_uuid,
        "form_flow_uuid": form_flow_uuid,
        "form_name": str(uuid.uuid4()),
        "description": str(uuid.uuid4()),
        "order_index": 0,
    }
    if custom_fields:
        obj_create.update(custom_fields)

    new_record = Form(**obj_create)
    db.add(new_record)
    await db.flush()
    await db.refresh(new_record)
    return form_uuid


async def insert_form_item_group(db, form_uuid: str, custom_fields=None) -> str:
    group_uuid = str(uuid.uuid4())
    obj_create = {
        "uuid": group_uuid,
        "form_uuid": form_uuid,
        "title": str(uuid.uuid4()),
        "description": str(uuid.uuid4()),
        "display_type": 1,
        "order_index": 0,
    }
    if custom_fields:
        obj_create.update(custom_fields)

    new_record = FormItemGroup(**obj_create)
    db.add(new_record)
    await db.flush()
    await db.refresh(new_record)
    return group_uuid


async def insert_form_item(
    db, form_uuid: str, form_item_group_uuid: Optional[str] = None, custom_fields=None
) -> str:
    item_uuid = str(uuid.uuid4())
    obj_create = {
        "uuid": item_uuid,
        "form_uuid": form_uuid,
        "form_item_group_uuid": form_item_group_uuid,
        "label": str(uuid.uuid4()),
        "field_type": UnittestFormItemFieldType.TEXT,
        "item_side": UnittestFormItemSide.DOCTOR,
        "required": False,
        "is_favorite": False,
        "extra_data": None,
        "order_index": 0,
    }
    if custom_fields:
        obj_create.update(custom_fields)

    new_record = FormItem(**obj_create)
    db.add(new_record)
    await db.flush()
    await db.refresh(new_record)
    return item_uuid
