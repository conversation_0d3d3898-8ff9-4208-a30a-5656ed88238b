from datetime import date, datetime, timedelta

from tests.helpers.enums.patient import EmergencyFlag, PatientWaitingStatus

from gc_dentist_shared.tenant_models import PatientWaiting


async def unittest_insert_patient_waiting(
    db_session,
    patient_user_id: int,
    emergency_flag: EmergencyFlag = EmergencyFlag.NORMAL,
    custom_fields=None,
):
    """Insert a patient waiting record into the database."""
    data = {
        "patient_user_id": patient_user_id,
        "emergency_flag": emergency_flag,
        "coming_time": datetime.now(),
        "status": PatientWaitingStatus.SCHEDULED.value,
        "visit_start_date": date.today(),
        "visit_end_date": date.today(),
        "visit_start_time": datetime.now().time(),
        "visit_end_time": (datetime.now() + timedelta(hours=1)).time(),
    }

    if custom_fields:
        data.update(custom_fields)

    new_waiting = PatientWaiting(**data)
    db_session.add(new_waiting)
    await db_session.flush()
    await db_session.refresh(new_waiting)

    return new_waiting.id
