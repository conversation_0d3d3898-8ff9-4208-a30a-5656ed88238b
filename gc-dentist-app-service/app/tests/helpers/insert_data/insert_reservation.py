import random
from datetime import datetime, timedelta

from enums.reservation_status_enum import ReservationStatusEnum
from enums.reservation_system_enum import ReservationSystemEnum
from sqlalchemy import update

from gc_dentist_shared.tenant_models import Reservation


async def unittest_insert_reservation(
    db_session, custom_fields=None, custom_extra_fields=None
):
    now = datetime.now()
    visit_time = now + timedelta(minutes=5)
    end_time = now + timedelta(minutes=10)

    data = {
        "schedule_visit_date": now.date(),
        "schedule_visit_time": visit_time.time(),
        "external_reservation_code": generate_random_number_str(),
        "external_patient_id": generate_random_number_str(6),
        "source": ReservationSystemEnum.IAPO.value,
        "status": ReservationStatusEnum.STATUS_BOOKED.value,
        "patient_user_id": 1,
    }

    extra_data = {
        "tab_id": data["external_reservation_code"],
        "private_id": data["external_patient_id"],
        "patient_no": generate_random_number_str(6),
        "tab_date": now.date(),
        "tab_start_time": visit_time.time(),
        "tab_end_time": end_time.time(),
        "medical_type_id1": 1,
        "medical_type1": "初診",
        "medical_type_pat1": "初診",
        "medical_type_id2": 5,
        "medical_type2": "Dr",
        "medical_type_pat2": None,
        "medical_type_id3": None,
        "medical_type3": None,
        "medical_type_pat3": None,
        "parts": "00000000000011100000000000000000",
        "baby_tooth": False,
        "tab_delete": 0,
    }

    if custom_extra_fields:
        extra_data.update(custom_extra_fields)
        data["extra_data"] = extra_data

    if custom_fields:
        data.update(custom_fields)

    reservation_model = Reservation(**data)
    db_session.add(reservation_model)
    await db_session.flush()
    await db_session.refresh(reservation_model)

    return reservation_model


async def unittest_update_status_reservation(db_session, reservation_id, status):
    stmt = (
        update(Reservation)
        .where(Reservation.id == reservation_id)
        .values(status=status)
    )
    return await db_session.execute(stmt)


def generate_random_number_str(length: int = 10) -> str:
    return "".join(random.choices("**********", k=length))
