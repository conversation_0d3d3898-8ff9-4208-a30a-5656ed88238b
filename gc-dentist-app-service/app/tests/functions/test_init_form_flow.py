import pytest
from sqlalchemy import select

from gc_dentist_shared.tenant_models import Form, FormFlow, FormItem, FormItemGroup


@pytest.mark.order(1)
@pytest.mark.asyncio
async def test_success_init_form_flow(async_tenant_db_session_object):
    async with async_tenant_db_session_object:
        result = await async_tenant_db_session_object.execute(
            select(FormFlow).where(
                FormFlow.flow_name == "大人用問診票", FormFlow.version == "1.0"
            )
        )
        form_flow = result.scalar_one_or_none()
        assert form_flow is not None
        assert form_flow.flow_type == 1

        result = await async_tenant_db_session_object.execute(
            select(Form).where(Form.form_flow_uuid == form_flow.uuid)
        )
        form = result.scalar_one_or_none()
        assert form is not None
        assert form.form_name == "基本情報 ページ"
        assert form.order_index == 0

        result = await async_tenant_db_session_object.execute(
            select(FormItemGroup).where(FormItemGroup.form_uuid == form.uuid)
        )
        group = result.scalar_one_or_none()
        assert group is not None
        assert group.title == "個人情報"
        assert group.order_index == 0

        result = await async_tenant_db_session_object.execute(
            select(FormItem).where(FormItem.form_item_group_uuid == group.uuid)
        )
        items = result.scalars().all()
        assert len(items) == 5

        expected_items = {
            "お名前": {"required": True, "order_index": 0},
            "フリガナ": {"required": False, "order_index": 1},
            "携帯番号": {"required": True, "order_index": 2},
            "生年月日": {"required": True, "order_index": 3},
            "性別": {"required": True, "order_index": 4},
        }

        for item in items:
            assert item.label in expected_items
            assert item.required == expected_items[item.label]["required"]
            assert item.order_index == expected_items[item.label]["order_index"]
