from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from schemas.requests.medical_history_schema import MedicalHistoryCreate
from services.medical_history_service import MedicalHistoryService
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.patient import MedicalHistoryStatus
from tests.helpers.insert_data.insert_medical_history import (
    unittest_insert_medical_history,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_patient_waitting import (
    unittest_insert_patient_waiting,
)

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.exception_handler.max_retry_exception import (
    MaxRetriesExceededError,
)


@pytest_asyncio.fixture(scope="class")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        patient_waiting_id = await unittest_insert_patient_waiting(
            async_tenant_db_session_object, patient_user_id
        )
        medical_history_id = await unittest_insert_medical_history(
            async_tenant_db_session_object, patient_user_id, patient_waiting_id
        )
    return {
        "patient_user_id": patient_user_id,
        "patient_waiting_id": patient_waiting_id,
        "medical_history_id": medical_history_id,
    }


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "patient_waiting_id, expected_id, expect_exception, exception_type, exception_msg, exception_code",
    [
        # Success: new medical history
        (lambda d: d["patient_waiting_id"], None, False, None, None, None),
        # Success: existing medical history
        (
            lambda d: d["patient_waiting_id"],
            lambda d: d["medical_history_id"],
            False,
            None,
            None,
            None,
        ),
        # Fail: patient_waiting_id not found
        (
            lambda d: 9999999,
            None,
            True,
            CustomValueError,
            CustomMessageCode.PATIENT_WAITING_NOT_FOUND.title,
            CustomMessageCode.PATIENT_WAITING_NOT_FOUND.code,
        ),
    ],
)
async def test_create_medical_history(
    async_tenant_db_session_object,
    setup_data,
    patient_waiting_id,
    expected_id,
    expect_exception,
    exception_type,
    exception_msg,
    exception_code,
):
    data = setup_data
    pid = data["patient_user_id"]
    pwid = patient_waiting_id(data)
    service = MedicalHistoryService()
    async with async_tenant_db_session_object.begin():
        if expect_exception:
            with pytest.raises(exception_type) as exc_info:
                await service.create_medical_history(
                    data=MedicalHistoryCreate(
                        patient_user_id=pid,
                        patient_waiting_id=pwid,
                        doctor_user_ids=[],
                        status=MedicalHistoryStatus.PROCESSING.value,
                        visit_start_datetime=datetime.now().astimezone(),
                    ),
                    db_session=async_tenant_db_session_object,
                )
            exc = exc_info.value
            assert exc.message == exception_msg
            assert exc.message_code == exception_code
        else:
            medical_history_id = await service.create_medical_history(
                data=MedicalHistoryCreate(
                    patient_user_id=pid,
                    patient_waiting_id=pwid,
                    doctor_user_ids=[],
                    status=MedicalHistoryStatus.PROCESSING.value,
                    visit_start_datetime=datetime.now().astimezone(),
                ),
                db_session=async_tenant_db_session_object,
            )
            if expected_id is None:
                assert medical_history_id is not None
            else:
                assert medical_history_id == expected_id(data)


@pytest.mark.asyncio
async def test_fail_create_medical_history_with_exception_retry(
    async_tenant_db_session_object, setup_data
):
    data = setup_data
    service = MedicalHistoryService()
    async with async_tenant_db_session_object.begin():
        with pytest.raises(MaxRetriesExceededError) as exc_info:
            with patch(
                "services.medical_history_service.MedicalHistoryService.medical_history_with_patient_waiting_id",
                side_effect=OperationalError("Operational error", None, None),
            ) as mock_func:
                _ = await service.create_medical_history(
                    data=MedicalHistoryCreate(
                        patient_user_id=data["patient_user_id"],
                        patient_waiting_id=data["patient_waiting_id"],
                        doctor_user_ids=[],
                        status=MedicalHistoryStatus.PROCESSING.value,
                        visit_start_datetime=datetime.now().astimezone(),
                    ),
                    db_session=async_tenant_db_session_object,
                )
                assert mock_func.call_count == 3
        exc = exc_info.value
        assert isinstance(exc, MaxRetriesExceededError)
