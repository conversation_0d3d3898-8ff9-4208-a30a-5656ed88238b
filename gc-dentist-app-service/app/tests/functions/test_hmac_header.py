from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Request
from fastapi.testclient import TestClient

app = FastAPI()


@app.get("/test")
async def api_unitetst_endpoint(
    request: Request,
    x_timestamp: str = Head<PERSON>(None),
    x_signature: str = Header(None),
):
    return {
        "x_timestamp": x_timestamp,
        "x_signature": x_signature,
        "headers": dict(request.headers),
    }


class TestHeaderExtraction:
    def test_extract_headers_from_request(self):
        # Arrange
        client = TestClient(app)
        headers = {
            "X-Timestamp": "1234567890",
            "X-Signature": "test_signature",
            "X-Client-ID": "test_client",
            "X-Nonce": "test_nonce",
        }

        # Act
        response = client.get("/test", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["x_timestamp"] == "1234567890"
        assert data["x_signature"] == "test_signature"
        assert data["headers"]["x-timestamp"] == "1234567890"
        assert data["headers"]["x-signature"] == "test_signature"
        assert data["headers"]["x-client-id"] == "test_client"
        assert data["headers"]["x-nonce"] == "test_nonce"

    def test_missing_headers(self):
        # Arrange
        client = TestClient(app)

        # Act
        response = client.get("/test")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["x_timestamp"] is None
        assert data["x_signature"] is None
        assert "x-timestamp" not in data["headers"]
        assert "x-signature" not in data["headers"]

    def test_case_insensitive_headers(self):
        # Arrange
        client = TestClient(app)
        headers = {
            "x-TIMESTAMP": "1234567890",
            "X-signature": "test_signature",
        }

        # Act
        response = client.get("/test", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["x_timestamp"] == "1234567890"
        assert data["x_signature"] == "test_signature"
        assert data["headers"]["x-timestamp"] == "1234567890"
        assert data["headers"]["x-signature"] == "test_signature"

    def test_multiple_values_for_same_header(self):
        # Arrange
        client = TestClient(app)
        headers = {
            "X-Timestamp": "1234567890,9876543210",
            "X-Signature": "test_signature",
        }

        # Act
        response = client.get("/test", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["x_timestamp"] == "1234567890,9876543210"
        assert data["headers"]["x-timestamp"] == "1234567890,9876543210"

    def test_empty_header_values(self):
        # Arrange
        client = TestClient(app)
        headers = {"X-Timestamp": "", "X-Signature": ""}

        # Act
        response = client.get("/test", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["x_timestamp"] == ""
        assert data["x_signature"] == ""
        assert data["headers"]["x-timestamp"] == ""
        assert data["headers"]["x-signature"] == ""

    def test_special_characters_in_headers(self):
        # Arrange
        client = TestClient(app)
        headers = {
            "X-Timestamp": "1234567890!@#$%^&*()",
            "X-Signature": "test_signature!@#$%^&*()",
        }

        # Act
        response = client.get("/test", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["x_timestamp"] == "1234567890!@#$%^&*()"
        assert data["x_signature"] == "test_signature!@#$%^&*()"
        assert data["headers"]["x-timestamp"] == "1234567890!@#$%^&*()"
        assert data["headers"]["x-signature"] == "test_signature!@#$%^&*()"
