import time

import pytest
from configuration.settings import configuration
from fastapi import HTTPException

from gc_dentist_shared.core.common.hmac import (
    generate_hmac_signature,
    hmac_authentication,
    verify_hmac_signature,
)


class TestHmac:
    def test_generate_hmac_signature(self):
        # Arrange
        message = "test_message"
        secret = "test_secret"  # pragma: allowlist secret

        # Act
        signature = generate_hmac_signature(message, secret)

        # Assert
        assert isinstance(signature, str)
        assert len(signature) == 64  # SHA256 hexdigest length

    def test_verify_hmac_signature_valid(self):
        # Arrange
        message = "test_message"
        secret = configuration.COMMUNICATE_SECRET_KEY
        signature = generate_hmac_signature(message, secret)

        # Act
        result = verify_hmac_signature(signature, message, configuration)

        # Assert
        assert result is True

    def test_verify_hmac_signature_invalid(self):
        # Arrange
        message = "test_message"
        invalid_signature = "invalid_signature"

        # Act
        result = verify_hmac_signature(invalid_signature, message, configuration)

        # Assert
        assert result is False

    def test_hmac_authentication_valid(self):
        # Arrange
        current_time = int(time.time())
        message = str(current_time)
        signature = generate_hmac_signature(
            message, configuration.COMMUNICATE_SECRET_KEY
        )

        # Act & Assert
        hmac_authentication(
            configuration=configuration,
            x_timestamp=str(current_time),
            x_signature=signature,
        )

    def test_hmac_authentication_missing_headers(self):
        # Arrange
        x_timestamp = None
        x_signature = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            hmac_authentication(
                configuration=configuration,
                x_timestamp=x_timestamp,
                x_signature=x_signature,
            )

        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Missing authentication headers"

    # def test_hmac_authentication_expired_timestamp(self):
    #     # Arrange
    #     expired_time = int(time.time()) - 601  # 601 seconds ago
    #     message = str(expired_time)
    #     signature = generate_hmac_signature(
    #         message, configuration.COMMUNICATE_SECRET_KEY
    #     )

    #     # Act & Assert
    #     with pytest.raises(HTTPException) as exc_info:
    #         hmac_authentication(x_timestamp=str(expired_time), x_signature=signature)

    #     assert exc_info.value.status_code == 401
    #     assert exc_info.value.detail == "Request timestamp expired"

    def test_hmac_authentication_invalid_signature(self):
        # Arrange
        current_time = int(time.time())
        invalid_signature = "invalid_signature"

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            hmac_authentication(
                configuration=configuration,
                x_timestamp=str(current_time),
                x_signature=invalid_signature,
            )

        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Invalid HMAC signature"
