from uuid import uuid4

import pytest
from schemas.requests.form_flow_requests import (
    <PERSON><PERSON><PERSON>,
    FormFlowsCreate,
    Form<PERSON><PERSON>Update,
    FormItemCreate,
    FormItemGroupCreate,
    FormItemGroupUpdate,
    FormItemUpdate,
    FormUpdate,
)
from services.form_flows.form_flow_services import BulkEditForm<PERSON>low, FormFlowService
from sqlalchemy import select, update
from starlette import status
from tests.helpers.enums.form_flows import (
    UnittestEditActionEnum,
    UnittestFormFlowType,
    UnittestFormItemFieldType,
    UnittestFormItemSide,
)
from tests.helpers.insert_data.insert_form import (
    insert_form,
    insert_form_flow,
    insert_form_item,
    insert_form_item_group,
)

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models.forms.form_flows import FormFlow
from gc_dentist_shared.tenant_models.forms.form_item_groups import FormItemGroup
from gc_dentist_shared.tenant_models.forms.form_items import FormItem
from gc_dentist_shared.tenant_models.forms.forms import Form


@pytest.mark.asyncio
async def test_validate_create_form_flows_pass(async_tenant_db_session_object):
    form_flow = FormFlowsCreate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        description="desc",
        version="1.0",
        forms=[
            FormCreate(
                form_name="Form A",
                description="desc",
                order_index=0,
                groups=[
                    FormItemGroupCreate(
                        title="Group 1",
                        order_index=0,
                        items=[
                            FormItemCreate(
                                label="Group Item 1",
                                field_type=UnittestFormItemFieldType.TEXT,
                                item_side=UnittestFormItemSide.DOCTOR,
                                required=True,
                                order_index=0,
                            ),
                            FormItemCreate(
                                label="Group Item 1",
                                field_type=UnittestFormItemFieldType.TEXT,
                                item_side=UnittestFormItemSide.DOCTOR,
                                required=True,
                                order_index=1,
                            ),
                        ],
                    )
                ],
                items=[
                    FormItemCreate(
                        label="Single Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        item_side=UnittestFormItemSide.DOCTOR,
                        required=True,
                        order_index=1,
                    ),
                    FormItemCreate(
                        label="Single Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        item_side=UnittestFormItemSide.DOCTOR,
                        required=True,
                        order_index=2,
                    ),
                ],
            )
        ],
    )

    service = FormFlowService(session=async_tenant_db_session_object)
    result = await service._validate_create_form_flows(form_flow)
    assert result is None


@pytest.mark.asyncio
async def test_validate_create_form_flows_duplicate_flow_name_activate(
    async_tenant_db_session_object,
):
    existing_flow_name = str(uuid4())
    async with async_tenant_db_session_object.begin():
        await insert_form_flow(
            async_tenant_db_session_object,
            flow_name=existing_flow_name,
            custom_fields=dict(is_active=True),
        )

    form_flow = FormFlowsCreate(
        flow_name=existing_flow_name,
        flow_type=UnittestFormFlowType.SURVEY,
        description="desc",
        version="1.0",
        forms=[
            FormCreate(
                form_name="Initial Form",
                description="desc",
                order_index=0,
                groups=[],
                items=[
                    FormItemCreate(
                        label="Single Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        item_side=UnittestFormItemSide.DOCTOR,
                        required=True,
                        order_index=1,
                    ),
                ],
            )
        ],
    )

    service = FormFlowService(session=async_tenant_db_session_object)
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_create_form_flows(form_flow)

    assert exc_info.value.status_code == 409
    assert exc_info.value.message_code == 500001


@pytest.mark.asyncio
async def test_validate_create_form_flows_duplicate_flow_name_not_active(
    async_tenant_db_session_object,
):
    existing_flow_name = str(uuid4())
    async with async_tenant_db_session_object.begin():
        await insert_form_flow(
            async_tenant_db_session_object,
            flow_name=existing_flow_name,
            custom_fields=dict(is_active=False),
        )

    form_flow = FormFlowsCreate(
        flow_name=existing_flow_name,
        flow_type=UnittestFormFlowType.SURVEY,
        description="desc",
        version="1.0",
        forms=[
            FormCreate(
                form_name="Initial Form",
                description="desc",
                order_index=0,
                groups=[],
                items=[
                    FormItemCreate(
                        label="Single Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        item_side=UnittestFormItemSide.DOCTOR,
                        required=True,
                        order_index=1,
                    ),
                ],
            )
        ],
    )

    service = FormFlowService(session=async_tenant_db_session_object)
    result = await service._validate_create_form_flows(form_flow)
    assert result is None


@pytest.mark.asyncio
def test_build_bulk_insert_data_full_structure(async_tenant_db_session_object):
    form_flow_uuid = str(uuid4())
    form_flow_name = str(uuid4())
    form_flow_description = str(uuid4())
    form_data = FormFlowsCreate(
        flow_name=form_flow_name,
        flow_type=UnittestFormFlowType.SURVEY,
        description=form_flow_description,
        version="1.0",
        forms=[
            FormCreate(
                form_name="Form A",
                description="desc",
                order_index=0,
                groups=[
                    FormItemGroupCreate(
                        title="Group 1",
                        description="group desc",
                        display_type=1,
                        order_index=0,
                        items=[
                            FormItemCreate(
                                label="Group Item Form A",
                                field_type=UnittestFormItemFieldType.TEXT,
                                item_side=UnittestFormItemSide.DOCTOR,
                                required=True,
                                is_favorite=True,
                                extra_data={"hint": "note"},
                                order_index=0,
                            )
                        ],
                    )
                ],
                items=[
                    FormItemCreate(
                        label="Single Item Form A",
                        field_type=UnittestFormItemFieldType.TEXT,
                        item_side=UnittestFormItemSide.DOCTOR,
                        required=False,
                        is_favorite=False,
                        extra_data=None,
                        order_index=1,
                    )
                ],
            ),
            FormCreate(
                form_name="Form B",
                description="desc",
                order_index=1,
                groups=[],
                items=[
                    FormItemCreate(
                        label="Single Item Form B",
                        field_type=UnittestFormItemFieldType.TEXT,
                        item_side=UnittestFormItemSide.DOCTOR,
                        required=False,
                        is_favorite=False,
                        extra_data=None,
                        order_index=1,
                    )
                ],
            ),
        ],
    )

    service = FormFlowService(session=async_tenant_db_session_object)
    result = service._build_bulk_insert_data(form_data, form_flow_uuid)

    assert isinstance(result, dict)
    assert "form_flow" in result
    assert "forms_bulk" in result
    assert "groups_bulk" in result
    assert "items_bulk" in result

    # Check that the returned form data is correct
    # Form Flow check
    assert result["form_flow"]["uuid"] == form_flow_uuid
    assert result["form_flow"]["flow_name"] == form_flow_name
    assert result["form_flow"]["flow_type"] == UnittestFormFlowType.SURVEY
    assert result["form_flow"]["description"] == form_flow_description
    assert result["form_flow"]["version"] == "1.0"

    assert len(result["forms_bulk"]) == 2
    assert result["forms_bulk"][0]["form_flow_uuid"] == form_flow_uuid
    assert result["forms_bulk"][0]["form_name"] == "Form A"
    assert result["forms_bulk"][1]["form_flow_uuid"] == form_flow_uuid
    assert result["forms_bulk"][1]["form_name"] == "Form B"

    # Group check
    assert len(result["groups_bulk"]) == 1
    assert result["groups_bulk"][0]["title"] == "Group 1"
    assert result["groups_bulk"][0]["form_uuid"] == result["forms_bulk"][0]["uuid"]

    # Items check
    assert len(result["items_bulk"]) == 3
    labels = [item["label"] for item in result["items_bulk"]]
    assert "Group Item Form A" in labels
    assert "Single Item Form A" in labels
    assert "Single Item Form B" in labels


@pytest.mark.asyncio
async def test_insert_form_flow_to_db_manual_dict(
    async_tenant_db_session_object,
):
    form_flow_uuid = str(uuid4())
    form_uuid = str(uuid4())
    group_uuid = str(uuid4())
    item_in_group_uuid = str(uuid4())
    single_item_uuid = str(uuid4())
    flow_name = f"flow-{uuid4()}"

    data_insert = {
        "form_flow": {
            "uuid": form_flow_uuid,
            "flow_name": flow_name,
            "flow_type": UnittestFormFlowType.SURVEY,
            "description": "Manual insert",
            "version": "1.0",
        },
        "forms_bulk": [
            {
                "uuid": form_uuid,
                "form_flow_uuid": form_flow_uuid,
                "form_name": "Test Form",
                "description": "Form description",
                "order_index": 0,
            }
        ],
        "groups_bulk": [
            {
                "uuid": group_uuid,
                "form_uuid": form_uuid,
                "title": "Group Title",
                "description": "Group description",
                "display_type": 1,
                "order_index": 0,
            }
        ],
        "items_bulk": [
            {
                "uuid": item_in_group_uuid,
                "form_uuid": form_uuid,
                "form_item_group_uuid": group_uuid,
                "label": "Item in Group",
                "sub_label": None,
                "field_type": UnittestFormItemFieldType.TEXT,
                "item_side": UnittestFormItemSide.DOCTOR,
                "required": True,
                "is_favorite": False,
                "extra_data": None,
                "order_index": 0,
            },
            {
                "uuid": single_item_uuid,
                "form_uuid": form_uuid,
                "form_item_group_uuid": None,
                "label": "Standalone Item",
                "sub_label": "Sub",
                "field_type": UnittestFormItemFieldType.RADIO,
                "item_side": UnittestFormItemSide.DOCTOR,
                "required": False,
                "is_favorite": True,
                "extra_data": {"options": ["A", "B"]},
                "order_index": 1,
            },
        ],
    }

    service = FormFlowService(session=async_tenant_db_session_object)

    # Act
    await service._insert_form_flow_to_db(data_insert)

    # Assert
    async with async_tenant_db_session_object as db:
        form_flow = await db.scalar(
            select(FormFlow).where(FormFlow.uuid == form_flow_uuid)
        )
        assert form_flow is not None
        assert form_flow.flow_name == flow_name

        form = await db.scalar(select(Form).where(Form.uuid == form_uuid))
        assert form is not None
        assert str(form.form_flow_uuid) == form_flow_uuid

        group = await db.scalar(
            select(FormItemGroup).where(FormItemGroup.uuid == group_uuid)
        )
        assert group is not None
        assert str(group.form_uuid) == form_uuid

        items = (
            (await db.execute(select(FormItem).where(FormItem.form_uuid == form_uuid)))
            .scalars()
            .all()
        )
        assert len(items) == 2
        labels = {item.label for item in items}
        assert "Item in Group" in labels
        assert "Standalone Item" in labels


@pytest.mark.asyncio
async def test_get_detail_form_flow_with_full_structure(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)

        form_uuid_first = await insert_form(
            async_tenant_db_session_object,
            form_flow_uuid,
            custom_fields={"order_index": 0},
        )
        group_uuid_form_first = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid_first
        )
        await insert_form_item(
            async_tenant_db_session_object,
            form_uuid_first,
            form_item_group_uuid=group_uuid_form_first,
        )
        await insert_form_item(
            async_tenant_db_session_object,
            form_uuid_first,
            form_item_group_uuid=group_uuid_form_first,
        )
        await insert_form_item(
            async_tenant_db_session_object, form_uuid_first, form_item_group_uuid=None
        )

        form_uuid_second = await insert_form(
            async_tenant_db_session_object,
            form_flow_uuid,
            custom_fields={"order_index": 1},
        )
        await insert_form_item(
            async_tenant_db_session_object, form_uuid_second, form_item_group_uuid=None
        )
        await insert_form_item(
            async_tenant_db_session_object, form_uuid_second, form_item_group_uuid=None
        )

    service = FormFlowService(session=async_tenant_db_session_object)
    result = await service.get_detail_form_flow(form_flow_uuid)

    assert result["uuid"] == form_flow_uuid
    assert len(result["forms"]) == 2

    form_first = result["forms"][0]
    assert form_first["uuid"] == form_uuid_first
    assert len(form_first["groups"]) == 1
    assert len(form_first["groups"][0]["items"]) == 2
    assert len(form_first["items"]) == 1

    form_second = result["forms"][1]
    assert form_second["uuid"] == form_uuid_second
    assert len(form_second["groups"]) == 0
    assert len(form_second["items"]) == 2


@pytest.mark.asyncio
async def test_get_detail_form_flow_not_found(async_tenant_db_session_object):
    service = FormFlowService(session=async_tenant_db_session_object)
    with pytest.raises(CustomValueError) as e:
        await service.get_detail_form_flow(str(uuid4()))
        assert e.value.status_code == status.HTTP_404_NOT_FOUND
        assert e.value.message_code == 500404


@pytest.mark.asyncio
async def test_get_detail_form_flow_filter_is_active(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        # Step 1: Insert active data
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, form_flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        await insert_form_item(
            async_tenant_db_session_object, form_uuid, form_item_group_uuid=group_uuid
        )
        item_single_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid
        )

    service = FormFlowService(session=async_tenant_db_session_object)
    detail_data = await service.get_detail_form_flow(form_flow_uuid)

    # Step 2: Validate all data is present
    assert detail_data["uuid"] == form_flow_uuid
    assert len(detail_data["forms"]) == 1

    form = detail_data["forms"][0]
    assert form["uuid"] == form_uuid
    assert len(form["groups"]) == 1
    assert len(form["items"]) == 1  # single item

    group = form["groups"][0]
    assert group["uuid"] == group_uuid
    assert len(group["items"]) == 1

    # Step 3: Set inactive single item
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            update(FormItem)
            .where(FormItem.uuid == item_single_uuid)
            .values(is_active=False)
        )
        await async_tenant_db_session_object.commit()

    detail_data_after_item_inactive = await service.get_detail_form_flow(form_flow_uuid)
    assert len(detail_data_after_item_inactive["forms"][0]["items"]) == 0

    # Step 4: Set inactive group
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            update(FormItemGroup)
            .where(FormItemGroup.uuid == group_uuid)
            .values(is_active=False)
        )
        await async_tenant_db_session_object.commit()

    detail_data_after_group_inactive = await service.get_detail_form_flow(
        form_flow_uuid
    )
    assert len(detail_data_after_group_inactive["forms"][0]["groups"]) == 0

    # Step 5: Set inactive form
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            update(Form).where(Form.uuid == form_uuid).values(is_active=False)
        )
        await async_tenant_db_session_object.commit()

    detail_data_after_form_inactive = await service.get_detail_form_flow(form_flow_uuid)
    assert len(detail_data_after_form_inactive["forms"]) == 0


@pytest.mark.asyncio
async def test_build_mapping_success():
    form_uuid_1 = str(uuid4())
    group_uuid_1 = str(uuid4())
    item_in_group_uuid = str(uuid4())
    item_standalone_uuid = str(uuid4())
    form_uuid_2 = str(uuid4())

    db_form_flow_data = {
        "flow_name": "Test Flow",
        "forms": [
            {
                "uuid": form_uuid_1,
                "form_name": "Form 1",
                "groups": [
                    {
                        "uuid": group_uuid_1,
                        "title": "Group 1",
                        "items": [
                            {"uuid": item_in_group_uuid, "label": "Item in Group"}
                        ],
                    }
                ],
                "items": [{"uuid": item_standalone_uuid, "label": "Item Standalone"}],
            },
            {"uuid": form_uuid_2, "form_name": "Form 2", "groups": [], "items": []},
        ],
    }

    service = FormFlowService(session=None)

    result = service.mapping_data_uuid(db_form_flow_data)

    assert isinstance(result, dict)
    assert "db_forms_map" in result
    assert "db_groups_map" in result
    assert "db_items_map" in result

    assert len(result["db_forms_map"]) == 2
    assert len(result["db_groups_map"]) == 1
    assert len(result["db_items_map"]) == 2

    assert form_uuid_1 in result["db_forms_map"]
    assert result["db_forms_map"][form_uuid_1]["form_name"] == "Form 1"

    assert group_uuid_1 in result["db_groups_map"]
    assert result["db_groups_map"][group_uuid_1]["title"] == "Group 1"

    assert item_in_group_uuid in result["db_items_map"]
    assert result["db_items_map"][item_in_group_uuid]["label"] == "Item in Group"
    assert item_standalone_uuid in result["db_items_map"]
    assert result["db_items_map"][item_standalone_uuid]["label"] == "Item Standalone"


@pytest.mark.asyncio
async def test_validate_uuid_exists_and_deletable_pass_with_deletions():
    # Arrange
    form_uuid_1 = str(uuid4())
    group_uuid_1 = str(uuid4())  # Keep
    group_uuid_2 = str(uuid4())  # Deleted
    item_uuid_1 = str(uuid4())  # Keep
    item_uuid_2 = str(uuid4())  # Keep
    item_uuid_3 = str(uuid4())  # Deleted

    data_state = {
        "db_forms_map": {form_uuid_1: {"uuid": form_uuid_1, "is_deletable": True}},
        "db_groups_map": {
            group_uuid_1: {"uuid": group_uuid_1, "is_deletable": True},  # Keep
            group_uuid_2: {"uuid": group_uuid_2, "is_deletable": True},  # Deleted
        },
        "db_items_map": {
            item_uuid_1: {"uuid": item_uuid_1, "is_deletable": True},  # Keep
            item_uuid_2: {"uuid": item_uuid_2, "is_deletable": True},  # keep
            item_uuid_3: {"uuid": item_uuid_3, "is_deletable": True},  # Delete
        },
    }

    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                uuid=form_uuid_1,
                action=UnittestEditActionEnum.UPDATE,
                form_name=str(uuid4()),
                order_index=0,
                groups=[
                    FormItemGroupUpdate(
                        uuid=group_uuid_1,
                        action=UnittestEditActionEnum.UPDATE,
                        title=str(uuid4()),
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                uuid=item_uuid_1,
                                action=UnittestEditActionEnum.UPDATE,
                                label=str(uuid4()),
                                field_type=UnittestFormItemFieldType.TEXT,
                                order_index=0,
                            )
                        ],
                    )
                ],
                items=[
                    FormItemUpdate(
                        uuid=item_uuid_2,
                        action=UnittestEditActionEnum.UPDATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=1,
                    )
                ],
            )
        ],
    )

    service = FormFlowService(session=None)
    delete_data = service._validate_uuid_exists_and_deletable(obj_requests, data_state)

    assert delete_data["deletes_forms_uuids"] == set()
    assert delete_data["deletes_groups_uuids"] == {group_uuid_2}
    assert delete_data["deletes_items_uuids"] == {item_uuid_3}


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "entity, error_code",
    [
        ("form", 500013),
        ("group", 500014),
        ("item", 500015),
    ],
)
async def test_validate_uuid_exists_and_deletable_uuid_not_found(entity, error_code):
    invalid_uuid = str(uuid4())
    data_mapping = {"db_forms_map": {}, "db_groups_map": {}, "db_items_map": {}}

    if entity == "form":
        forms = [
            FormUpdate(
                uuid=invalid_uuid,
                action=UnittestEditActionEnum.UPDATE,
                form_name=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ]
    elif entity == "group":
        forms = [
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=str(uuid4()),
                order_index=0,
                groups=[
                    FormItemGroupUpdate(
                        uuid=invalid_uuid,
                        action=UnittestEditActionEnum.UPDATE,
                        title=str(uuid4()),
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label=str(uuid4()),
                                field_type=UnittestFormItemFieldType.TEXT,
                                order_index=0,
                            )
                        ],
                    )
                ],
                items=[],
            )
        ]
    elif entity == "item":
        forms = [
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        uuid=invalid_uuid,
                        action=UnittestEditActionEnum.UPDATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ]

    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=forms,
    )

    service = FormFlowService(session=None)

    with pytest.raises(CustomValueError) as exc_info:
        service._validate_uuid_exists_and_deletable(obj_requests, data_mapping)

    assert exc_info.value.status_code == 400
    assert exc_info.value.message_code == error_code


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "entity, error_code",
    [
        ("form", 500035),
        ("group", 500036),
        ("item", 500037),
    ],
)
async def test_validate_uuid_exists_and_deletable_uuid_conflict(entity, error_code):
    duplicate_form_uuid = str(uuid4())
    duplicate_group_uuid = str(uuid4())
    duplicate_item_uuid = str(uuid4())

    db_forms_map = {
        duplicate_form_uuid: {
            "uuid": duplicate_form_uuid,
            "is_deletable": True,
            "groups": [],
            "items": [],
        }
    }
    db_groups_map = {
        duplicate_group_uuid: {
            "uuid": duplicate_group_uuid,
            "is_deletable": False,
        }
    }
    db_items_map = {
        duplicate_item_uuid: {
            "uuid": duplicate_item_uuid,
            "is_deletable": False,
        }
    }

    if entity == "form":
        forms = [
            FormUpdate(
                uuid=duplicate_form_uuid,
                action=UnittestEditActionEnum.UPDATE,
                form_name=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            ),
            FormUpdate(
                uuid=duplicate_form_uuid,
                action=UnittestEditActionEnum.UPDATE,
                form_name=str(uuid4()),
                order_index=1,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=1,
                    )
                ],
            ),
        ]
    elif entity == "group":
        forms = [
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=str(uuid4()),
                order_index=0,
                groups=[
                    FormItemGroupUpdate(
                        uuid=duplicate_group_uuid,
                        action=UnittestEditActionEnum.UPDATE,
                        title=str(uuid4()),
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label=str(uuid4()),
                                field_type=UnittestFormItemFieldType.TEXT,
                                order_index=0,
                            )
                        ],
                    )
                ],
                items=[],
            ),
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=str(uuid4()),
                order_index=1,
                groups=[
                    FormItemGroupUpdate(
                        uuid=duplicate_group_uuid,
                        action=UnittestEditActionEnum.UPDATE,
                        title=str(uuid4()),
                        order_index=1,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label=str(uuid4()),
                                field_type=UnittestFormItemFieldType.TEXT,
                                order_index=0,
                            )
                        ],
                    )
                ],
                items=[],
            ),
        ]
    elif entity == "item":
        forms = [
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        uuid=duplicate_item_uuid,
                        action=UnittestEditActionEnum.UPDATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            ),
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=str(uuid4()),
                order_index=1,
                items=[
                    FormItemUpdate(
                        uuid=duplicate_item_uuid,
                        action=UnittestEditActionEnum.UPDATE,
                        label=str(uuid4()),
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=1,
                    )
                ],
            ),
        ]

    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=forms,
    )

    service = FormFlowService(session=None)

    with pytest.raises(CustomValueError) as exc_info:
        service._validate_uuid_exists_and_deletable(
            obj_requests,
            data_mapping={
                "db_forms_map": db_forms_map,
                "db_groups_map": db_groups_map,
                "db_items_map": db_items_map,
            },
        )

    assert exc_info.value.status_code == 409
    assert exc_info.value.message_code == error_code


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "entity, error_code",
    [
        ("form", 500010),
        ("group", 500011),
        ("item", 500012),
    ],
)
async def test_validate_uuid_exists_and_deletable_entity_not_deletable(
    entity, error_code
):
    form_uuid = str(uuid4())
    group_uuid = str(uuid4())
    item_uuid = str(uuid4())

    db_forms_map = {
        form_uuid: {
            "uuid": form_uuid,
            "is_deletable": True,
            "groups": [],
            "items": [],
        }
    }
    db_groups_map = {}
    db_items_map = {}

    forms = []

    if entity == "form":
        db_forms_map[form_uuid]["is_deletable"] = False
        forms = [
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name="Test Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ]
    elif entity == "group":
        db_groups_map[group_uuid] = {
            "uuid": group_uuid,
            "is_deletable": False,
        }
        forms = [
            FormUpdate(
                uuid=form_uuid,
                action=UnittestEditActionEnum.UPDATE,
                form_name=str(uuid4()),
                order_index=0,
                groups=[
                    FormItemGroupUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        title=str(uuid4()),
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label=str(uuid4()),
                                field_type=UnittestFormItemFieldType.TEXT,
                                order_index=0,
                            )
                        ],
                    )
                ],
                items=[],
            )
        ]
    elif entity == "item":
        db_items_map[item_uuid] = {"uuid": item_uuid, "is_deletable": False}
        forms = [
            FormUpdate(
                uuid=form_uuid,
                action=UnittestEditActionEnum.UPDATE,
                form_name="Test Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ]

    obj_requests = FormFlowUpdate(
        flow_name="Test Flow",
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=forms,
    )

    service = FormFlowService(session=None)

    with pytest.raises(CustomValueError) as exc_info:
        service._validate_uuid_exists_and_deletable(
            obj_requests,
            data_mapping={
                "db_forms_map": db_forms_map,
                "db_groups_map": db_groups_map,
                "db_items_map": db_items_map,
            },
        )

    assert exc_info.value.status_code == 400
    assert exc_info.value.message_code == error_code


@pytest.mark.asyncio
async def test_validate_uuid_exists_and_deletable_form_deletion_cascades_to_group_and_item():
    form_uuid = str(uuid4())
    group_uuid = str(uuid4())
    item_uuid_in_group = str(uuid4())
    item_uuid_single = str(uuid4())

    data_mapping = {
        "db_forms_map": {
            form_uuid: {
                "uuid": form_uuid,
                "is_deletable": True,
                "groups": [
                    {
                        "uuid": group_uuid,
                        "is_deletable": True,
                        "items": [{"uuid": item_uuid_in_group, "is_deletable": True}],
                    }
                ],
                "items": [{"uuid": item_uuid_single, "is_deletable": True}],
            }
        },
        "db_groups_map": {
            group_uuid: {
                "uuid": group_uuid,
                "is_deletable": True,
                "items": [{"uuid": item_uuid_in_group, "is_deletable": True}],
            }
        },
        "db_items_map": {
            item_uuid_in_group: {"uuid": item_uuid_in_group, "is_deletable": True},
            item_uuid_single: {"uuid": item_uuid_single, "is_deletable": True},
        },
    }

    obj_requests = FormFlowUpdate(
        flow_name="Flow A",
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[  # Old Form is removed from the request
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name="Test Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ],
    )

    service = FormFlowService(session=None)
    result = service._validate_uuid_exists_and_deletable(obj_requests, data_mapping)

    assert result["deletes_forms_uuids"] == {form_uuid}
    assert result["deletes_groups_uuids"] == {group_uuid}
    assert result["deletes_items_uuids"] == {item_uuid_in_group, item_uuid_single}


@pytest.mark.asyncio
async def test_validate_uuid_exists_and_deletable_group_deletion_cascades_to_item():
    """
    Tests that when a group is deleted, its child items are also added to the deletion set.
    """
    form_uuid = str(uuid4())
    group_uuid_to_delete = str(uuid4())
    item_uuid_in_deleted_group = str(uuid4())

    data_mapping = {
        "db_forms_map": {form_uuid: {"uuid": form_uuid, "is_deletable": True}},
        "db_groups_map": {
            group_uuid_to_delete: {
                "uuid": group_uuid_to_delete,
                "is_deletable": True,
                "items": [{"uuid": item_uuid_in_deleted_group, "is_deletable": True}],
            }
        },
        "db_items_map": {
            item_uuid_in_deleted_group: {
                "uuid": item_uuid_in_deleted_group,
                "is_deletable": True,
            }
        },
    }

    # The request only contains the form, implicitly deleting the group.
    obj_requests = FormFlowUpdate(
        flow_name="Test Flow",
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                uuid=form_uuid,
                action=UnittestEditActionEnum.UPDATE,
                form_name="Updated Form",
                order_index=0,
                groups=[],  # Group is removed from the request
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ],
    )

    service = FormFlowService(session=None)
    result = service._validate_uuid_exists_and_deletable(obj_requests, data_mapping)

    # Assert that both the group and its child item are marked for deletion.
    assert result["deletes_groups_uuids"] == {group_uuid_to_delete}
    assert result["deletes_items_uuids"] == {item_uuid_in_deleted_group}
    assert result["deletes_forms_uuids"] == set()


@pytest.mark.asyncio
async def test_validate_edit_form_flow_name_success(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, form_flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        item_uuid_in_group = await insert_form_item(
            async_tenant_db_session_object, form_uuid, form_item_group_uuid=group_uuid
        )
        item_uuid_single = await insert_form_item(
            async_tenant_db_session_object, form_uuid
        )

    # Object request has no old data -> remove data
    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name="Test Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ],
    )
    service = FormFlowService(session=async_tenant_db_session_object)
    result = await service._validate_edit_form_flow(obj_requests, form_flow_uuid)

    assert result["deletes_forms_uuids"] == {form_uuid}
    assert result["deletes_groups_uuids"] == {group_uuid}
    assert result["deletes_items_uuids"] == {item_uuid_in_group, item_uuid_single}


@pytest.mark.asyncio
async def test_validate_edit_form_flow_name_not_found(async_tenant_db_session_object):
    service = FormFlowService(session=async_tenant_db_session_object)
    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name="Test Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ],
    )

    # Send with custom form_flow_uuid
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_edit_form_flow(
            obj_requests, form_flow_uuid=str(uuid4())
        )

    assert exc_info.value.status_code == 404
    assert exc_info.value.message_code == 500404


@pytest.mark.asyncio
async def test_validate_edit_form_flow_name_conflict_should_raise(
    async_tenant_db_session_object,
):
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        exists_flow_name = str(uuid4())
        await insert_form_flow(
            async_tenant_db_session_object, flow_name=exists_flow_name
        )

    # Update flow_name duplicate in database
    obj_requests = FormFlowUpdate(
        flow_name=exists_flow_name,
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name="Test Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Test Item",
                        field_type=UnittestFormItemFieldType.TEXT,
                        order_index=0,
                    )
                ],
            )
        ],
    )
    service = FormFlowService(session=async_tenant_db_session_object)
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_edit_form_flow(obj_requests, form_flow_uuid)

    assert exc_info.value.status_code == 409
    assert exc_info.value.message_code == 500001


@pytest.mark.parametrize(
    "action, is_in_group, target_list_name",
    [
        (UnittestEditActionEnum.CREATE, False, "inserts_items"),
        (UnittestEditActionEnum.CREATE, True, "inserts_items"),
        (UnittestEditActionEnum.UPDATE, False, "updates_items"),
        (UnittestEditActionEnum.UPDATE, True, "updates_items"),
    ],
)
def test_seperator_data_item(action, is_in_group, target_list_name):
    """
    Tests the _seperator_data_item function for all CREATE/UPDATE and standalone/in-group scenarios.
    """
    # Arrange
    service = FormFlowService(session=None)
    data_edit_form_flow = BulkEditFormFlow()
    parent_form_uuid = str(uuid4())
    item_uuid = str(uuid4()) if action == UnittestEditActionEnum.UPDATE else None
    parent_group_uuid = str(uuid4()) if is_in_group else None

    item_data_dict = {
        "action": action,
        "label": str(uuid4()),
        "field_type": UnittestFormItemFieldType.TEXT,
        "order_index": 0,
    }
    if item_uuid:
        item_data_dict["uuid"] = item_uuid

    item_data = FormItemUpdate(**item_data_dict)

    # Act
    service._seperator_data_item(
        data_edit_form_flow, item_data, parent_form_uuid, parent_group_uuid
    )

    # Assert
    target_list = getattr(data_edit_form_flow, target_list_name)
    assert len(target_list) == 1

    processed_item = target_list[0]
    assert processed_item.get("form_uuid") == parent_form_uuid
    assert processed_item.get("form_item_group_uuid") == parent_group_uuid

    if action == UnittestEditActionEnum.CREATE:
        assert "uuid" in processed_item
        assert len(data_edit_form_flow.updates_items) == 0
    elif action == UnittestEditActionEnum.UPDATE:
        assert processed_item.get("uuid") == item_uuid
        assert len(data_edit_form_flow.inserts_items) == 0


def test_seperator_data_form_with_create_group_and_item():
    """
    Tests processing a new group which contains a new item.
    Ensures the new group's UUID is correctly passed to its child item.
    """
    # Arrange
    service = FormFlowService(session=None)
    bulk_data = BulkEditFormFlow()
    parent_form_uuid = str(uuid4())
    form_data = FormUpdate(
        uuid=parent_form_uuid,
        form_name=str(uuid4()),
        order_index=0,
        action=UnittestEditActionEnum.UPDATE,
        groups=[
            FormItemGroupUpdate(
                action=UnittestEditActionEnum.CREATE,
                title=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=str(uuid4()),
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            )
        ],
        items=[],
    )

    # Act
    service._seperator_data_form(bulk_data, parent_form_uuid, form_data)

    # Assert
    assert len(bulk_data.inserts_groups) == 1
    assert len(bulk_data.inserts_items) == 1

    inserted_group = bulk_data.inserts_groups[0]
    assert inserted_group["form_uuid"] == parent_form_uuid
    assert "uuid" in inserted_group

    inserted_item = bulk_data.inserts_items[0]
    assert inserted_item["form_uuid"] == parent_form_uuid
    assert inserted_item["form_item_group_uuid"] == inserted_group["uuid"]


def test_seperator_data_form_with_update_group_and_item():
    """
    Tests processing an existing group which contains an existing item.
    Ensures the existing group's UUID is correctly passed to its child item.
    """
    # Arrange
    service = FormFlowService(session=None)
    bulk_data = BulkEditFormFlow()
    parent_form_uuid = str(uuid4())
    group_uuid = str(uuid4())
    item_uuid = str(uuid4())
    form_data = FormUpdate(
        uuid=parent_form_uuid,
        form_name=str(uuid4()),
        order_index=0,
        action=UnittestEditActionEnum.UPDATE,
        groups=[
            FormItemGroupUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=group_uuid,
                title=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=item_uuid,
                        label=str(uuid4()),
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            )
        ],
        items=[],
    )

    # Act
    service._seperator_data_form(bulk_data, parent_form_uuid, form_data)

    # Assert
    assert len(bulk_data.updates_groups) == 1
    assert len(bulk_data.updates_items) == 1
    assert not bulk_data.inserts_groups
    assert not bulk_data.inserts_items

    updated_group = bulk_data.updates_groups[0]
    assert updated_group["uuid"] == group_uuid

    updated_item = bulk_data.updates_items[0]
    assert updated_item["uuid"] == item_uuid
    assert updated_item["form_item_group_uuid"] == group_uuid


def test_seperator_data_form_with_standalone_items_only():
    """
    Tests processing a form that only contains standalone items (no groups).
    """
    # Arrange
    service = FormFlowService(session=None)
    bulk_data = BulkEditFormFlow()
    parent_form_uuid = str(uuid4())
    item_to_update_uuid = str(uuid4())
    form_data = FormUpdate(
        uuid=parent_form_uuid,
        form_name=str(uuid4()),
        order_index=0,
        action=UnittestEditActionEnum.UPDATE,
        groups=[],
        items=[
            FormItemUpdate(
                action=UnittestEditActionEnum.CREATE,
                label=str(uuid4()),
                order_index=0,
                field_type=UnittestFormItemFieldType.TEXT,
            ),
            FormItemUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=item_to_update_uuid,
                label=str(uuid4()),
                order_index=1,
                field_type=UnittestFormItemFieldType.TEXT,
            ),
        ],
    )

    # Act
    service._seperator_data_form(bulk_data, parent_form_uuid, form_data)

    # Assert
    assert not bulk_data.inserts_groups and not bulk_data.updates_groups
    assert len(bulk_data.inserts_items) == 1
    assert len(bulk_data.updates_items) == 1

    assert bulk_data.inserts_items[0]["form_item_group_uuid"] is None
    assert bulk_data.updates_items[0]["uuid"] == item_to_update_uuid
    assert bulk_data.updates_items[0]["form_item_group_uuid"] is None


def test_seperator_data_form_with_mixed_actions_and_structure():
    """
    A comprehensive integration test for the function with a mix of all possible actions.
    This version includes items within groups to satisfy validation rules.
    """
    # Arrange
    service = FormFlowService(session=None)
    bulk_data = BulkEditFormFlow()
    parent_form_uuid = str(uuid4())
    group_to_update_uuid = str(uuid4())
    item_in_group_to_update_uuid = str(uuid4())
    standalone_item_to_update_uuid = str(uuid4())

    form_data = FormUpdate(
        uuid=parent_form_uuid,
        form_name=str(uuid4()),
        order_index=0,
        action=UnittestEditActionEnum.UPDATE,
        groups=[
            # CREATE Group must contain at least one item
            FormItemGroupUpdate(
                action=UnittestEditActionEnum.CREATE,
                title=str(uuid4()),
                order_index=0,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=str(uuid4()),
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
            # UPDATE Group must contain at least one item
            FormItemGroupUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=group_to_update_uuid,
                title=str(uuid4()),
                order_index=1,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=item_in_group_to_update_uuid,
                        label=str(uuid4()),
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
        ],
        items=[
            # Standalone items
            FormItemUpdate(
                action=UnittestEditActionEnum.CREATE,
                label=str(uuid4()),
                order_index=2,
                field_type=UnittestFormItemFieldType.TEXT,
            ),
            FormItemUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=standalone_item_to_update_uuid,
                label=str(uuid4()),
                order_index=3,
                field_type=UnittestFormItemFieldType.TEXT,
            ),
        ],
    )

    # Act
    service._seperator_data_form(bulk_data, parent_form_uuid, form_data)

    # Assert
    # Check groups
    assert len(bulk_data.inserts_groups) == 1
    assert len(bulk_data.updates_groups) == 1
    assert "uuid" in bulk_data.inserts_groups[0]
    assert bulk_data.updates_groups[0]["uuid"] == group_to_update_uuid

    # Check items: 1 standalone CREATE + 1 in-group CREATE = 2
    assert len(bulk_data.inserts_items) == 2
    # Check items: 1 standalone UPDATE + 1 in-group UPDATE = 2
    assert len(bulk_data.updates_items) == 2

    # Verify one of the updated items to ensure correctness
    updated_item_in_group = next(
        item
        for item in bulk_data.updates_items
        if item["uuid"] == item_in_group_to_update_uuid
    )
    assert updated_item_in_group["form_item_group_uuid"] == group_to_update_uuid


def test_seperator_edit_data_form_flow_with_create_form():
    """
    Tests processing a request that creates a new form with its own children.
    Ensures the new form gets a new UUID and is linked to the parent flow.
    """
    # Arrange
    service = FormFlowService(session=None)
    flow_uuid = str(uuid4())
    form_name = str(uuid4())
    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=form_name,
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=str(uuid4()),
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            )
        ],
    )

    # Act
    result = service._seperator_edit_data_form_flow(obj_requests, flow_uuid)

    # Assert
    assert len(result.inserts_forms) == 1
    assert len(result.inserts_items) == 1
    assert not result.updates_forms

    inserted_form = result.inserts_forms[0]
    assert inserted_form["form_name"] == form_name
    assert "uuid" in inserted_form
    assert inserted_form["form_flow_uuid"] == flow_uuid

    inserted_item = result.inserts_items[0]
    # The item's parent form_uuid must be the newly generated one
    assert inserted_item["form_uuid"] == inserted_form["uuid"]


def test_seperator_edit_data_form_flow_with_update_form():
    """
    Tests processing a request that updates an existing form.
    Ensures the existing form's UUID is used for its children.
    """
    # Arrange
    service = FormFlowService(session=None)
    flow_uuid = str(uuid4())
    form_to_update_uuid = str(uuid4())
    obj_requests = FormFlowUpdate(
        flow_name="Flow with Updated Form",
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_to_update_uuid,
                form_name="An Updated Form",
                order_index=0,
                groups=[],
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="New Item in Updated Form",
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            )
        ],
    )

    # Act
    result = service._seperator_edit_data_form_flow(obj_requests, flow_uuid)

    # Assert
    assert len(result.updates_forms) == 1
    assert len(result.inserts_items) == 1
    assert not result.inserts_forms

    updated_form = result.updates_forms[0]
    assert updated_form["uuid"] == form_to_update_uuid
    assert updated_form["form_name"] == "An Updated Form"

    inserted_item = result.inserts_items[0]
    # The item's parent form_uuid must be the existing one
    assert inserted_item["form_uuid"] == form_to_update_uuid


def test_seperator_edit_data_form_flow_with_mixed_actions_integration():
    """
    A full integration test with a mix of creating and updating forms,
    groups, and items to test all logic branches.
    """
    # Arrange
    service = FormFlowService(session=None)
    flow_uuid = str(uuid4())
    form_to_update_uuid = str(uuid4())
    group_to_update_uuid = str(uuid4())
    item_to_update_uuid = str(uuid4())

    obj_requests = FormFlowUpdate(
        flow_name="Complex Flow Update",
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            # Form 1: An existing form to update
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_to_update_uuid,
                form_name="Updated Form",
                order_index=0,
                groups=[
                    FormItemGroupUpdate(  # Create a new group inside the updated form
                        action=UnittestEditActionEnum.CREATE,
                        title="New Group",
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label="Item in new group",
                                order_index=0,
                                field_type=UnittestFormItemFieldType.TEXT,
                            )
                        ],
                    )
                ],
                items=[  # Update a standalone item inside the updated form
                    FormItemUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=item_to_update_uuid,
                        label="Updated item",
                        order_index=1,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
            # Form 2: A brand new form to create
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name="New Form",
                order_index=1,
                groups=[  # Update a group (this shouldn't happen in a new form, but tests logic)
                    FormItemGroupUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=group_to_update_uuid,
                        title="Group in new form",
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label="Item in another group",
                                order_index=0,
                                field_type=UnittestFormItemFieldType.TEXT,
                            )
                        ],
                    )
                ],
                items=[],
            ),
        ],
    )

    # Act
    result = service._seperator_edit_data_form_flow(obj_requests, flow_uuid)

    # Assert
    # Check top-level flow data
    assert result.update_form_flow["flow_name"] == "Complex Flow Update"

    # Check forms
    assert len(result.updates_forms) == 1
    assert result.updates_forms[0]["uuid"] == form_to_update_uuid
    assert len(result.inserts_forms) == 1
    assert result.inserts_forms[0]["form_flow_uuid"] == flow_uuid

    # Check groups
    assert len(result.inserts_groups) == 1  # From the updated form
    assert result.inserts_groups[0]["form_uuid"] == form_to_update_uuid
    assert len(result.updates_groups) == 1  # From the new form
    assert result.updates_groups[0]["uuid"] == group_to_update_uuid

    # Check items
    assert len(result.inserts_items) == 2  # One from each form's group
    assert len(result.updates_items) == 1  # The standalone one in the updated form
    assert result.updates_items[0]["uuid"] == item_to_update_uuid


@pytest.mark.asyncio
async def test_execute_bulk_edit_form_flow_delete_operation(
    async_tenant_db_session_object,
):
    """
    Tests that entities are correctly soft-deleted in the database.
    """
    # Arrange
    service = FormFlowService(session=async_tenant_db_session_object)
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_to_delete_uuid = await insert_form(
            async_tenant_db_session_object, form_flow_uuid
        )

    data_delete = {"deletes_forms_uuids": {form_to_delete_uuid}}
    empty_edit_data = BulkEditFormFlow()

    # Act
    await service._execute_bulk_edit_form_flow(
        form_flow_uuid, empty_edit_data, data_delete
    )

    # Assert
    async with async_tenant_db_session_object.begin():
        deleted_form = (
            await async_tenant_db_session_object.execute(
                select(Form).where(Form.uuid == form_to_delete_uuid)
            )
        ).scalar_one_or_none()
        assert deleted_form is not None
        assert deleted_form.is_active is False
        assert deleted_form.deleted_at is not None


@pytest.mark.asyncio
async def test_execute_bulk_edit_form_flow_insert_operation(
    async_tenant_db_session_object,
):
    """
    Tests that new entities are correctly inserted into the database.
    """
    # Arrange
    service = FormFlowService(session=async_tenant_db_session_object)
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)

    empty_delete_data = {}
    data_edit_form_flow = BulkEditFormFlow()
    new_form_uuid = str(uuid4())
    data_edit_form_flow.inserts_forms = [
        {
            "uuid": new_form_uuid,
            "form_flow_uuid": form_flow_uuid,
            "form_name": "Newly Inserted Form",
            "order_index": 1,
        }
    ]

    # Act
    await service._execute_bulk_edit_form_flow(
        form_flow_uuid, data_edit_form_flow, empty_delete_data
    )

    # Assert
    async with async_tenant_db_session_object.begin():
        inserted_form = (
            await async_tenant_db_session_object.execute(
                select(Form).where(Form.uuid == new_form_uuid)
            )
        ).scalar_one_or_none()
        assert inserted_form is not None
        assert inserted_form.form_name == "Newly Inserted Form"


@pytest.mark.asyncio
async def test_execute_bulk_edit_form_flow_update_operation(
    async_tenant_db_session_object,
):
    """
    Tests that existing entities are correctly updated in the database.
    """
    # Arrange
    service = FormFlowService(session=async_tenant_db_session_object)
    new_flow_name = f"Updated Flow Name {uuid4()}"
    new_form_name = f"Updated Form Name {uuid4()}"

    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_to_update_uuid = await insert_form(
            async_tenant_db_session_object, form_flow_uuid
        )

    empty_delete_data = {}
    data_edit_form_flow = BulkEditFormFlow()
    data_edit_form_flow.update_form_flow = {"flow_name": new_flow_name}
    data_edit_form_flow.updates_forms = [
        {"uuid": form_to_update_uuid, "form_name": new_form_name}
    ]

    # Act
    await service._execute_bulk_edit_form_flow(
        form_flow_uuid, data_edit_form_flow, empty_delete_data
    )

    # Assert
    async with async_tenant_db_session_object.begin():
        # Check FormFlow update
        updated_flow = (
            await async_tenant_db_session_object.execute(
                select(FormFlow).where(FormFlow.uuid == form_flow_uuid)
            )
        ).scalar_one()
        assert updated_flow.flow_name == new_flow_name

        # Check Form update
        updated_form = (
            await async_tenant_db_session_object.execute(
                select(Form).where(Form.uuid == form_to_update_uuid)
            )
        ).scalar_one()
        assert updated_form.form_name == new_form_name


@pytest.mark.asyncio
async def test_execute_bulk_edit_form_flow_full_integration_all_entities(
    async_tenant_db_session_object,
):
    """
    Tests a full scenario with DELETE, INSERT, and UPDATE operations together
    for all entity types: Form, FormItemGroup, and FormItem.
    """
    # Arrange
    service = FormFlowService(session=async_tenant_db_session_object)

    # 1. Setup initial state in DB for all entities
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)

        # Entities to be UPDATED
        form_to_update_uuid = await insert_form(
            async_tenant_db_session_object, form_flow_uuid
        )
        group_to_update_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_to_update_uuid
        )
        item_to_update_uuid = await insert_form_item(
            async_tenant_db_session_object, form_to_update_uuid, group_to_update_uuid
        )

        # Entities to be DELETED
        form_to_delete_uuid = await insert_form(
            async_tenant_db_session_object, form_flow_uuid
        )
        group_to_delete_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_to_update_uuid
        )
        item_to_delete_uuid = await insert_form_item(
            async_tenant_db_session_object, form_to_update_uuid, group_to_update_uuid
        )

    # 2. Prepare data for all operations
    # Data for deletion
    data_delete = {
        "deletes_forms_uuids": {form_to_delete_uuid},
        "deletes_groups_uuids": {group_to_delete_uuid},
        "deletes_items_uuids": {item_to_delete_uuid},
    }

    # Data for edits and inserts
    data_edit_form_flow = BulkEditFormFlow()
    new_form_uuid = str(uuid4())
    new_group_uuid = str(uuid4())
    new_item_uuid = str(uuid4())

    # UPDATE data
    data_edit_form_flow.update_form_flow = {"flow_name": "Final Flow Name"}
    data_edit_form_flow.updates_forms = [
        {"uuid": form_to_update_uuid, "form_name": "Form Was Updated"}
    ]
    data_edit_form_flow.updates_groups = [
        {"uuid": group_to_update_uuid, "title": "Group Was Updated"}
    ]
    data_edit_form_flow.updates_items = [
        {"uuid": item_to_update_uuid, "label": "Item Was Updated"}
    ]

    # INSERT data
    data_edit_form_flow.inserts_forms = [
        {
            "uuid": new_form_uuid,
            "form_flow_uuid": form_flow_uuid,
            "form_name": "New Form",
            "order_index": 2,
        }
    ]
    data_edit_form_flow.inserts_groups = [
        {
            "uuid": new_group_uuid,
            "form_uuid": form_to_update_uuid,
            "title": "New Group",
            "order_index": 2,
        }
    ]
    data_edit_form_flow.inserts_items = [
        {
            "uuid": new_item_uuid,
            "form_uuid": form_to_update_uuid,
            "form_item_group_uuid": group_to_update_uuid,
            "label": "New Item",
            "order_index": 2,
            "field_type": "TEXT",
        }
    ]

    # Act
    await service._execute_bulk_edit_form_flow(
        form_flow_uuid, data_edit_form_flow, data_delete
    )

    # Assert - Verify final state of the database
    async with async_tenant_db_session_object:
        # Assert Deletions
        assert (
            await async_tenant_db_session_object.get(Form, form_to_delete_uuid)
        ).is_active is False
        assert (
            await async_tenant_db_session_object.get(
                FormItemGroup, group_to_delete_uuid
            )
        ).is_active is False
        assert (
            await async_tenant_db_session_object.get(FormItem, item_to_delete_uuid)
        ).is_active is False

        # Assert Inserts
        assert (
            await async_tenant_db_session_object.get(Form, new_form_uuid)
        ) is not None
        assert (
            await async_tenant_db_session_object.get(FormItemGroup, new_group_uuid)
        ) is not None
        assert (
            await async_tenant_db_session_object.get(FormItem, new_item_uuid)
        ) is not None

        # Assert Updates
        assert (
            await async_tenant_db_session_object.get(FormFlow, form_flow_uuid)
        ).flow_name == "Final Flow Name"
        assert (
            await async_tenant_db_session_object.get(Form, form_to_update_uuid)
        ).form_name == "Form Was Updated"
        assert (
            await async_tenant_db_session_object.get(
                FormItemGroup, group_to_update_uuid
            )
        ).title == "Group Was Updated"
        assert (
            await async_tenant_db_session_object.get(FormItem, item_to_update_uuid)
        ).label == "Item Was Updated"


@pytest.mark.asyncio
async def test_edit_form_flow_moving_group_between_forms(
    async_tenant_db_session_object,
):
    """
    Tests moving a group from one form to another, which should be handled
    as a soft-delete of the old group and a create of a new one.
    """
    # 1. ARRANGE: Setup initial state with a group in form_A
    service = FormFlowService(session=async_tenant_db_session_object)
    group_title_to_move = f"Movable Group {uuid4()}"

    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_a_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)
        form_b_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)

        # This group starts in form_A
        group_to_move_uuid = await insert_form_item_group(
            async_tenant_db_session_object,
            form_a_uuid,
            custom_fields=dict(title=group_title_to_move),
        )

    # 2. ARRANGE: Prepare payload where the group is removed from form_A and created in form_B
    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            # Form A no longer has the group
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_a_uuid,
                form_name="Form A",
                order_index=0,
                groups=[],  # Group is removed here
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Item in moved group",
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
            # Form B now contains a new group with the same title
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_b_uuid,
                form_name="Form B",
                order_index=1,
                groups=[
                    FormItemGroupUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=group_to_move_uuid,
                        title=group_title_to_move,
                        order_index=0,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label="Item in moved group",
                                order_index=0,
                                field_type=UnittestFormItemFieldType.TEXT,
                            )
                        ],
                    )
                ],
                items=[],
            ),
        ],
    )

    # 3. ACT: Call the main public method
    await service.edit_form_flow(obj_requests, flow_uuid)

    # 4. ASSERT: Verify the old group is deleted and a new one exists in form_B
    async with async_tenant_db_session_object:
        # Assert a new group with the same title now exists and belongs to form_B
        res = await async_tenant_db_session_object.execute(
            select(FormItemGroup).where(
                FormItemGroup.title == group_title_to_move,
                FormItemGroup.form_uuid == form_b_uuid,
                FormItemGroup.is_active.is_(True),
            )
        )
        assert res.scalar_one_or_none() is not None


@pytest.mark.asyncio
async def test_edit_form_flow_moving_item_between_forms(
    async_tenant_db_session_object,
):
    """
    Tests moving a standalone item from one form to another by re-parenting it
    via an UPDATE action.
    """
    # 1. ARRANGE: Setup initial state with an item in form_A
    service = FormFlowService(session=async_tenant_db_session_object)
    item_label_to_move = f"Movable Item {uuid4()}"

    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_a_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)
        form_b_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)

        # This item starts as a standalone item in form_A
        item_to_move_uuid = await insert_form_item(
            async_tenant_db_session_object,
            form_a_uuid,
            custom_fields=dict(label=item_label_to_move),
        )

    # 2. ARRANGE: Prepare payload where the item is removed from form_A and updated in form_B
    obj_requests = FormFlowUpdate(
        flow_name=str(uuid4()),
        flow_type=UnittestFormFlowType.SURVEY,
        version="1.0",
        forms=[
            # Form A no longer has the item
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_a_uuid,
                form_name="Form A",
                order_index=0,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label="Item in moved group",
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],  # Item is removed from here
            ),
            # Form B now contains the original item, but as an update
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_b_uuid,
                form_name="Form B",
                order_index=1,
                items=[
                    FormItemUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=item_to_move_uuid,
                        label=item_label_to_move,  # Label can be updated or stay the same
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
        ],
    )

    # 3. ACT: Call the main public method
    await service.edit_form_flow(obj_requests, flow_uuid)

    # 4. ASSERT: Verify the item's parent form has changed
    async with async_tenant_db_session_object:
        # Get the item by its original, unchanged UUID
        moved_item = await async_tenant_db_session_object.get(
            FormItem, item_to_move_uuid
        )

        # Assert the item still exists and is active
        assert moved_item is not None
        assert moved_item.is_active is True

        # Assert its parent form is now form_B
        assert str(moved_item.form_uuid) == form_b_uuid


@pytest.mark.asyncio
async def test_edit_form_flow_end_to_end_full_coverage(
    async_tenant_db_session_object,
):
    """
    Tests the entire edit_form_flow process with a full combination of
    DELETE, UPDATE, and INSERT actions for all entity types (Form, Group, Item).
    """
    # 1. ARRANGE: Setup a comprehensive initial database state
    service = FormFlowService(session=async_tenant_db_session_object)
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)

        # Entities for UPDATE
        form_to_update_uuid = await insert_form(
            async_tenant_db_session_object, flow_uuid
        )
        group_to_update_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_to_update_uuid
        )
        item_to_update_uuid = await insert_form_item(
            async_tenant_db_session_object, form_to_update_uuid
        )

        # Entities for DELETE
        form_to_delete_uuid = await insert_form(
            async_tenant_db_session_object, flow_uuid
        )
        group_to_delete_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_to_update_uuid
        )
        item_to_delete_uuid = await insert_form_item(
            async_tenant_db_session_object, form_to_update_uuid
        )

    # 2. ARRANGE: Prepare unique data for the request payload
    # --- Data for UPDATE operations ---
    updated_flow_name = str(uuid4())
    updated_form_name = str(uuid4())
    updated_group_title = str(uuid4())
    updated_item_label = str(uuid4())
    # --- Data for INSERT operations ---
    inserted_form_name = str(uuid4())
    inserted_group_title = str(uuid4())
    inserted_item_label = str(uuid4())

    # 3. ARRANGE: Prepare the request payload
    obj_requests = FormFlowUpdate(
        flow_name=updated_flow_name,
        flow_type=UnittestFormFlowType.SURVEY,
        version="2.0",
        forms=[
            # UPDATE Form
            FormUpdate(
                action=UnittestEditActionEnum.UPDATE,
                uuid=form_to_update_uuid,
                form_name=updated_form_name,
                order_index=0,
                groups=[
                    # INSERT Group
                    FormItemGroupUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        title=inserted_group_title,
                        order_index=1,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label=str(uuid4()),
                                order_index=0,
                                field_type=UnittestFormItemFieldType.TEXT,
                            )
                        ],
                    ),
                    # UPDATE Group
                    FormItemGroupUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=group_to_update_uuid,
                        title=updated_group_title,
                        order_index=2,
                        items=[
                            FormItemUpdate(
                                action=UnittestEditActionEnum.CREATE,
                                label=str(uuid4()),
                                order_index=0,
                                field_type=UnittestFormItemFieldType.TEXT,
                            )
                        ],
                    ),
                ],
                items=[
                    # UPDATE Item
                    FormItemUpdate(
                        action=UnittestEditActionEnum.UPDATE,
                        uuid=item_to_update_uuid,
                        label=updated_item_label,
                        order_index=3,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
            # INSERT Form
            FormUpdate(
                action=UnittestEditActionEnum.CREATE,
                form_name=inserted_form_name,
                order_index=1,
                items=[
                    # INSERT Item
                    FormItemUpdate(
                        action=UnittestEditActionEnum.CREATE,
                        label=inserted_item_label,
                        order_index=0,
                        field_type=UnittestFormItemFieldType.TEXT,
                    )
                ],
            ),
        ],
    )

    # 4. ACT: Call the main public method
    await service.edit_form_flow(obj_requests, flow_uuid)

    # 5. ASSERT: Query the database to verify the final state
    async with async_tenant_db_session_object:
        # --- Assert DELETION ---
        assert (
            await async_tenant_db_session_object.get(Form, form_to_delete_uuid)
        ).is_active is False

        assert (
            await async_tenant_db_session_object.get(
                FormItemGroup, group_to_delete_uuid
            )
        ).is_active is False

        assert (
            await async_tenant_db_session_object.get(FormItem, item_to_delete_uuid)
        ).is_active is False

        # --- Assert UPDATE ---
        assert (
            await async_tenant_db_session_object.get(FormFlow, flow_uuid)
        ).flow_name == updated_flow_name

        assert (
            await async_tenant_db_session_object.get(Form, form_to_update_uuid)
        ).form_name == updated_form_name

        assert (
            await async_tenant_db_session_object.get(
                FormItemGroup, group_to_update_uuid
            )
        ).title == updated_group_title

        assert (
            await async_tenant_db_session_object.get(FormItem, item_to_update_uuid)
        ).label == updated_item_label

        # --- Assert INSERT ---
        assert (
            await async_tenant_db_session_object.execute(
                select(Form).where(Form.form_name == inserted_form_name)
            )
        ).scalar_one_or_none() is not None
        assert (
            await async_tenant_db_session_object.execute(
                select(FormItemGroup).where(FormItemGroup.title == inserted_group_title)
            )
        ).scalar_one_or_none() is not None
        assert (
            await async_tenant_db_session_object.execute(
                select(FormItem).where(FormItem.label == inserted_item_label)
            )
        ).scalar_one_or_none() is not None


@pytest.mark.asyncio
async def test_get_and_validate_data_delete_form_flow_success(
    async_tenant_db_session_object,
):
    """
    Tests the success case where the form flow is valid for deletion
    and the correct child UUIDs are collected.
    """
    # 1. ARRANGE: Setup a comprehensive database state
    service = FormFlowService(session=async_tenant_db_session_object)
    async with async_tenant_db_session_object.begin():
        # Create a deletable flow
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)

        # Create active children that should be collected
        form_uuid_1 = await insert_form(async_tenant_db_session_object, flow_uuid)
        form_uuid_2 = await insert_form(async_tenant_db_session_object, flow_uuid)
        group_uuid_1 = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid_1
        )
        item_uuid_1 = await insert_form_item(
            async_tenant_db_session_object, form_uuid_1, group_uuid_1
        )
        item_uuid_2 = await insert_form_item(
            async_tenant_db_session_object, form_uuid_2
        )

        # Create inactive children that should be ignored
        await insert_form(
            async_tenant_db_session_object,
            flow_uuid,
            custom_fields=dict(is_active=False),
        )
        await insert_form_item(
            async_tenant_db_session_object,
            form_uuid_2,
            custom_fields=dict(is_active=False),
        )

    # 2. ACT: Call the function
    result = await service._get_and_validate_data_delete_form_flow(str(flow_uuid))

    # 3. ASSERT: Verify the collected UUIDs
    assert "deletes_forms_uuids" in result
    assert "deletes_groups_uuids" in result
    assert "deletes_items_uuids" in result

    # Using sets for order-insensitive comparison
    assert set(result["deletes_forms_uuids"]) == {str(form_uuid_1), str(form_uuid_2)}
    assert set(result["deletes_groups_uuids"]) == {str(group_uuid_1)}
    assert set(result["deletes_items_uuids"]) == {str(item_uuid_1), str(item_uuid_2)}


@pytest.mark.asyncio
async def test_get_and_validate_data_delete_form_flow_not_found(
    async_tenant_db_session_object,
):
    """
    Tests that a 404 CustomValueError is raised if the form flow UUID does not exist.
    """
    # Arrange
    service = FormFlowService(session=async_tenant_db_session_object)
    non_existent_uuid = str(uuid4())

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service._get_and_validate_data_delete_form_flow(non_existent_uuid)

    assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
    assert exc_info.value.message_code == 500404


@pytest.mark.asyncio
async def test_get_and_validate_data_delete_form_flow_not_deletable(
    async_tenant_db_session_object,
):
    """
    Tests that a 400 CustomValueError is raised if the form flow is marked as not deletable.
    """
    # Arrange: Create a flow that is explicitly NOT deletable
    service = FormFlowService(session=async_tenant_db_session_object)
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(
            async_tenant_db_session_object, custom_fields=dict(is_deletable=False)
        )

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service._get_and_validate_data_delete_form_flow(str(flow_uuid))

    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc_info.value.message_code == 500032


@pytest.mark.asyncio
async def test_execute_delete_form_flow_full_cascade(async_tenant_db_session_object):
    """
    Tests that the function correctly soft-deletes the FormFlow and all its children.
    """
    # 1. ARRANGE: Setup an initial state of active records
    service = FormFlowService(session=async_tenant_db_session_object)

    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        item_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid, group_uuid
        )

    delete_data = {
        "deletes_forms_uuids": [str(form_uuid)],
        "deletes_groups_uuids": [str(group_uuid)],
        "deletes_items_uuids": [str(item_uuid)],
    }

    # 2. ACT: Call the execution function
    await service._execute_delete_form_flow(delete_data, str(flow_uuid))

    # 3. ASSERT: Verify that all records are now inactive
    async with async_tenant_db_session_object:
        deleted_flow = await async_tenant_db_session_object.get(FormFlow, flow_uuid)
        deleted_form = await async_tenant_db_session_object.get(Form, form_uuid)
        deleted_group = await async_tenant_db_session_object.get(
            FormItemGroup, group_uuid
        )
        deleted_item = await async_tenant_db_session_object.get(FormItem, item_uuid)

        assert deleted_flow.is_active is False
        assert deleted_flow.deleted_at is not None

        assert deleted_form.is_active is False
        assert deleted_form.deleted_at is not None

        assert deleted_group.is_active is False
        assert deleted_group.deleted_at is not None

        assert deleted_item.is_active is False
        assert deleted_item.deleted_at is not None


@pytest.mark.asyncio
async def test_delete_form_flow_success_full_cascade(async_tenant_db_session_object):
    """
    Tests the full success path: calling delete_form_flow on a valid
    flow soft-deletes the flow and all its children.
    """
    # 1. ARRANGE: Setup an initial state of active records
    service = FormFlowService(session=async_tenant_db_session_object)

    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        item_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid, group_uuid
        )

    # 2. ACT: Call the main public method
    result_uuid = await service.delete_form_flow(str(flow_uuid))

    # 3. ASSERT: Verify that all records are now inactive in the database
    assert result_uuid == str(flow_uuid)
    async with async_tenant_db_session_object.begin():
        deleted_flow = await async_tenant_db_session_object.get(FormFlow, flow_uuid)
        deleted_form = await async_tenant_db_session_object.get(Form, form_uuid)
        deleted_group = await async_tenant_db_session_object.get(
            FormItemGroup, group_uuid
        )
        deleted_item = await async_tenant_db_session_object.get(FormItem, item_uuid)

        assert deleted_flow.is_active is False
        assert deleted_form.is_active is False
        assert deleted_group.is_active is False
        assert deleted_item.is_active is False


@pytest.mark.asyncio
async def test_delete_form_flow_fails_if_not_found(
    async_tenant_db_session_object,
):
    """
    Tests that the delete process fails with a 404 error if the flow UUID does not exist.
    """
    # Arrange
    service = FormFlowService(session=async_tenant_db_session_object)
    non_existent_uuid = str(uuid4())

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service.delete_form_flow(non_existent_uuid)

    assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
    assert exc_info.value.message_code == 500404


@pytest.mark.asyncio
async def test_delete_form_flow_fails_if_not_deletable(async_tenant_db_session_object):
    """
    Tests that the delete process fails with a 400 error if the flow is marked as not deletable.
    """
    # Arrange: Create a flow that is explicitly NOT deletable
    service = FormFlowService(session=async_tenant_db_session_object)
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(
            async_tenant_db_session_object, custom_fields=dict(is_deletable=False)
        )

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service.delete_form_flow(str(flow_uuid))

    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc_info.value.message_code == 500032
