import time
import uuid
from datetime import datetime
from unittest.mock import patch

import pytest
from configuration.settings import configuration
from core.constants import DISPLAY_DATE_FORMAT, PATIENT_FIELDS_ENCRYPTED
from fastapi import status
from sqlalchemy import select
from tests.helpers.insert_data.insert_patient import (
    unittest_insert_patient,
    unittest_remove_patient_by_ids,
)

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.tenant_models.patient_profiles import PatientProfile


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.fixture
def non_existent_patient_id():
    """Return an ID that does not exist in the DB"""
    return 999999999


@pytest.fixture
def update_payload():
    """Return valid payload for updating patient profile"""
    phone_number = str(int(time.time() * 1e6))[-11:]
    return {
        "is_adult": True,
        "profile": {
            "first_name": "Updated",
            "last_name": "Patient",
            "first_name_kana": "アップデート",
            "last_name_kana": "パティエント",
            "home_phone": phone_number,
            "phone": phone_number,
            "email": f"{uuid.uuid4()}@example.com",
            "gender": 1,
            "date_of_birth": "1990/01/01",
            "address_1": "city",
            "address_2": "street name and number",
            "address_3": "house number",
            "prefecture_id": 1,
            "postal_code": "1200000",
        },
    }


@pytest.mark.asyncio
async def test_update_patient_profile_success(
    async_client, _headers, async_tenant_db_session_object, update_payload
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

    response = await async_client.put(
        f"/v1_0/patients/{patient_user_id}",
        json=update_payload,
        headers=_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True

    aes_gcm = AesGCMRotation(configuration)
    async with async_tenant_db_session_object.begin():
        profile = await async_tenant_db_session_object.scalar(
            select(PatientProfile).where(
                PatientProfile.patient_user_id == patient_user_id
            )
        )

        profile_payload = update_payload.get("profile")

        for field in PATIENT_FIELDS_ENCRYPTED:
            actual = getattr(profile, field)
            if not actual:
                continue

            actual = aes_gcm.decrypt_data(actual)
            value = profile_payload.get(field)
            if field == "date_of_birth":
                value = datetime.strptime(value, DISPLAY_DATE_FORMAT).strftime(
                    DISPLAY_DATE_FORMAT
                )

            assert actual == value

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )


@pytest.mark.asyncio
async def test_update_patient_profile_not_found(
    async_client, _headers, non_existent_patient_id
):
    response = await async_client.put(
        f"/v1_0/patients/{non_existent_patient_id}",
        json={},
        headers=_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 60003


@pytest.mark.asyncio
async def test_update_patient_profile_empty_payload(
    async_client, _headers, async_tenant_db_session_object
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

    response_empty = await async_client.put(
        f"/v1_0/patients/{patient_user_id}",
        json={},
        headers=_headers,
    )
    assert response_empty.status_code == status.HTTP_200_OK

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )


@pytest.mark.asyncio
async def test_update_patient_profile_internal_error(
    async_client, _headers, update_payload
):
    with patch(
        "services.patient_service.PatientService.update_patient_profile",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.put(
            "/v1_0/patients/1",
            json=update_payload,
            headers=_headers,
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 60004


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "param_data, expected_status",
    [
        ({"profile": {"email": "testexample.com"}}, 422),
        ({"profile": {"email": "test@examplecom"}}, 422),
        ({"profile": {"email": "invalid-email"}}, 422),
        ({"profile": {"phone": "123456789"}}, 422),
        ({"profile": {"phone": "123456789012"}}, 422),
        ({"profile": {"phone": "invalid-phone"}}, 422),
    ],
)
async def test_validate_patient_user(
    async_client, _headers, param_data, expected_status, async_tenant_db_session_object
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

    response = await async_client.put(
        f"/v1_0/patients/{patient_user_id}", headers=_headers, json=param_data
    )
    assert response.status_code == expected_status

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )
