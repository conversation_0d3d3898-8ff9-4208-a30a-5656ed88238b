from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy import text
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.patient import PatientWaitingStatus
from tests.helpers.header_mock import get_headers
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_patient_waitting import (
    unittest_insert_patient_waiting,
)


async def setup_data(db_session):
    patient_user_id = await unittest_insert_patient(db_session)
    doctor_user_ids = [await unittest_insert_doctor(db_session) for _ in range(4)]
    patient_waiting_id = await unittest_insert_patient_waiting(
        db_session=db_session,
        patient_user_id=patient_user_id,
        custom_fields={
            "assigned_doctors": [doctor_user_ids],
        },
    )
    return {
        "patient_user_id": patient_user_id,
        "patient_waiting_id": patient_waiting_id,
        "doctor_user_ids": doctor_user_ids,
    }


@pytest_asyncio.fixture(scope="class")
async def setup_update_patient_waiting_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        data = await setup_data(async_tenant_db_session_object)
    return data


@pytest_asyncio.fixture(scope="function")
async def setup_update_patient_waiting_data_roll_back(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        data = await setup_data(async_tenant_db_session_object)
    return data


def get_valid_payload(overrides=None):
    payload = {
        "status": PatientWaitingStatus.SCHEDULED.value,
        "assigned_doctors": [],
        "assigned_room": None,
        "room_number": 0,
    }
    if overrides is not None:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload_overrides, expected_status, expected_success, expected_message, expected_messageCode",
    [
        (
            lambda data: {
                "assigned_doctors": data["doctor_user_ids"],
            },
            200,
            True,
            None,
            None,
        ),
        (
            lambda data: {
                "assigned_doctors": data["doctor_user_ids"],
                "status": PatientWaitingStatus.RESOLVED.value,
            },
            200,
            True,
            None,
            None,
        ),
        (
            lambda data: {
                "status": "invalid_status",
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        ),
        (
            lambda data: {
                "assigned_doctors": [9999999],
                "visit_end_date": (datetime.now() + timedelta(days=2))
                .date()
                .isoformat(),
                "visit_end_time": (datetime.now() + timedelta(days=2))
                .time()
                .isoformat(),
            },
            400,
            False,
            CustomMessageCode.DOCTOR_NOT_FOUND.title,
            CustomMessageCode.DOCTOR_NOT_FOUND.code,
        ),
    ],
)
async def test_update_patient_waiting_parametrized(
    async_client,
    tenant_uuid,
    setup_update_patient_waiting_data,
    payload_overrides,
    expected_status,
    expected_success,
    expected_message,
    expected_messageCode,
):
    data = setup_update_patient_waiting_data
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(payload_overrides(data))

    response = await async_client.put(
        f"/v1_0/patient-waitings/{data['patient_waiting_id']}",
        headers=headers,
        json=payload,
    )
    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is expected_success
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_messageCode


@pytest.mark.asyncio
async def test_update_patient_waiting_invalid_patient_waiting_id(
    async_client, tenant_uuid, setup_update_patient_waiting_data
):
    data = setup_update_patient_waiting_data
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "assigned_doctors": data["doctor_user_ids"],
            "visit_end_date": (datetime.now() + timedelta(days=2)).date().isoformat(),
            "visit_end_time": (datetime.now() + timedelta(days=2)).time().isoformat(),
        }
    )

    response = await async_client.put(
        "/v1_0/patient-waitings/9999999",
        headers=headers,
        json=payload,
    )
    assert response.status_code == 400
    result = response.json()
    assert result["success"] is False
    assert result["message"] == CustomMessageCode.PATIENT_WAITING_NOT_FOUND.title
    assert result["messageCode"] == CustomMessageCode.PATIENT_WAITING_NOT_FOUND.code


@pytest.mark.asyncio
async def test_update_patient_waiting_database_error(
    async_client, tenant_uuid, setup_update_patient_waiting_data
):
    data = setup_update_patient_waiting_data
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "assigned_doctors": data["doctor_user_ids"],
            "visit_end_date": (datetime.now() + timedelta(days=2)).date().isoformat(),
            "visit_end_time": (datetime.now() + timedelta(days=2)).time().isoformat(),
        }
    )

    with patch(
        "services.common.validate_service.ValidateService.validate_doctor_users"
    ) as mock_validate_doctor_users:
        mock_validate_doctor_users.side_effect = OperationalError(
            "Database error", None, None
        )

        response = await async_client.put(
            f"/v1_0/patient-waitings/{data['patient_waiting_id']}",
            headers=headers,
            json=payload,
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert mock_validate_doctor_users.call_count == 3


@pytest.mark.asyncio
async def test_update_patient_waiting_rool_back(
    async_client,
    tenant_uuid,
    setup_update_patient_waiting_data_roll_back,
    async_tenant_db_session_object,
):
    data = setup_update_patient_waiting_data_roll_back
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "assigned_doctors": data["doctor_user_ids"],
            "visit_end_date": (datetime.now() + timedelta(days=2)).date().isoformat(),
            "visit_end_time": (datetime.now() + timedelta(days=2)).time().isoformat(),
            "status": PatientWaitingStatus.RESOLVED.value,
        }
    )

    with patch(
        "services.medical_history_service.MedicalHistoryService.create_medical_history"
    ) as mock_create_medical_history:
        mock_create_medical_history.side_effect = Exception("Function error!")

        response = await async_client.put(
            f"/v1_0/patient-waitings/{data['patient_waiting_id']}",
            headers=headers,
            json=payload,
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert mock_create_medical_history.call_count == 1

    async with async_tenant_db_session_object.begin():
        query = await async_tenant_db_session_object.execute(
            text("SELECT * FROM patient_waitings WHERE id = :id"),
            {"id": data["patient_waiting_id"]},
        )
        result = query.fetchone()
        assert result.status == PatientWaitingStatus.SCHEDULED.value
