from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.patient import EmergencyFlag, PatientWaitingStatus
from tests.helpers.header_mock import get_headers
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_patient import unittest_insert_patient


@pytest_asyncio.fixture(scope="class")
async def setup_create_patient_waiting_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient = [
            await unittest_insert_patient(async_tenant_db_session_object)
            for _ in range(4)
        ]
        doctor = [
            await unittest_insert_doctor(async_tenant_db_session_object)
            for _ in range(4)
        ]
    return {"patients": patient, "doctors": doctor}


def get_valid_payload(overrides=None):
    payload = {
        "patient_user_id": 1,
        "emergency_flag": EmergencyFlag.NORMAL.value,
        "coming_time": datetime.now().astimezone().isoformat(),
        "status": PatientWaitingStatus.SCHEDULED.value,
        "visit_start_date": (datetime.now() + timedelta(days=1)).date().isoformat(),
        "visit_start_time": (datetime.now() + timedelta(days=1)).time().isoformat(),
        "visit_end_date": None,
        "visit_end_time": None,
        "assigned_doctors": [],
        "assigned_room": None,
        "room_number": 0,
    }
    if overrides is not None:
        payload.update(overrides)
    return payload


combined_cases = [
    # Success cases
    (True, 0, {}, CustomMessageCode.PATIENT_WAITING_CREATED_SUCCESS.title, None, 200),
    (
        True,
        1,
        {"assigned_doctors": "doctors"},
        CustomMessageCode.PATIENT_WAITING_CREATED_SUCCESS.title,
        None,
        200,
    ),
    # Invalid 422 cases
    (
        False,
        2,
        {"patient_user_id": "invalid_patient_user_id"},
        CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        422,
    ),
    (
        False,
        3,
        {"coming_time": "invalid_date_format"},
        CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        422,
    ),
    (
        False,
        3,
        {"status": "invalid_status"},
        CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        422,
    ),
    # Invalid 400 cases
    (
        False,
        3,
        {"patient_user_id": 99999999},
        CustomMessageCode.PATIENT_NOT_FOUND.title,
        CustomMessageCode.PATIENT_NOT_FOUND.code,
        400,
    ),
    (
        False,
        3,
        {"assigned_doctors": [99999999]},
        CustomMessageCode.DOCTOR_NOT_FOUND.title,
        CustomMessageCode.DOCTOR_NOT_FOUND.code,
        400,
    ),
    (
        False,
        3,
        {"reservation_id": 9999999},
        CustomMessageCode.PATIENT_WAITING_RESERVATION_NOT_FOUND.title,
        CustomMessageCode.PATIENT_WAITING_RESERVATION_NOT_FOUND.code,
        400,
    ),
]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "case_type, idx, extra, msg, msg_code, status_code", combined_cases
)
async def test_create_patient_waiting_combined(
    async_client,
    tenant_uuid,
    setup_create_patient_waiting_data,
    case_type,
    idx,
    extra,
    msg,
    msg_code,
    status_code,
):
    data = setup_create_patient_waiting_data["patients"][idx]
    headers = get_headers(tenant_uuid)

    payload_data = {
        "patient_user_id": data,
        "coming_time": datetime.now().astimezone().isoformat(),
    }

    assigned_doctors = extra.get("assigned_doctors")
    if assigned_doctors:
        payload_data["assigned_doctors"] = (
            setup_create_patient_waiting_data["doctors"]
            if assigned_doctors == "doctors"
            else assigned_doctors
        )

    payload_data.update({k: v for k, v in extra.items() if k != "assigned_doctors"})
    payload = get_valid_payload(payload_data)
    response = await async_client.post(
        "/v1_0/patient-waitings", headers=headers, json=payload
    )
    result = response.json()

    assert response.status_code == status_code
    assert result["success"] is case_type
    assert result["message"] == msg
    assert result["messageCode"] == msg_code


@pytest.mark.asyncio
async def test_create_patient_waiting_database_error(
    async_client, tenant_uuid, setup_create_patient_waiting_data
):
    data = setup_create_patient_waiting_data["patients"][3]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "patient_user_id": data,
            "coming_time": datetime.now().astimezone().isoformat(),
        }
    )
    with patch(
        "api.v1.patients.patient_waiting_api.PatientWaitingService.create_patient_waiting"
    ) as mock_method:
        mock_method.side_effect = OperationalError("Database error", None, None)
        response = await async_client.post(
            "/v1_0/patient-waitings", headers=headers, json=payload
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
