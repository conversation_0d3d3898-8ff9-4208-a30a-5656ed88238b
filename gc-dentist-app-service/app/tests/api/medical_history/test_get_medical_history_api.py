from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.patient import MedicalHistoryStatus
from tests.helpers.header_mock import get_headers
from tests.helpers.insert_data.insert_medical_history import (
    unittest_insert_medical_history,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_patient_waitting import (
    unittest_insert_patient_waiting,
)

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


async def create_data(db_session):
    patient_user_id = await unittest_insert_patient(db_session)
    patient_waiting_id = await unittest_insert_patient_waiting(
        db_session, patient_user_id
    )
    visit_start_datetime = datetime.now().astimezone()
    medical_history_id = await unittest_insert_medical_history(
        db_session,
        patient_user_id,
        patient_waiting_id,
        custom_fields={
            "visit_start_datetime": visit_start_datetime,
        },
    )
    return {
        "patient_user_id": patient_user_id,
        "patient_waiting_id": patient_waiting_id,
        "medical_history_id": medical_history_id,
        "visit_start_datetime": visit_start_datetime.strftime("%Y-%m-%d"),
    }


@pytest_asyncio.fixture(scope="class")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        data = [await create_data(async_tenant_db_session_object) for _ in range(4)]
    return data


def get_valid_param(overrides=None):
    payload = {
        "status": MedicalHistoryStatus.PROCESSING.value,
    }
    if overrides:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "data_idx, filters, expected_code, expected_message, check_data_response, data_response",
    [
        (
            0,
            lambda _: {"status": MedicalHistoryStatus.PROCESSING.value},
            200,
            True,
            True,
            [],
        ),
        (
            0,
            lambda data: {"visit_start_datetime": data["visit_start_datetime"]},
            200,
            True,
            True,
            [],
        ),
        (
            0,
            lambda data: {"medical_history_id": data["medical_history_id"]},
            200,
            True,
            True,
            [],
        ),
        (
            0,
            lambda data: {
                "patient_user_id": data["patient_user_id"],
                "patient_waiting_id": data["patient_waiting_id"],
            },
            200,
            True,
            True,
            [],
        ),
        (
            0,
            lambda data: {
                "patient_user_id": data["patient_user_id"],
                "visit_start_datetime": data["visit_start_datetime"],
            },
            200,
            True,
            True,
            [],
        ),
        (
            0,
            lambda _: {"status": MedicalHistoryStatus.COMPLETED.value},
            200,
            True,
            True,
            [],
        ),
        (0, lambda _: {"status": "INVALID_STATUS"}, 422, False, True, []),
        (0, lambda _: {"visit_start_datetime": "INVALID_DATE"}, 422, False, True, []),
        (0, lambda _: {"patient_user_id": 999999}, 200, True, False, []),
    ],
)
async def test_get_medical_history_cases(
    async_client,
    tenant_uuid,
    setup_data,
    data_idx,
    filters,
    expected_code,
    expected_message,
    check_data_response,
    data_response,
):
    data = setup_data[data_idx]
    param = get_valid_param(filters(data))
    headers = get_headers(tenant_uuid)

    response = await async_client.get(
        "/v1_0/medical-history",
        headers=headers,
        params=param,
    )
    assert response.status_code == expected_code
    result = response.json()
    assert result["success"] is expected_message
    if check_data_response:
        assert result["data"] is not data_response
    else:
        assert result["data"] == data_response


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "side_effect, expected_status, expected_success, message_code",
    [
        (
            Exception("Server error"),
            400,
            False,
            CustomMessageCode.UNKNOWN_ERROR.code,
        ),
        (
            OperationalError(
                "Operational error occurred",
                None,
                None,
            ),
            400,
            False,
            CustomMessageCode.UNKNOWN_ERROR.code,
        ),
        (
            CustomValueError(
                CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
                CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            ),
            400,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        ),
    ],
)
async def test_get_medical_history_exceptions(
    async_client,
    tenant_uuid,
    setup_data,
    side_effect,
    expected_status,
    expected_success,
    message_code,
):
    data = setup_data[0]
    headers = get_headers(tenant_uuid)
    url = "/v1_0/medical-history"

    with patch(
        "services.medical_history_service.MedicalHistoryService.get_medical_history",
        side_effect=side_effect,
    ):
        response = await async_client.get(
            url,
            headers=headers,
            params=get_valid_param(
                {
                    "patient_user_id": data["patient_user_id"],
                    "visit_start_datetime": data["visit_start_datetime"],
                }
            ),
        )
        assert response.status_code == expected_status
        result = response.json()
        assert result["success"] is expected_success
        assert result["messageCode"] == message_code
