from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.patient import MedicalHistoryStatus
from tests.helpers.header_mock import get_headers
from tests.helpers.insert_data.insert_medical_history import (
    unittest_insert_medical_history,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_patient_waitting import (
    unittest_insert_patient_waiting,
)


async def create_data(db_session):
    patient_user_id = await unittest_insert_patient(db_session)
    patient_waiting_id = await unittest_insert_patient_waiting(
        db_session, patient_user_id
    )
    medical_history_id = await unittest_insert_medical_history(
        db_session, patient_user_id, patient_waiting_id
    )
    return {
        "patient_user_id": patient_user_id,
        "patient_waiting_id": patient_waiting_id,
        "medical_history_id": medical_history_id,
    }


@pytest_asyncio.fixture(scope="class")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        data = [await create_data(async_tenant_db_session_object) for _ in range(4)]
    return data


def get_valid_payload(overrides=None):
    payload = {
        "status": MedicalHistoryStatus.PROCESSING.value,
        "doctor_user_ids": [],
        "visit_start_datetime": datetime.now().astimezone().isoformat(),
    }
    if overrides:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "data_idx, status, expected_code, expected_message",
    [
        (0, MedicalHistoryStatus.COMPLETED.value, 200, True),
        (1, MedicalHistoryStatus.CANCELLED.value, 200, True),
    ],
)
async def test_update_medical_history_success_cases(
    async_client,
    tenant_uuid,
    setup_data,
    data_idx,
    status,
    expected_code,
    expected_message,
):
    data = setup_data[data_idx]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload({"status": status})
    response = await async_client.put(
        f"/v1_0/medical-history/{data['medical_history_id']}",
        headers=headers,
        json=payload,
    )
    assert response.status_code == expected_code
    result = response.json()
    assert result["success"] is expected_message


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "url, payload, expected_status, expected_code, expected_message",
    [
        (
            "/v1_0/medical-history/9999999",
            None,
            400,
            CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.code,
            CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.title,
        ),
        (
            None,
            {"status": "INVALID_STATUS"},
            422,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        ),
        (
            None,
            {"visit_start_datetime": "invalid-date-time"},
            422,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        ),
        (
            None,
            {"visit_start_datetime": "2023-02-21T21:11:00+00:00"},
            422,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        ),
        (
            None,
            {},
            422,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        ),
    ],
)
async def test_update_medical_history_invalid_cases(
    async_client,
    tenant_uuid,
    setup_data,
    url,
    payload,
    expected_status,
    expected_code,
    expected_message,
):
    headers = get_headers(tenant_uuid)
    if url is None:
        data = setup_data[3]
        url = f"/v1_0/medical-history/{data['medical_history_id']}"
    if payload is None:
        payload = get_valid_payload()
    response = await async_client.put(
        url,
        headers=headers,
        json=payload,
    )
    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is False
    assert result["messageCode"] == expected_code
    assert result["message"] == expected_message


@pytest.mark.asyncio
async def test_update_medical_history_max_retries(
    async_client, tenant_uuid, setup_data
):
    data = setup_data[3]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "doctor_user_ids": [1],
            "status": MedicalHistoryStatus.PROCESSING.value,
        }
    )
    with patch(
        "services.common.validate_service.ValidateService.validate_doctor_users",
        side_effect=OperationalError("Max retries exceeded", None, None),
    ) as mock_method:
        response = await async_client.put(
            f"/v1_0/medical-history/{data['medical_history_id']}",
            headers=headers,
            json=payload,
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
        assert mock_method.call_count == 3
