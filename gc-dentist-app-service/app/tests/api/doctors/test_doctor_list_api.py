import time
import uuid
from unittest.mock import patch

import pytest
from fastapi import status
from tests.helpers.insert_data.insert_doctor import (
    unittest_insert_doctor,
    unittest_remove_doctor_by_ids,
)


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.mark.asyncio
async def test_get_list_success_structure(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    response = await async_client.get("/v1_0/doctors", headers=_headers)
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    assert isinstance(data["items"], list)
    assert "total" in data
    assert "page" in data
    assert "size" in data

    assert data["total"] == 1
    assert len(data["items"]) == 1

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
async def test_get_list_empty_when_no_active_doctors(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        await unittest_insert_doctor(
            async_tenant_db_session_object, custom_user_fields={"status": False}
        )

    response = await async_client.get("/v1_0/doctors", headers=_headers)

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True

    data = response_json["data"]
    assert data["total"] == 0
    assert len(data["items"]) == 0


@pytest.mark.asyncio
async def test_get_list_pagination_logic(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        doctor_id_1 = await unittest_insert_doctor(async_tenant_db_session_object)
        doctor_id_2 = await unittest_insert_doctor(async_tenant_db_session_object)
        doctor_id_3 = await unittest_insert_doctor(async_tenant_db_session_object)

    response_page1 = await async_client.get(
        "/v1_0/doctors?page=1&size=2", headers=_headers
    )

    assert response_page1.status_code == status.HTTP_200_OK
    data_page1 = response_page1.json()["data"]

    assert data_page1["total"] == 3
    assert len(data_page1["items"]) == 2
    assert data_page1["page"] == 1
    assert data_page1["size"] == 2

    assert data_page1["items"][0]["id"] == doctor_id_1
    assert data_page1["items"][1]["id"] == doctor_id_2

    response_page2 = await async_client.get(
        "/v1_0/doctors?page=2&size=2", headers=_headers
    )

    assert response_page2.status_code == status.HTTP_200_OK
    data_page2 = response_page2.json()["data"]

    assert data_page2["total"] == 3
    assert len(data_page2["items"]) == 1
    assert data_page2["page"] == 2
    assert data_page2["items"][0]["id"] == doctor_id_3

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object,
            doctor_ids=[doctor_id_1, doctor_id_2, doctor_id_3],
        )


@pytest.mark.asyncio
async def test_get_list_with_valid_decrypt_info(
    async_client, async_tenant_db_session_object, _headers
):
    doctor_data = {
        "last_name": "Nguyen",
        "first_name": "Van",
        "last_name_kana": "グエン",
        "first_name_kana": "バン",
        "phone": str(int(time.time() * 1e6))[-11:],
        "date_of_birth": "1990/12/01",
        "email": str(uuid.uuid4()) + "@example.com",
    }
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(
            async_tenant_db_session_object,
            custom_profile_fields=doctor_data,
        )

    response = await async_client.get("/v1_0/doctors?page=1", headers=_headers)

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    assert isinstance(data["items"], list)

    first_item = data["items"][0]
    profile_first_item = first_item["profile"]

    birthday = profile_first_item["date_of_birth"]
    phone = profile_first_item["phone"]

    assert birthday == doctor_data["date_of_birth"]
    assert phone == doctor_data["phone"]

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params",
    [
        {"page": 0, "size": 10},
        {"page": -1, "size": 10},
        {"page": 1, "size": 0},
        {"page": 1, "size": -5},
    ],
)
async def test_get_list_invalid_pagination_input(async_client, _headers, params):
    response = await async_client.get("/v1_0/doctors", params=params, headers=_headers)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = response.json()
    assert "data" in error_data
    assert isinstance(error_data["data"], list)
    assert len(error_data["data"]) > 0


@pytest.mark.asyncio
async def test_get_list_exception_error(async_client, _headers):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.doctor_service.DoctorService.list_doctor",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get("/v1_0/doctors", headers=_headers)

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 100008
