from unittest.mock import patch

import pytest
from fastapi import status
from tests.helpers.insert_data.insert_doctor import (
    unittest_insert_doctor,
    unittest_remove_doctor_by_ids,
)


@pytest.fixture
def non_existent_doctor_id():
    """Return an ID that does not exist in the DB"""
    return 999999999


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.mark.asyncio
async def test_get_doctor_profile_success_structure(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    response = await async_client.get(
        f"/v1_0/doctors/{doctor_user_id}", headers=_headers
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
async def test_get_doctor_profile_data_integrity(
    async_client, async_tenant_db_session_object, _headers
):
    doctor_data = {
        "last_name": "Nguyen",
        "first_name": "Van",
        "last_name_kana": "グエン",
        "first_name_kana": "バン",
        "phone": "0987654321",
        "email": "<EMAIL>",
        "gender": 1,
        "date_of_birth": "2000/01/01",
        "prefecture_id": 1,
        "postal_code": "091221",
        "address_1": "90",
        "address_2": "ha huy tap",
        "address_3": "da nang",
        "country_code": "+81",
        "order_index": 1,
    }
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(
            async_tenant_db_session_object,
            custom_profile_fields=doctor_data,
        )

    response = await async_client.get(
        f"/v1_0/doctors/{doctor_user_id}", headers=_headers
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"] is True
    data = response_json["data"]

    assert "username" in data
    assert "uuid" in data
    assert "profile" in data
    profile = data["profile"]
    for key, value in doctor_data.items():
        assert profile.get(key) == value

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
async def test_get_doctor_profile_not_found(
    async_client, _headers, non_existent_doctor_id
):

    response = await async_client.get(
        f"/v1_0/doctors/{non_existent_doctor_id}", headers=_headers
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_json = response.json()
    assert response_json["messageCode"] == 70003


@pytest.mark.asyncio
async def test_get_doctor_profile_exception_error(async_client, _headers):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.doctor_service.DoctorService.get_doctor_profile",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get("/v1_0/doctors/1", headers=_headers)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["messageCode"] == 100005


@pytest.mark.asyncio
async def test_get_doctor_profile_invalid_path_param(async_client, _headers):
    """Test passing invalid doctor_id path param (non-integer)"""
    response = await async_client.get("/v1_0/doctors/abc", headers=_headers)

    assert response.status_code == status.HTTP_404_NOT_FOUND
