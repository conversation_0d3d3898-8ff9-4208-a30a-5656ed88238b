from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from tests.helpers.header_mock import get_headers
from tests.helpers.insert_data.insert_patient import unittest_insert_patient

from gc_dentist_shared.core.exception_handler.custom_exception import (
    CustomValueError,
    S3BucketExceptionError,
)


@pytest_asyncio.fixture(scope="class")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
    return {"patient_user_id": patient_user_id}


def get_valid_payload(overrides=None):
    payload = {
        "prefix_name": "intraoral_examination",
        "file_names": ["test1.jpg", "test2.jpg"],
        "role": "doctor",
        "id": 1,
    }
    if overrides is not None:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload_overrides, expected_status, expected_success, message_code, call_count",
    [
        (
            lambda data: {
                "prefix_name": "intraoral_examination",
                "role": "patient",
                "id": data["patient_user_id"],
            },
            200,
            True,
            None,
            1,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "role": "doctor",
                "id": "invalid_id",
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            0,
        ),
        (
            lambda _: {"prefix_name": "", "role": "", "id": ""},
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            0,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "file_names": [],
                "role": "doctor",
                "id": 1,
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            0,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "file_names": ["invalid_file_name"],
                "role": "doctor",
                "id": 1,
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            0,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "file_names": [".invalid_file_name"],
                "role": "doctor",
                "id": 1,
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            0,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "file_names": [" "],
                "role": "doctor",
                "id": 1,
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            0,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "role": "doctor",
                "id": 9999999,
            },
            400,
            False,
            CustomMessageCode.DOCTOR_NOT_FOUND.code,
            0,
        ),
        (
            lambda _: {
                "prefix_name": "intraoral_examination",
                "role": "patient",
                "id": 9999999,
            },
            400,
            False,
            CustomMessageCode.PATIENT_NOT_FOUND.code,
            0,
        ),
    ],
)
async def test_generate_presigned_url(
    async_client,
    tenant_uuid,
    setup_data,
    payload_overrides,
    expected_status,
    expected_success,
    message_code,
    call_count,
):
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(payload_overrides(setup_data))

    with patch(
        "services.auth_s3_service.S3AuthService.s3_create_presigned_url",
        return_value="https://example.com/presigned-url",
    ) as mock_generate_presigned_url:
        response = await async_client.post(
            "/v1_0/auth-s3/generate-presigned-url", headers=headers, json=payload
        )

        assert response.status_code == expected_status
        result = response.json()

        if expected_success:
            assert result["success"] is True
            assert result["data"] == "https://example.com/presigned-url"
        else:
            assert result["success"] is False
            assert result["messageCode"] == message_code

        assert mock_generate_presigned_url.call_count == call_count


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "side_effect, expected_status, expected_success, message_code",
    [
        (
            Exception("Server error"),
            400,
            False,
            CustomMessageCode.UNKNOWN_ERROR.code,
        ),
        (
            CustomValueError(
                message=CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
                message_code=CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
            ),
            400,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        ),
        (
            S3BucketExceptionError(),
            400,
            False,
            CustomMessageCode.S3_BUCKET_ERROR.code,
        ),
    ],
)
async def test_generate_presigned_url_exception(
    async_client,
    tenant_uuid,
    setup_data,
    side_effect,
    expected_status,
    expected_success,
    message_code,
):
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "prefix_name": "intraoral_examination",
            "role": "doctor",
            "id": setup_data["patient_user_id"],
        }
    )

    with patch(
        "services.auth_s3_service.S3AuthService.generate_presigned_url",
        side_effect=side_effect,
    ) as mock_generate_presigned_url:
        response = await async_client.post(
            "/v1_0/auth-s3/generate-presigned-url", headers=headers, json=payload
        )

        assert response.status_code == expected_status
        result = response.json()
        assert result["success"] is expected_success
        assert result["messageCode"] == message_code
        assert mock_generate_presigned_url.call_count == 1
