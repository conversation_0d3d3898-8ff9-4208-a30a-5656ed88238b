from datetime import datetime
from typing import ClassVar

from sqlalchemy.ext.asyncio import AsyncSession
from tests.helpers.insert_data.insert_intraoral_examination import (
    unittest_insert_intraoral_examination,
)
from tests.helpers.insert_data.insert_medical_history import (
    unittest_insert_medical_history,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_patient_waitting import (
    unittest_insert_patient_waiting,
)


class OralExaminationSetupData:
    default_data: ClassVar[dict] = {
        "patient_user_id": 1,
        "medical_history_id": 1,
        "examination_date": datetime.now().astimezone().isoformat(),
        "intraoral_examination": {
            "teeth": {
                "upper_right_1": {
                    "tooth_type": "adult",
                    "whole_tooth": {
                        "inspection": "string",
                        "pocket_depth_mm": 15,
                        "recession_mm": 10,
                        "clinical_attachment_loss_mm": 0,
                        "bleeding": True,
                        "plaque": True,
                        "mobility": 3,
                        "furcation_involvement": 3,
                        "suppuration": True,
                        "calculus": True,
                        "image_url": "https://example.com/",
                        "image_note": "string",
                        "diagnosis_code": "string",
                        "evaluation_result": "string",
                        "note": "string",
                        "extra_data": {
                            "additionalProp1": "string",
                            "additionalProp2": "string",
                            "additionalProp3": "string",
                        },
                    },
                    "note": "string",
                    "treatment": ["WSD"],
                },
                "upper_right_2": {
                    "tooth_type": "adult",
                    "points": {
                        "1": {"inspection": "C3"},
                        "2": {
                            "inspection": "Per",
                            "pocket_depth_mm": 15,
                            "recession_mm": 10,
                            "clinical_attachment_loss_mm": 0,
                            "bleeding": True,
                            "plaque": True,
                            "mobility": 3,
                            "furcation_involvement": 3,
                            "suppuration": True,
                            "calculus": True,
                            "image_url": "https://example.com/",
                            "image_note": "string",
                            "diagnosis_code": "string",
                            "evaluation_result": "string",
                            "note": "string",
                            "extra_data": {
                                "additionalProp1": "string",
                                "additionalProp2": "string",
                                "additionalProp3": "string",
                            },
                        },
                        "3": {"inspection": "C1"},
                    },
                    "note": "string",
                    "treatment": ['C"'],
                },
            },
            "general_image_url": "https://example.com/",
        },
        "note": "string",
        "memo_path": "string",
        "tooth_type": "adult",
    }

    @classmethod
    async def setup_data_for_intraoral_examination(cls, db_session: AsyncSession):
        patient_user_id = await unittest_insert_patient(db_session)
        patient_waiting_id = await unittest_insert_patient_waiting(
            db_session, patient_user_id
        )
        medical_history_id = await unittest_insert_medical_history(
            db_session, patient_user_id, patient_waiting_id
        )
        return {
            "patient_user_id": patient_user_id,
            "patient_waiting_id": patient_waiting_id,
            "medical_history_id": medical_history_id,
        }

    @classmethod
    async def insert_intraoral_examination(
        cls, db_session: AsyncSession, custom_fields=None
    ):
        result = await cls.setup_data_for_intraoral_examination(db_session)
        oral_examination_id = await unittest_insert_intraoral_examination(
            db_session,
            patient_user_id=result["patient_user_id"],
            medical_history_id=result["medical_history_id"],
            custom_fields=custom_fields,
        )

        return {
            **result,
            "oral_examination_id": oral_examination_id,
        }

    @classmethod
    async def insert_intraoral_examination_for_api_get(
        cls,
        patient_user_id: int,
        medical_history_id: int,
        db_session: AsyncSession,
        custom_fields=None,
    ):
        oral_examination_id = await unittest_insert_intraoral_examination(
            db_session,
            patient_user_id=patient_user_id,
            medical_history_id=medical_history_id,
            custom_fields=custom_fields,
        )

        return {
            "oral_examination_id": oral_examination_id,
            "patient_user_id": patient_user_id,
            "medical_history_id": medical_history_id,
        }
