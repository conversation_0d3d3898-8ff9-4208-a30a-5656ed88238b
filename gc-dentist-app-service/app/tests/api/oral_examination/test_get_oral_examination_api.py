from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.oral_examination import OralExaminationType
from tests.helpers.header_mock import get_headers

from .setup_data import OralExaminationSetupData


@pytest_asyncio.fixture(scope="class")
async def setup_intraoral_examination_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_info = (
            await OralExaminationSetupData.setup_data_for_intraoral_examination(
                async_tenant_db_session_object
            )
        )
        data_patient = [
            await OralExaminationSetupData.insert_intraoral_examination_for_api_get(
                patient_user_id=patient_info["patient_user_id"],
                medical_history_id=patient_info["medical_history_id"],
                db_session=async_tenant_db_session_object,
                custom_fields={
                    "examination_date": datetime.now().astimezone() - timedelta(days=_)
                },
            )
            for _ in range(4)
        ]
    return data_patient


def get_valid_payload(overrides=None):
    payload = OralExaminationSetupData.default_data.copy()
    if overrides is not None:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params_builder",
    [
        lambda data: {"oral_examination_id": data[0]["oral_examination_id"]},
        lambda data: {"patient_user_id": data[0]["patient_user_id"]},
        lambda data: {"examination_date": datetime.now().date()},
        lambda data: {
            "examination_date": datetime.now().date(),
            "patient_user_id": data[0]["patient_user_id"],
            "oral_examination_id": data[0]["oral_examination_id"],
        },
        lambda data: {
            "examination_date": (datetime.now() - timedelta(days=1)).date(),
            "patient_user_id": data[0]["patient_user_id"],
            "oral_examination_id": data[0]["oral_examination_id"],
        },
    ],
)
async def test_get_intraoral_examination_success_cases(
    async_client, tenant_uuid, setup_intraoral_examination_data, params_builder
):
    data = setup_intraoral_examination_data
    headers = get_headers(tenant_uuid)
    params = params_builder(data)
    response = await async_client.get(
        f"/v1_0/oral-examinations/{OralExaminationType.INTRAORAL_EXAMINATION.value}",
        headers=headers,
        params=params,
    )
    assert response.status_code == 200
    result = response.json()
    assert result["success"] is True
    assert result["data"] is not None


@pytest.mark.asyncio
async def test_get_intraoral_examination_not_found(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    headers = get_headers(tenant_uuid)
    params = {
        "oral_examination_id": 999999,
    }
    response = await async_client.get(
        f"/v1_0/oral-examinations/{OralExaminationType.INTRAORAL_EXAMINATION.value}",
        headers=headers,
        params=params,
    )
    assert response.status_code == 200
    result = response.json()
    assert result["success"] is True
    assert result["data"] == []


@pytest.mark.asyncio
async def test_get_intraoral_examination_fail_type(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data
    headers = get_headers(tenant_uuid)
    params = {
        "patient_user_id": data[0]["patient_user_id"],
    }
    response = await async_client.get(
        "/v1_0/oral-examinations/invalid_type", headers=headers, params=params
    )
    assert response.status_code == 422
    result = response.json()
    assert result["success"] is False


@pytest.mark.asyncio
async def test_get_intraoral_examination_retry_exception(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data
    headers = get_headers(tenant_uuid)
    params = {
        "patient_user_id": data[0]["patient_user_id"],
    }
    with patch(
        "services.oral_examination.oral_examination_service.OralExaminationService.get_condition_for_filter",
        side_effect=OperationalError("Database connection error", None, None),
    ) as mock_get_condition_for_filter:
        response = await async_client.get(
            f"/v1_0/oral-examinations/{OralExaminationType.INTRAORAL_EXAMINATION.value}",
            headers=headers,
            params=params,
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert mock_get_condition_for_filter.call_count == 3
