import uuid
from unittest.mock import patch

import pytest
import pytest_asyncio
from schemas.requests.form_submission import (
    FormSubmissionCreate,
    SubmittedForm,
    SubmittedFormFlowData,
    SubmittedFormItem,
    SubmittedFormItemGroup,
)
from services.form_flows.form_submission_services import FormSubmissionService
from sqlalchemy import select, update
from tests.helpers.enums.form_flows import (
    UnittestFormFlowType,
    UnittestFormItemFieldType,
)
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_form import (
    insert_form,
    insert_form_flow,
    insert_form_item,
    insert_form_item_group,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import (
    DoctorUser,
    FormFlow,
    FormSubmission,
    PatientUser,
)


@pytest_asyncio.fixture(scope="function")
async def create_data_form_flow(async_tenant_db_session_object) -> dict:
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, form_flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        item_in_group_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid, form_item_group_uuid=group_uuid
        )
        standalone_item_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid
        )

    return {
        "form_flow_uuid": form_flow_uuid,
        "form_uuid": form_uuid,
        "group_uuid": group_uuid,
        "item_in_group_uuid": item_in_group_uuid,
        "standalone_item_uuid": standalone_item_uuid,
    }


@pytest.fixture(scope="function")
def valid_submission_data(create_data_form_flow: dict) -> FormSubmissionCreate:
    """
    Creates a valid FormSubmissionCreate object based on the create_data_form_flow fixture.
    This serves as a valid baseline for modification in tests.
    """
    return FormSubmissionCreate(
        form_flow_uuid=create_data_form_flow["form_flow_uuid"],
        doctor_user_id=1,
        patient_user_id=1,
        form_flow_data=SubmittedFormFlowData(
            uuid=create_data_form_flow["form_flow_uuid"],
            flow_name="Valid Flow Name",
            flow_type=UnittestFormFlowType.SURVEY,
            forms=[
                SubmittedForm(
                    uuid=create_data_form_flow["form_uuid"],
                    form_name="Seeded Form",
                    order_index=0,
                    groups=[
                        SubmittedFormItemGroup(
                            uuid=create_data_form_flow["group_uuid"],
                            title="Seeded Group",
                            order_index=0,
                            items=[
                                SubmittedFormItem(
                                    uuid=create_data_form_flow["item_in_group_uuid"],
                                    label="Item In Group",
                                    answer="Answer 1",
                                    field_type=UnittestFormItemFieldType.TEXT,
                                    order_index=0,
                                )
                            ],
                        )
                    ],
                    items=[
                        SubmittedFormItem(
                            uuid=create_data_form_flow["standalone_item_uuid"],
                            label="Standalone Item",
                            answer="Answer 2",
                            field_type=UnittestFormItemFieldType.TEXT,
                            order_index=1,
                        )
                    ],
                )
            ],
        ),
    )


@pytest.mark.asyncio
async def test_create_form_submission_success(
    async_client,
    async_tenant_db_session_object,
    valid_submission_data,
    _headers_tenant_uuid,
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    # Update the payload with real IDs from the DB
    valid_submission_data.patient_user_id = patient_user_id
    valid_submission_data.doctor_user_id = doctor_user_id

    payload = valid_submission_data.model_dump(mode="json")

    # Act: Call the API endpoint
    response = await async_client.post(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == 200
    response_json = response.json()

    submission_uuid = response_json["data"]["submission_uuid"]
    assert isinstance(submission_uuid, str)
    assert uuid.UUID(submission_uuid)

    async with async_tenant_db_session_object.begin():
        result = await async_tenant_db_session_object.execute(
            select(FormSubmission).where(FormSubmission.uuid == submission_uuid)
        )
        created_submission = result.scalar_one()

    assert created_submission is not None
    assert created_submission.patient_user_id == patient_user_id
    assert created_submission.doctor_user_id == doctor_user_id
    assert (
        str(created_submission.form_flow_uuid) == valid_submission_data.form_flow_uuid
    )
    assert created_submission.form_flow_data == payload.get("form_flow_data")


@pytest.mark.asyncio
async def test_create_submission_empty_forms_list_422(
    async_client,
    valid_submission_data,
    _headers_tenant_uuid,
):
    """
    Tests for a 422 error when the 'forms' list is empty.
    """
    # Arrange
    payload = valid_submission_data.model_dump(mode="json")
    payload["form_flow_data"]["forms"] = []  # Make the list empty

    # Act
    response = await async_client.post(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
    )

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]
    assert "forms" in error_detail["loc"]
    assert "at least 1 item" in error_detail["msg"]


@pytest.mark.asyncio
async def test_create_submission_duplicate_form_order_422(
    async_client,
    valid_submission_data,
    _headers_tenant_uuid,
):
    """
    Tests for a 422 error when 'order_index' for forms is duplicated.
    """
    # Arrange
    payload = valid_submission_data.model_dump(mode="json")
    # Add a new form with a conflicting order_index
    duplicate_form = payload["form_flow_data"]["forms"][0].copy()
    payload["form_flow_data"]["forms"].append(duplicate_form)

    # Act
    response = await async_client.post(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
    )

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]
    assert "Duplicate form order index" in error_detail["msg"]


@pytest.mark.asyncio
async def test_create_submission_empty_form_content_422(
    async_client,
    valid_submission_data,
    _headers_tenant_uuid,
):
    """
    Tests for a 422 error when a form contains no items and no groups.
    """
    # Arrange
    payload = valid_submission_data.model_dump(mode="json")
    # Make the form content empty
    payload["form_flow_data"]["forms"][0]["items"] = []
    payload["form_flow_data"]["forms"][0]["groups"] = []

    # Act
    response = await async_client.post(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
    )

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]
    assert "Form cannot be empty" in error_detail["msg"]


@pytest.mark.asyncio
async def test_create_submission_duplicate_group_item_order_422(
    async_client,
    valid_submission_data,
    _headers_tenant_uuid,
):
    """
    Tests for a 422 error when item 'order_index' within a group is duplicated.
    """
    # Arrange
    payload = valid_submission_data.model_dump(mode="json")
    # Add a new item with a conflicting order_index within the same group
    group_items = payload["form_flow_data"]["forms"][0]["groups"][0]["items"]
    duplicate_item = group_items[0].copy()
    group_items.append(duplicate_item)

    # Act
    response = await async_client.post(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
    )

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]
    assert "Duplicate group item order index" in error_detail["msg"]


@pytest.mark.asyncio
async def test_create_submission_missing_answer_field_422(
    async_client,
    valid_submission_data,
    _headers_tenant_uuid,
):
    """
    Tests for a 422 error when the required 'answer' field is completely
    missing from a SubmittedFormItem.
    """
    # Arrange
    payload = valid_submission_data.model_dump(mode="json")

    del payload["form_flow_data"]["forms"][0]["items"][0]["answer"]

    # Act
    response = await async_client.post(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
    )

    # Assert
    assert response.status_code == 422

    error_detail = response.json()["data"][0]
    assert error_detail["loc"] == [
        "body",
        "form_flow_data",
        "forms",
        0,
        "items",
        0,
        "answer",
    ]
    assert error_detail["type"] == "missing"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "entity_to_deactivate, expected_error_code",
    [
        ("form_flow", 500404),
        ("patient", 60003),
        ("doctor", 70003),
    ],
)
async def test_validate_create_form_submission_with_inactive_entities(
    valid_submission_data,
    async_tenant_db_session_object,
    entity_to_deactivate,
    expected_error_code,
):
    """
    Tests that validation fails if related entities exist but are inactive.
    """
    # Arrange: Create all entities in an active state first
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    submission_data = valid_submission_data
    submission_data.patient_user_id = patient_user_id
    submission_data.doctor_user_id = doctor_user_id

    # Deactivate the specific entity for this test case
    async with async_tenant_db_session_object.begin():
        if entity_to_deactivate == "form_flow":
            await async_tenant_db_session_object.execute(
                update(FormFlow)
                .where(FormFlow.uuid == submission_data.form_flow_uuid)
                .values(is_active=False)
            )
        elif entity_to_deactivate == "patient":
            await async_tenant_db_session_object.execute(
                update(PatientUser)
                .where(PatientUser.id == patient_user_id)
                .values(status=False)
            )
        elif entity_to_deactivate == "doctor":
            await async_tenant_db_session_object.execute(
                update(DoctorUser)
                .where(DoctorUser.id == doctor_user_id)
                .values(status=False)
            )

    service = FormSubmissionService(async_tenant_db_session_object)

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_create_form_submission(submission_data)

    assert exc_info.value.status_code == 400
    assert exc_info.value.message_code == expected_error_code


@pytest.mark.asyncio
async def test_create_submission_exception(
    async_client, valid_submission_data, _headers_tenant_uuid
):
    payload = valid_submission_data.model_dump(mode="json")
    with patch.object(
        FormSubmissionService,
        "create_form_submission",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.post(
            "/v1_0/form-submissions", headers=_headers_tenant_uuid, json=payload
        )

    assert response.status_code == 400
    assert response.json()["messageCode"] == 500101


@pytest_asyncio.fixture(scope="function")
async def seeded_form_submission(
    async_tenant_db_session_object,
    valid_submission_data: FormSubmissionCreate,
) -> FormSubmission:
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    submission_payload = valid_submission_data
    submission_payload.patient_user_id = patient_user_id
    submission_payload.doctor_user_id = doctor_user_id

    service = FormSubmissionService(async_tenant_db_session_object)
    submission_uuid = await service.create_form_submission(submission_payload)

    async with async_tenant_db_session_object:
        result = await async_tenant_db_session_object.execute(
            select(FormSubmission).where(FormSubmission.uuid == submission_uuid)
        )
        created_submission = result.scalar_one()
        return created_submission


@pytest.mark.asyncio
async def test_get_detail_form_submission_api_success(
    async_client,
    seeded_form_submission: FormSubmission,
    _headers_tenant_uuid: dict,
):
    """
    Tests successful retrieval of a submission via the API (200 OK).
    """
    # Arrange
    submission_uuid = str(seeded_form_submission.uuid)
    endpoint = f"/v1_0/form-submissions/{submission_uuid}"

    # Act
    response = await async_client.get(endpoint, headers=_headers_tenant_uuid)

    # Assert
    assert response.status_code == 200
    response_json = response.json()

    response_data = response_json["data"]
    assert response_data["patient_user_id"] == seeded_form_submission.patient_user_id
    assert response_data["doctor_user_id"] == seeded_form_submission.doctor_user_id
    assert response_data["form_flow_uuid"] == str(seeded_form_submission.form_flow_uuid)
    assert response_data["form_flow_data"] == seeded_form_submission.form_flow_data


@pytest.mark.asyncio
async def test_get_detail_form_submission_api_not_found(
    async_client,
    _headers_tenant_uuid: dict,
):
    """
    Tests that the API returns a 404 Not Found for a non-existent submission UUID.
    """
    # Arrange
    non_existent_uuid = str(uuid.uuid4())
    endpoint = f"/v1_0/form-submissions/{non_existent_uuid}"

    # Act
    response = await async_client.get(endpoint, headers=_headers_tenant_uuid)

    # Assert
    assert response.status_code == 400
    assert response.json()["messageCode"] == 500111


@pytest.mark.asyncio
async def test_get_detail_form_submission_api_unexpected_exception(
    async_client,
    seeded_form_submission: FormSubmission,
    _headers_tenant_uuid: dict,
):
    with patch.object(
        FormSubmissionService,
        "get_detail_form_submission",
        side_effect=Exception("Something went wrong"),
    ):
        submission_uuid = str(seeded_form_submission.uuid)
        endpoint = f"/v1_0/form-submissions/{submission_uuid}"

        # Act
        response = await async_client.get(endpoint, headers=_headers_tenant_uuid)

        # Assert
        assert response.status_code == 400
        assert response.json()["messageCode"] == 500112


@pytest_asyncio.fixture(scope="function")
async def seeded_submissions_with_profiles(
    async_tenant_db_session_object, truncate_table
) -> list[FormSubmission]:
    await truncate_table(async_tenant_db_session_object, ["form_submissions"])
    submissions = []
    async with async_tenant_db_session_object.begin():
        for i in range(3):
            patient_user_id = await unittest_insert_patient(
                async_tenant_db_session_object,
                custom_profile_fields={
                    "first_name": f"PatientFirst{i}",
                    "last_name": f"PatientLast{i}",
                },
            )
            doctor_user_id = await unittest_insert_doctor(
                async_tenant_db_session_object,
                custom_profile_fields={
                    "first_name": f"DoctorFirst{i}",
                    "last_name": f"DoctorLast{i}",
                },
            )
            form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)

            submission = FormSubmission(
                form_flow_uuid=form_flow_uuid,
                patient_user_id=patient_user_id,
                doctor_user_id=doctor_user_id,
                form_flow_data={"flow_name": f"Flow Name {i}"},
            )
            async_tenant_db_session_object.add(submission)
            submissions.append(submission)

    return sorted(submissions, key=lambda s: s.created_at, reverse=True)


@pytest.mark.asyncio
async def test_list_form_submission_success_with_data(
    async_client,
    async_tenant_db_session_object,
    seeded_submissions_with_profiles,
    _headers_tenant_uuid,
):
    # Act
    response = await async_client.get(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid
    )

    # Assert
    assert response.status_code == 200
    response_json = response.json()["data"]

    assert "items" in response_json
    assert response_json["total"] == 3
    assert len(response_json["items"]) == 3

    # Check data integrity of the first item
    first_item_api = response_json["items"][0]
    first_item_db = seeded_submissions_with_profiles[0]

    assert first_item_api["uuid"] == str(first_item_db.uuid)
    assert first_item_api["flow_name"] == first_item_db.form_flow_data["flow_name"]
    assert first_item_api["patient_profile"]["first_name"] == "PatientFirst2"
    assert first_item_api["doctor_profile"]["first_name"] == "DoctorFirst2"


@pytest.mark.asyncio
async def test_list_form_submission_success_empty_list(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid, truncate_table
):
    await truncate_table(async_tenant_db_session_object, ["form_submissions"])
    # Act
    response = await async_client.get(
        "/v1_0/form-submissions", headers=_headers_tenant_uuid
    )

    # Assert
    assert response.status_code == 200
    response_json = response.json()["data"]

    assert response_json["items"] == []
    assert response_json["total"] == 0


@pytest.mark.asyncio
async def test_list_form_submission_unexpected_exception(
    async_client, _headers_tenant_uuid
):

    # Arrange
    with patch.object(
        FormSubmissionService,
        "list_form_flow_submission",
        side_effect=Exception("Something went wrong"),
    ):

        # Act
        response = await async_client.get(
            "/v1_0/form-submissions", headers=_headers_tenant_uuid
        )

        # Assert
        assert response.status_code == 400
        assert response.json()["messageCode"] == 500130


@pytest.mark.asyncio
async def test_edit_form_submission_api_success(
    async_client,
    seeded_form_submission: FormSubmission,
    _headers_tenant_uuid: dict,
    async_tenant_db_session_object,
):
    """
    Tests the successful update of a submission via the API (200 OK)
    and verifies the change in the database.
    """
    # Arrange
    submission_uuid = str(seeded_form_submission.uuid)
    endpoint = f"/v1_0/form-submissions/{submission_uuid}"

    item_uuid_to_update = seeded_form_submission.form_flow_data["forms"][0]["items"][0][
        "uuid"
    ]
    new_answer = str(uuid.uuid4())

    payload = {"answers": [{"item_uuid": item_uuid_to_update, "answer": new_answer}]}

    # Act
    response = await async_client.put(
        endpoint, headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == 200
    response_json = response.json()
    assert response_json["data"]["submission_uuid"] == submission_uuid
    async with async_tenant_db_session_object:
        updated_submission = await async_tenant_db_session_object.get(
            FormSubmission, submission_uuid
        )
    updated_item = updated_submission.form_flow_data["forms"][0]["items"][0]
    assert updated_item["answer"] == new_answer


@pytest.mark.asyncio
async def test_edit_form_submission_api_not_found(
    async_client,
    _headers_tenant_uuid: dict,
):
    """
    Tests that the API returns a 404 Not Found for a non-existent submission UUID.
    """
    # Arrange
    non_existent_uuid = str(uuid.uuid4())
    endpoint = f"/v1_0/form-submissions/{non_existent_uuid}"
    payload = {"answers": []}

    # Act
    response = await async_client.put(
        endpoint, headers=_headers_tenant_uuid, json=payload
    )

    # Assert
    assert response.status_code == 400
    assert response.json()["messageCode"] == 500111


@pytest.mark.asyncio
async def test_edit_form_submission_api_invalid_payload_422(
    async_client,
    seeded_form_submission: FormSubmission,
    _headers_tenant_uuid: dict,
):
    """
    Tests that the API returns a 422 Unprocessable Entity for a malformed payload.
    """
    # Arrange
    submission_uuid = str(seeded_form_submission.uuid)
    endpoint = f"/v1_0/form-submissions/{submission_uuid}"

    invalid_payload = {"answers": [{"item_uuid": str(uuid.uuid4())}]}

    # Act
    response = await async_client.put(
        endpoint, headers=_headers_tenant_uuid, json=invalid_payload
    )

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]
    assert "answer" in error_detail["loc"]
    assert error_detail["type"] == "missing"


@pytest.mark.asyncio
async def test_edit_form_submission_unexpected_exception(
    async_client, _headers_tenant_uuid
):

    # Arrange
    with patch.object(
        FormSubmissionService,
        "edit_form_submission",
        side_effect=Exception("Something went wrong"),
    ):

        # Act
        submission_uuid = str(uuid.uuid4())
        response = await async_client.put(
            f"/v1_0/form-submissions/{submission_uuid}",
            headers=_headers_tenant_uuid,
            json={"answers": []},
        )

        # Assert
        assert response.status_code == 400
        assert response.json()["messageCode"] == 500141
