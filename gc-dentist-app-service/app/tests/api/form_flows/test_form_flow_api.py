import uuid
from unittest.mock import patch

import pytest
from fastapi import status
from services.form_flows.form_flow_services import FormFlowService
from sqlalchemy import select
from tests.helpers.enums.form_flows import (
    UnittestFormFlowType,
    UnittestFormItemFieldType,
    UnittestFormItemSide,
)
from tests.helpers.insert_data.insert_form import (
    insert_form,
    insert_form_flow,
    insert_form_item,
    insert_form_item_group,
)

from gc_dentist_shared.tenant_models import Form, FormFlow, FormItem, FormItemGroup


@pytest.mark.asyncio
async def test_create_form_flow_success(async_client, _headers_tenant_uuid):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "description": str(uuid.uuid4()),
        "version": "1.0",
        "forms": [
            {
                "form_name": "Form A",
                "description": str(uuid.uuid4()),
                "order_index": 0,
                "groups": [
                    {
                        "title": "Group 1",
                        "order_index": 0,
                        "items": [
                            {
                                "label": "Group Item 1",
                                "field_type": UnittestFormItemFieldType.TEXT,
                                "item_side": UnittestFormItemSide.DOCTOR,
                                "required": True,
                                "order_index": 0,
                            }
                        ],
                    }
                ],
                "items": [
                    {
                        "label": "Single Item 1",
                        "field_type": UnittestFormItemFieldType.TEXT,
                        "item_side": UnittestFormItemSide.DOCTOR,
                        "required": True,
                        "order_index": 1,
                    }
                ],
            }
        ],
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["message"] == "Create form flow successfully."
    assert response_json["data"]["form_flow_uuid"] is not None
    assert isinstance(uuid.UUID(response_json["data"]["form_flow_uuid"]), uuid.UUID)


@pytest.mark.asyncio
async def test_create_form_flow_duplicate_name(async_client, _headers_tenant_uuid):
    flow_name = str(uuid.uuid4())

    payload = {
        "flow_name": flow_name,
        "flow_type": UnittestFormFlowType.SURVEY,
        "description": "Duplicate test",
        "version": "1.0",
        "forms": [
            {
                "form_name": "Form A",
                "description": "desc",
                "order_index": 0,
                "groups": [],
                "items": [
                    {
                        "label": "Item 1",
                        "field_type": UnittestFormItemFieldType.TEXT,
                        "item_side": UnittestFormItemSide.DOCTOR,
                        "required": True,
                        "order_index": 0,
                    }
                ],
            }
        ],
    }

    # First submission — should succeed
    response1 = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )
    assert response1.status_code == status.HTTP_200_OK

    # Second submission — same flow_name, should raise conflict
    response2 = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )
    assert response2.status_code == status.HTTP_409_CONFLICT
    assert response2.json()["messageCode"] == 500001


@pytest.mark.asyncio
async def test_create_form_flow_unexpected_exception(
    async_client, _headers_tenant_uuid
):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "description": "Trigger generic exception",
        "version": "1.0",
        "forms": [
            {
                "form_name": "Test Form",
                "description": "desc",
                "order_index": 0,
                "groups": [],
                "items": [
                    {
                        "label": "Some item",
                        "field_type": UnittestFormItemFieldType.TEXT,
                        "item_side": UnittestFormItemSide.DOCTOR,
                        "required": True,
                        "order_index": 0,
                    }
                ],
            }
        ],
    }

    # Patch the service to throw a generic Exception
    with patch.object(
        FormFlowService,
        "create_form_flows",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.post(
            "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 500008


@pytest.mark.asyncio
async def test_create_form_flow_invalid_pydantic_schema(
    async_client, _headers_tenant_uuid
):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": str(uuid.uuid4()),  # invalid
        "description": str(uuid.uuid4()),
        "version": "1.0",
        "forms": [],
    }

    # Patch the service to throw a generic Exception
    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()
    assert isinstance(response_json["data"], list)
    error_fields = [err["loc"][-1] for err in response_json["data"]]
    assert "flow_type" in error_fields


@pytest.mark.asyncio
async def test_create_form_flow_empty_steps(async_client, _headers_tenant_uuid):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [],  # Invalid: list is empty
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()

    # Check that the error is in the correct field and has the expected message
    error = response_json["data"][0]
    assert "forms" in error["loc"]
    assert "List should have at least 1 item after validation, not 0" in error["msg"]


@pytest.mark.asyncio
async def test_create_form_flow_empty_group_items(async_client, _headers_tenant_uuid):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "forms": [
            {
                "form_name": "Form A",
                "order_index": 0,
                "groups": [
                    {
                        "title": "Empty Group",
                        "order_index": 0,
                        "items": [],
                    }  # Invalid
                ],
            }
        ],
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()

    error = response_json["data"][0]
    assert error["loc"] == ["body", "forms", 0, "groups", 0, "items"]
    assert "List should have at least 1 item after validation, not 0" in error["msg"]


@pytest.mark.asyncio
async def test_create_form_flow_empty_content_form(async_client, _headers_tenant_uuid):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "forms": [
            {
                "form_name": "Form A",
                "order_index": 0,
                "items": [],
                "groups": [],
            },
        ],
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()

    error = response_json["data"][0]
    assert error["loc"] == ["body", "forms", 0]
    assert "Value error, Form cannot be empty!" in error["msg"]


@pytest.mark.asyncio
async def test_create_form_flow_duplicate_form_order(
    async_client, _headers_tenant_uuid
):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "forms": [
            {
                "form_name": "Form A",
                "order_index": 0,
                "items": [
                    {
                        "label": "Item 1",
                        "field_type": UnittestFormItemFieldType.TEXT,
                        "item_side": UnittestFormItemSide.DOCTOR,
                        "required": True,
                        "order_index": 0,  # Duplicate
                    },
                    {
                        "label": "Item 2",
                        "field_type": UnittestFormItemFieldType.TEXT,
                        "item_side": UnittestFormItemSide.DOCTOR,
                        "required": True,
                        "order_index": 0,
                    },  # Duplicate
                ],
            },
        ],
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()

    error = response_json["data"][0]
    assert error["loc"] == ["body", "forms", 0]
    assert "Value error, Duplicate form content order index!" in error["msg"]


@pytest.mark.asyncio
async def test_create_form_flow_duplicate_content_form(
    async_client, _headers_tenant_uuid
):
    """
    Duplicate 'order_index' in items and 'order_index' of groups
    """
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "forms": [
            {
                "form_name": str(uuid.uuid4()),
                "order_index": 0,
                "items": [
                    {
                        "label": str(uuid.uuid4()),
                        "field_type": UnittestFormItemFieldType.TEXT,
                        "item_side": UnittestFormItemSide.DOCTOR,
                        "required": True,
                        "order_index": 0,  # Duplicate
                    },
                ],
                "groups": [
                    {
                        "title": str(uuid.uuid4()),
                        "order_index": 0,  # Duplicate
                        "items": [
                            {
                                "label": str(uuid.uuid4()),
                                "field_type": UnittestFormItemFieldType.TEXT,
                                "item_side": UnittestFormItemSide.DOCTOR,
                                "required": True,
                                "order_index": 0,
                            }
                        ],
                    }
                ],
            },
        ],
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()

    error = response_json["data"][0]
    assert error["loc"] == ["body", "forms", 0]
    assert "Value error, Duplicate form content order index!" in error["msg"]


@pytest.mark.asyncio
async def test_create_form_flow_duplicate_form_group_items(
    async_client, _headers_tenant_uuid
):
    payload = {
        "flow_name": str(uuid.uuid4()),
        "flow_type": UnittestFormFlowType.SURVEY,
        "forms": [
            {
                "form_name": str(uuid.uuid4()),
                "order_index": 0,
                "groups": [
                    {
                        "title": str(uuid.uuid4()),
                        "order_index": 0,
                        "items": [
                            {
                                "label": str(uuid.uuid4()),
                                "field_type": UnittestFormItemFieldType.TEXT,
                                "item_side": UnittestFormItemSide.DOCTOR,
                                "required": True,
                                "order_index": 0,
                            },
                            {
                                "label": str(uuid.uuid4()),
                                "field_type": UnittestFormItemFieldType.TEXT,
                                "item_side": UnittestFormItemSide.DOCTOR,
                                "required": True,
                                "order_index": 0,
                            },
                        ],
                    }
                ],
            },
        ],
    }

    response = await async_client.post(
        "/v1_0/form-flows", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()

    error = response_json["data"][0]
    assert error["loc"] == ["body", "forms", 0, "groups", 0, "items"]
    assert "Value error, Duplicate group item order index!" in error["msg"]


@pytest.mark.asyncio
async def test_get_detail_form_flow_success(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid
):
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        await insert_form_item(
            async_tenant_db_session_object, form_uuid, form_item_group_uuid=group_uuid
        )  # item in group
        await insert_form_item(async_tenant_db_session_object, form_uuid)  # single item
        await insert_form_item(async_tenant_db_session_object, form_uuid)  # single item

    response = await async_client.get(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid
    )
    assert response.status_code == status.HTTP_200_OK
    json_data = response.json()

    assert json_data["message"] == "Get form flow detail successfully."
    assert json_data["data"]["uuid"] == str(flow_uuid)
    assert len(json_data["data"]["forms"]) == 1
    form = json_data["data"]["forms"][0]
    assert len(form["groups"]) == 1
    assert len(form["groups"][0]["items"]) == 1
    assert len(form["items"]) == 2


@pytest.mark.asyncio
async def test_get_detail_form_flow_not_found(async_client, _headers_tenant_uuid):
    fake_uuid = str(uuid.uuid4())
    response = await async_client.get(
        f"/v1_0/form-flows/{fake_uuid}", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["messageCode"] == 500404


@pytest.mark.asyncio
async def test_get_detail_form_flow_exception(async_client, _headers_tenant_uuid):
    test_uuid = str(uuid.uuid4())
    with patch.object(
        FormFlowService,
        "get_detail_form_flow",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get(
            f"/v1_0/form-flows/{test_uuid}", headers=_headers_tenant_uuid
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 500009


@pytest.mark.asyncio
async def test_get_detail_form_flow_invalid_uuid(async_client, _headers_tenant_uuid):
    invalid_uuid = "not-a-valid-uuid"

    response = await async_client.get(
        f"/v1_0/form-flows/{invalid_uuid}", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()
    assert response_json["data"][0]["type"] == "uuid_parsing"
    assert "form_flow_uuid" in response_json["data"][0]["loc"]


@pytest.mark.asyncio
async def test_edit_form_flow_invalid_uuid(async_client, _headers_tenant_uuid):
    invalid_uuid = "not-a-valid-uuid"

    response = await async_client.get(
        f"/v1_0/form-flows/{invalid_uuid}", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_json = response.json()
    assert response_json["data"][0]["type"] == "uuid_parsing"
    assert "form_flow_uuid" in response_json["data"][0]["loc"]


@pytest.mark.asyncio
async def test_edit_form_flow_api_full_coverage(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid
):
    """
    Tests the entire edit form flow via API with a full combination of
    DELETE, UPDATE, and INSERT actions for all entity types (Form, Group, Item).
    """
    # 1. ARRANGE: Setup a comprehensive initial database state
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)

        # --- Entities for UPDATE ---
        form_to_update_uuid = await insert_form(
            async_tenant_db_session_object, flow_uuid
        )
        group_to_update_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_to_update_uuid
        )
        item_to_update_uuid = await insert_form_item(
            async_tenant_db_session_object, form_to_update_uuid
        )

        # --- Entities for DELETE ---
        form_to_delete_uuid = await insert_form(
            async_tenant_db_session_object, flow_uuid
        )
        group_to_delete_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_to_update_uuid
        )
        item_to_delete_uuid = await insert_form_item(
            async_tenant_db_session_object, form_to_update_uuid
        )

    # 2. ARRANGE: Prepare unique data for the request payload
    updated_flow_name = str(uuid.uuid4())
    updated_form_name = str(uuid.uuid4())
    updated_group_title = str(uuid.uuid4())
    updated_item_label = str(uuid.uuid4())
    inserted_form_name = str(uuid.uuid4())
    inserted_group_title = str(uuid.uuid4())
    inserted_item_label = str(uuid.uuid4())

    # 3. ARRANGE: Prepare the complex request payload (as a dictionary)
    payload = {
        "flow_name": updated_flow_name,
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [
            # UPDATE Form
            {
                "action": "UPDATE",
                "uuid": form_to_update_uuid,
                "form_name": updated_form_name,
                "order_index": 0,
                "groups": [
                    # INSERT Group
                    {
                        "action": "CREATE",
                        "title": inserted_group_title,
                        "order_index": 1,
                        "items": [
                            {
                                "action": "CREATE",
                                "label": str(uuid.uuid4()),
                                "order_index": 0,
                                "field_type": UnittestFormItemFieldType.TEXT,
                            }
                        ],
                    },
                    # UPDATE Group
                    {
                        "action": "UPDATE",
                        "uuid": group_to_update_uuid,
                        "title": updated_group_title,
                        "order_index": 2,
                        "items": [
                            {
                                "action": "CREATE",
                                "label": str(uuid.uuid4()),
                                "order_index": 0,
                                "field_type": UnittestFormItemFieldType.TEXT,
                            }
                        ],
                    },
                ],
                "items": [
                    # UPDATE Item
                    {
                        "action": "UPDATE",
                        "uuid": item_to_update_uuid,
                        "label": updated_item_label,
                        "order_index": 3,
                        "field_type": UnittestFormItemFieldType.TEXT,
                    }
                ],
            },
            # INSERT Form
            {
                "action": "CREATE",
                "form_name": inserted_form_name,
                "order_index": 1,
                "items": [
                    # INSERT Item
                    {
                        "action": "CREATE",
                        "label": inserted_item_label,
                        "order_index": 0,
                        "field_type": UnittestFormItemFieldType.TEXT,
                    }
                ],
            },
        ],
    }

    # 4. ACT: Call the API endpoint
    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    # 5. ASSERT: Check API response
    assert response.status_code == status.HTTP_200_OK

    # 6. ASSERT: Verify the final state in the database
    async with async_tenant_db_session_object.begin():
        # --- Assert DELETION ---
        assert (
            await async_tenant_db_session_object.get(Form, form_to_delete_uuid)
        ).is_active is False
        assert (
            await async_tenant_db_session_object.get(
                FormItemGroup, group_to_delete_uuid
            )
        ).is_active is False
        assert (
            await async_tenant_db_session_object.get(FormItem, item_to_delete_uuid)
        ).is_active is False

        # --- Assert UPDATE ---
        assert (
            await async_tenant_db_session_object.get(FormFlow, flow_uuid)
        ).flow_name == updated_flow_name
        assert (
            await async_tenant_db_session_object.get(Form, form_to_update_uuid)
        ).form_name == updated_form_name
        assert (
            await async_tenant_db_session_object.get(
                FormItemGroup, group_to_update_uuid
            )
        ).title == updated_group_title
        assert (
            await async_tenant_db_session_object.get(FormItem, item_to_update_uuid)
        ).label == updated_item_label

        # --- Assert INSERT ---
        assert (
            await async_tenant_db_session_object.execute(
                select(Form).where(Form.form_name == inserted_form_name)
            )
        ).scalar_one_or_none() is not None
        assert (
            await async_tenant_db_session_object.execute(
                select(FormItemGroup).where(FormItemGroup.title == inserted_group_title)
            )
        ).scalar_one_or_none() is not None
        assert (
            await async_tenant_db_session_object.execute(
                select(FormItem).where(FormItem.label == inserted_item_label)
            )
        ).scalar_one_or_none() is not None


@pytest.mark.asyncio
async def test_edit_form_flow_empty_forms_list(async_client, _headers_tenant_uuid):
    """
    Tests validation failure when the 'forms' list is empty.
    Pydantic rule: min_length=1
    """
    flow_uuid = uuid.uuid4()
    payload = {
        "flow_name": "Flow with no forms",
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [],  # Invalid: must have at least one form
    }

    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_detail = response.json()["data"][0]
    assert error_detail["loc"] == ["body", "forms"]
    assert "List should have at least 1 item" in error_detail["msg"]


@pytest.mark.asyncio
async def test_edit_form_flow_duplicate_form_order(async_client, _headers_tenant_uuid):
    """
    Tests validation failure for duplicate order_index among forms.
    Pydantic rule: @model_validator
    """
    flow_uuid = uuid.uuid4()
    payload = {
        "flow_name": "Flow with duplicate form order",
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [
            {
                "action": "CREATE",
                "form_name": "A",
                "order_index": 1,
                "items": [
                    {
                        "action": "CREATE",
                        "label": "i",
                        "order_index": 0,
                        "field_type": "text",
                    }
                ],
            },
            {
                "action": "CREATE",
                "form_name": "B",
                "order_index": 1,
                "items": [
                    {
                        "action": "CREATE",
                        "label": "j",
                        "order_index": 0,
                        "field_type": "text",
                    }
                ],
            },
        ],
    }

    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_detail = response.json()["data"][0]
    assert "Value error, Duplicate form order index!" in error_detail["msg"]


@pytest.mark.asyncio
async def test_edit_form_flow_empty_form_content(async_client, _headers_tenant_uuid):
    """
    Tests validation failure when a form has no items and no groups.
    Pydantic rule: @model_validator
    """
    flow_uuid = uuid.uuid4()
    payload = {
        "flow_name": "Flow with empty form",
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [
            {
                "action": "CREATE",
                "form_name": "Empty Form",
                "order_index": 0,
                "items": [],  # Invalid state
                "groups": [],  # Invalid state
            }
        ],
    }
    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_detail = response.json()["data"][0]
    assert error_detail["loc"] == ["body", "forms", 0]
    assert "Value error, Form cannot be empty!" == error_detail["msg"]


@pytest.mark.asyncio
async def test_edit_form_flow_duplicate_content_order(
    async_client, _headers_tenant_uuid
):
    """
    Tests validation failure for duplicate order_index between a group and a standalone item.
    Pydantic rule: @model_validator
    """
    flow_uuid = uuid.uuid4()
    payload = {
        "flow_name": "Flow with duplicate content order",
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [
            {
                "action": "CREATE",
                "form_name": "Form",
                "order_index": 0,
                "groups": [
                    {
                        "action": "CREATE",
                        "title": "G1",
                        "order_index": 1,
                        "items": [
                            {
                                "action": "CREATE",
                                "label": "i",
                                "order_index": 0,
                                "field_type": "text",
                            }
                        ],
                    }
                ],
                "items": [
                    {
                        "action": "CREATE",
                        "label": "I1",
                        "order_index": 1,
                        "field_type": "text",
                    }
                ],  # Duplicate index
            }
        ],
    }
    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_detail = response.json()["data"][0]
    assert "Value error, Duplicate form content order index!" in error_detail["msg"]


@pytest.mark.asyncio
async def test_edit_form_flow_empty_group_items(async_client, _headers_tenant_uuid):
    """
    Tests validation failure when a group has an empty items list.
    Pydantic rule: min_length=1
    """
    flow_uuid = uuid.uuid4()
    payload = {
        "flow_name": "Flow with empty group",
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [
            {
                "action": "CREATE",
                "form_name": "Form",
                "order_index": 0,
                "groups": [
                    {
                        "action": "CREATE",
                        "title": "Empty Group",
                        "order_index": 0,
                        "items": [],  # Invalid: must have at least one item
                    }
                ],
            }
        ],
    }
    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_detail = response.json()["data"][0]
    assert error_detail["loc"] == ["body", "forms", 0, "groups", 0, "items"]
    assert "List should have at least 1 item" in error_detail["msg"]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload_modifier, expected_msg",
    [
        ({"action": "UPDATE"}, "Value error, UUID is required!"),
        (
            {"action": "CREATE", "uuid": str(uuid.uuid4())},
            "UUID should not be provided when creating.",
        ),
        ({"action": "UPDATE", "uuid": "not-a-valid-uuid"}, "Invalid UUID format!"),
    ],
)
async def test_edit_form_flow_base_uuid_rules(
    async_client, _headers_tenant_uuid, payload_modifier, expected_msg
):
    """
    Tests UUID validation rules from the base schema.
    Pydantic rule: @model_validator
    """
    flow_uuid = uuid.uuid4()
    base_item = {"label": "i", "field_type": "text", "order_index": 0}
    base_item.update(payload_modifier)  # Apply the invalid modification

    payload = {
        "flow_name": "Flow with base errors",
        "flow_type": UnittestFormFlowType.SURVEY,
        "version": "1.0",
        "forms": [
            {
                "action": "CREATE",
                "form_name": "Form",
                "order_index": 0,
                "items": [base_item],
            }
        ],
    }

    response = await async_client.put(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid, json=payload
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_detail = response.json()["data"][0]
    assert "Value error" in error_detail["msg"]
    assert expected_msg in error_detail["msg"]


@pytest.mark.asyncio
async def test_get_list_form_flow_success_with_data(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid, truncate_table
):
    # Arrange: Insert some data into the database
    await truncate_table(async_tenant_db_session_object, ["form_flows"])

    async with async_tenant_db_session_object.begin():
        await insert_form_flow(async_tenant_db_session_object)
        await insert_form_flow(async_tenant_db_session_object)

    # Act: Call the API endpoint
    response = await async_client.get("/v1_0/form-flows", headers=_headers_tenant_uuid)

    # Assert: Check the response
    assert response.status_code == status.HTTP_200_OK
    json_data = response.json()["data"]

    assert "items" in json_data
    assert "total" in json_data
    assert "page" in json_data
    assert "size" in json_data

    assert json_data["total"] == 2
    assert len(json_data["items"]) == 2
    # Check if the structure of a returned item matches the schema
    first_item = json_data["items"][0]
    assert "uuid" in first_item
    assert "flow_name" in first_item
    assert "flow_type" in first_item


@pytest.mark.asyncio
async def test_get_list_form_flow_returns_empty_list(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid, truncate_table
):
    # Arrange: Ensure the table is empty
    await truncate_table(async_tenant_db_session_object, ["form_flows"])

    # Act
    response = await async_client.get("/v1_0/form-flows", headers=_headers_tenant_uuid)

    # Assert
    assert response.status_code == status.HTTP_200_OK
    json_data = response.json()["data"]

    assert json_data["total"] == 0
    assert len(json_data["items"]) == 0


@pytest.mark.asyncio
async def test_get_list_form_flow_filters_inactive_and_orders_correctly(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid, truncate_table
):
    # Arrange: Ensure the table is clean before inserting test-specific data
    await truncate_table(async_tenant_db_session_object, ["form_flows"])
    async with async_tenant_db_session_object.begin():
        # This one is older and active
        await insert_form_flow(
            async_tenant_db_session_object, flow_name="Old Active Flow"
        )
        # This one is inactive and should be filtered out
        await insert_form_flow(
            async_tenant_db_session_object,
            flow_name="Inactive Flow",
            custom_fields=dict(is_active=False),
        )
        # This one is newer and active, should appear first
        flow_name = str(uuid.uuid4())
        newest_flow_uuid = await insert_form_flow(
            async_tenant_db_session_object, flow_name=flow_name
        )

    # Act
    response = await async_client.get("/v1_0/form-flows", headers=_headers_tenant_uuid)

    # Assert
    assert response.status_code == status.HTTP_200_OK
    json_data = response.json()["data"]

    # Should only return the 2 active flows
    assert json_data["total"] == 2
    assert len(json_data["items"]) == 2

    # The first item in the list should be the newest one
    assert json_data["items"][0]["uuid"] == str(newest_flow_uuid)
    assert json_data["items"][0]["flow_name"] == flow_name


@pytest.mark.asyncio
async def test_get_list_form_flow_handles_service_exception(
    async_client, _headers_tenant_uuid
):
    with patch.object(
        FormFlowService,
        "list_form_flow",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get(
            "/v1_0/form-flows", headers=_headers_tenant_uuid
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 500026


@pytest.mark.asyncio
async def test_delete_form_flow_api_success(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid
):
    """
    Tests the success case for the DELETE API endpoint.
    """
    # 1. ARRANGE: Create a deletable form flow in the database
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        item_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid, group_uuid
        )

    # 2. ACT: Call the DELETE endpoint
    response = await async_client.delete(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid
    )

    # 3. ASSERT: Check the API response
    assert response.status_code == status.HTTP_200_OK
    json_data = response.json()
    assert json_data["data"]["form_flow_uuid"] == str(flow_uuid)

    # 4. ASSERT: Verify the data is soft-deleted in the database
    async with async_tenant_db_session_object:
        deleted_flow = await async_tenant_db_session_object.get(FormFlow, flow_uuid)
        deleted_form = await async_tenant_db_session_object.get(Form, form_uuid)
        deleted_group = await async_tenant_db_session_object.get(
            FormItemGroup, group_uuid
        )
        deleted_item = await async_tenant_db_session_object.get(FormItem, item_uuid)

        assert deleted_flow.is_active is False
        assert deleted_form.is_active is False
        assert deleted_group.is_active is False
        assert deleted_item.is_active is False


@pytest.mark.asyncio
async def test_delete_form_flow_api_not_found(async_client, _headers_tenant_uuid):
    """
    Tests that the API returns 404 Not Found when the UUID does not exist.
    """
    # Arrange
    non_existent_uuid = uuid.uuid4()

    # Act
    response = await async_client.delete(
        f"/v1_0/form-flows/{non_existent_uuid}", headers=_headers_tenant_uuid
    )

    # Assert
    assert response.status_code == status.HTTP_404_NOT_FOUND
    json_data = response.json()
    assert json_data["messageCode"] == 500404


@pytest.mark.asyncio
async def test_delete_form_flow_api_not_deletable(
    async_client, async_tenant_db_session_object, _headers_tenant_uuid
):
    """
    Tests that the API returns 400 Bad Request when the form flow is not deletable.
    """
    # 1. ARRANGE: Create a non-deletable form flow
    async with async_tenant_db_session_object.begin():
        flow_uuid = await insert_form_flow(
            async_tenant_db_session_object, custom_fields=dict(is_deletable=False)
        )

    # 2. ACT: Attempt to delete it via the API
    response = await async_client.delete(
        f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid
    )

    # 3. ASSERT: Check for the correct error response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    json_data = response.json()
    assert json_data["messageCode"] == 500032


@pytest.mark.asyncio
async def test_delete_form_flow_handles_service_exception(
    async_client, _headers_tenant_uuid
):
    flow_uuid = str(uuid.uuid4())
    with patch.object(
        FormFlowService,
        "delete_form_flow",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.delete(
            f"/v1_0/form-flows/{flow_uuid}", headers=_headers_tenant_uuid
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 500031
