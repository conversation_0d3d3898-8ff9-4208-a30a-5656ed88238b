from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from tests.helpers.enums.master_data import MasterModelEnum
from tests.helpers.header_mock import get_headers


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params,expected_status,expected_success,expected_message,expected_data_check",
    [
        (
            {
                "master_name": MasterModelEnum.M_POSTAL_CODE.value,
                "items": ["postal_code", "id"],
                "filter": '{"postal_code":"0600000"}',
            },
            200,
            True,
            None,
            lambda data: all("postal_code" in item and "id" in item for item in data),
        ),
        (
            {
                "master_name": MasterModelEnum.M_PREFECTURE.value,
                "items": ["id", "name_json"],
                "filter": '{"id":10}',
            },
            200,
            True,
            None,
            lambda data: all("id" in item and "name_json" in item for item in data),
        ),
        (
            {
                "master_name": "invalid_master_name",
                "items": ["id", "name_json"],
                "filter": '{"id":10}',
            },
            422,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
            None,
        ),
        (
            {
                "master_name": MasterModelEnum.M_PREFECTURE.value,
                "items": ["id", "invalid_item"],
                "filter": '{"id":10}',
            },
            400,
            False,
            CustomMessageCode.MASTER_DATA_COLUMN_NOT_EXIST.title,
            None,
        ),
        (
            {
                "master_name": MasterModelEnum.M_PREFECTURE.value,
                "items": ["id", "name_json"],
                "filter": '{"invalid":10}',
            },
            400,
            False,
            CustomMessageCode.MASTER_DATA_COLUMN_NOT_EXIST.title,
            None,
        ),
        (
            {
                "master_name": MasterModelEnum.M_PREFECTURE.value,
                "items": ["id", "name_json"],
                "filter": "invalid_json",
            },
            400,
            False,
            CustomMessageCode.MASTER_DATA_INVALID_FILTER.title,
            None,
        ),
        (
            {
                "master_name": MasterModelEnum.M_POSTAL_CODE.value,
                "items": ["postal_code", "id"],
                "filter": '{"postal_code":"invalid_postal_code"}',
            },
            200,
            True,
            None,
            lambda data: isinstance(data, list) and len(data) == 0,
        ),
    ],
)
async def test_get_item_master_data_parametrized(
    async_client,
    tenant_uuid,
    params,
    expected_status,
    expected_success,
    expected_message,
    expected_data_check,
):
    headers = get_headers(tenant_uuid)
    response = await async_client.get(
        "/v1_0/master-data/items",
        headers=headers,
        params=params,
    )
    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is expected_success
    if expected_message is not None:
        assert result["message"] == expected_message
    if expected_success and expected_data_check is not None:
        assert isinstance(result["data"], list)
        assert expected_data_check(result["data"])
    if expected_success is False and expected_message is not None:
        assert result["message"] == expected_message


@pytest.mark.asyncio
async def test_get_item_master_api_exception(async_client, tenant_uuid):
    headers = get_headers(tenant_uuid)
    with patch(
        "services.master_data_service.MasterDataService.get_master_data_items",
        side_effect=Exception("Database error"),
    ):
        params = {
            "master_name": MasterModelEnum.M_POSTAL_CODE.value,
            "items": ["postal_code", "id"],
            "filter": '{"postal_code":"0600000"}',
        }
        response = await async_client.get(
            "/v1_0/master-data/items",
            headers=headers,
            params=params,
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["message"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.title
        assert result["messageCode"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.code
