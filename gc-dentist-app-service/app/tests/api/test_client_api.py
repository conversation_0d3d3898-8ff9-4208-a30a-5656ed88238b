# # import copy
# import uuid
# from datetime import datetime, timezone
# from unittest.mock import patch

# import pytest
# from core.common.hmac import generate_hmac_signature
# from core.exception_handler.max_retry_exception import MaxRetriesExceeded
# from core.messages import ERROR_PROCESS


# @pytest.fixture
# def valid_headers():
#     time_request = str(int(datetime.now(timezone.utc).timestamp() * 1000))
#     signature = generate_hmac_signature(message=str(time_request))
#     return {
#         "X-Signature": signature,
#         "X-Timestamp": str(time_request),
#     }


# @pytest.mark.asyncio
# async def test_create_client(async_client, valid_headers):
#     headers = valid_headers

#     public_key = str(uuid.uuid4())
#     response = await async_client.post(
#         "/v1_0/client",
#         json={
#             "client_name": str(uuid.uuid4()),
#             "public_key": public_key,
#             "sbi_client_id": str(uuid.uuid4()),
#             "sbi_client_secret": str(uuid.uuid4()),
#         },
#         headers=headers,
#     )

#     client_uuid = response.json()["client_uuid"]
#     assert response.status_code == 200

#     resp_public_key = await async_client.get(
#         f"/v1_0/client/{client_uuid}/public_key", headers=headers
#     )
#     assert resp_public_key.status_code == 200
#     assert resp_public_key.json()["public_key"] == public_key

#     resp_deactive_client = await async_client.put(
#         f"/v1_0/client/{client_uuid}/deactivate", headers=headers
#     )
#     assert resp_deactive_client.status_code == 200


# @pytest.mark.asyncio
# async def test_create_client_invalid(async_client, valid_headers):
#     response = await async_client.post(
#         "/v1_0/client",
#         json={
#             "client_name": "",
#             # "public_key": "",
#             "sbi_client_id": str(uuid.uuid4()),
#             "sbi_client_secret": str(uuid.uuid4()),
#         },
#         headers=valid_headers,
#     )
#     assert response.status_code == 422


# @pytest.mark.asyncio
# @pytest.mark.parametrize(
#     "mock_error",
#     [
#         Exception("Mocked database error"),
#         MaxRetriesExceeded("MaxRetriesExceeded", Exception("Mocked database error")),
#     ],
# )
# @patch("api.client_api.ClientManagementService.create_client_management")
# async def test_create_client_with_exception(
#     mock_create_client, mock_error, async_client, valid_headers
# ):
#     mock_create_client.side_effect = mock_error

#     response = await async_client.post(
#         "/v1_0/client",
#         json={
#             "client_name": str(uuid.uuid4()),
#             "public_key": str(uuid.uuid4()),
#             "sbi_client_id": str(uuid.uuid4()),
#             "sbi_client_secret": str(uuid.uuid4()),
#         },
#         headers=valid_headers,
#     )
#     assert response.status_code == 400
#     assert response.json()["detail"] == ERROR_PROCESS


# @pytest.mark.asyncio
# @pytest.mark.parametrize(
#     "mock_error",
#     [
#         Exception("Mocked database error"),
#         MaxRetriesExceeded("MaxRetriesExceeded", Exception("Mocked database error")),
#     ],
# )
# @patch("api.client_api.ClientManagementService.get_client_public_key")
# async def test_get_public_key_with_exception(
#     mock_create_client, mock_error, async_client, valid_headers
# ):
#     mock_create_client.side_effect = mock_error

#     client_uuid = str(uuid.uuid4())
#     response = await async_client.get(
#         f"/v1_0/client/{client_uuid}/public_key", headers=valid_headers
#     )
#     assert response.status_code == 400
#     assert response.json()["detail"] == ERROR_PROCESS
