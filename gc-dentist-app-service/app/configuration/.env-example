ENVIRONMENT='develop'
PROJECT_NAME='Payment Gateway Service'

BACKEND_CORS_ORIGINS='["http://localhost:3000"]'

POSTGRES_SERVER=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_PORT=5432
DB_ECHO=false
DB_INIT=false

READ_ONLY_POSTGRES_SERVER=
READ_ONLY_POSTGRES_USER=
READ_ONLY_POSTGRES_PASSWORD=
READ_ONLY_POSTGRES_PORT=5432

# Center Database Name
POSTGRES_GLOBAL_DB_NAME='central_local'
READ_ONLY_POSTGRES_GLOBAL_DB_NAME='central_local'

# Template Database for clinic tenant using build migration files 
DB_NAME_TEMPLATE = 'clinic_template'

COMMUNICATE_SECRET_KEY=

AES_SECRET_ID_ROTATION=
AWS_SECRET_ROTATION_KEY_MAPPING=
AWS_SECRET_CURRENT_VERSION=
AES_SECRET_KEY_MAPPING=

SES_REGION_NAME=
SES_FROM_MAIL=

# AWS S3 configuration
S3_BUCKET_NAME=
S3_FOLDER_NAME=
EXPIRED_GENERATE_PRESIGNED_URL=

IAPO_ACCESS_TOKEN=
IAPO_URL=


# Redis configuration
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
REDIS_DATABASE=
REDIS_TIMEOUT=
REDIS_SSL=


# Twilio configuration
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_SERVICE_SID=
TWILIO_MESSAGE_SERVICE_SID=

#Firebase
FIREBASE_ENABLED=
FIREBASE_DRY_RUN=
FIREBASE_CERT_PATH=

#Cloudfront
#path: configuration/.keys/xxxxx.pem
CLOUDFRONT_URL=
CLOUDFRONT_PRIVATE_KEY=
CLOUDFRONT_PRIVATE_KEY_PATH=
CLOUDFRONT_PUBLIC_KEY_ID=
CLOUDFRONT_SIGNED_URL_EXPIRE_MINUTES=

# Auth configuration
AUTH_SERVICE_JWKS_URL=
AUTH_SERVICE_JWKS_LIFESPAN=
JWT_ALGORITHM=

TOKEN_EXCLUDE_URLS=

# OPA (Open Policy Agent) configuration
OPA_SERVICE_BASE_URL=http://localhost:8181

