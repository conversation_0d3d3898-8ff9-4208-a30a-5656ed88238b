#!/usr/bin/env python3
import re
from datetime import datetime, timezone
from pathlib import Path

from core.constants import MAPPING_LANG_REGION
from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from gc_dentist_shared.core.common.i18n import i18n
from gc_dentist_shared.core.logger.config import log


class LanguageMiddleware(BaseHTTPMiddleware):
    """Record request logging middleware"""

    @staticmethod
    def get_language(lang_code):
        if not lang_code:
            return "en_US"
        languages = re.findall(
            r"([a-z]{2}-[A-Z]{2}|[a-z]{2})(;q=\d.\d{1,3})?", lang_code
        )
        languages = sorted(
            languages, key=lambda x: x[1], reverse=True
        )  # sort the priority, no priority comes last
        translation_directory = Path("locale")
        translation_files = [i.name for i in translation_directory.iterdir()]
        explicit_priority = None

        for lang in languages:
            lang_code = MAPPING_LANG_REGION.get(lang[0][:2]) or "en-US"
            lang_folder = lang_code.replace("-", "_")
            if lang_folder in translation_files:
                if not lang[
                    1
                ]:  # languages without quality value having the highest priority 1
                    return lang_folder
                elif (
                    not explicit_priority
                ):  # set language with explicit priority <= priority 1
                    explicit_priority = lang[0]

        # Return language with explicit priority or default value
        return explicit_priority if explicit_priority else "en_US"

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        try:
            lang_code: str | None = request.headers.get("Accept-Language", None)
            i18n.set_language(language=self.get_language(lang_code))
            start_time = datetime.now(timezone.utc)
            response: Response = await call_next(request)
            end_time = datetime.now(timezone.utc)
            log.info(
                f"{response.status_code} {request.client.host} {request.method} {request.url} {end_time - start_time}"
            )
        except Exception as exc:
            """
            Catch other exception as db connection, ASGI, etc, v.v that not catch by app.exception_handler
            Root cause: if exception is not caught in app.exception_handler,
            it will return response error without CORS header
            """

            log.error(f"Unhandled exception: {exc}")

            import traceback

            log.error(traceback.format_exc())

            response = JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": i18n("Server internal error")},
            )
        return response
