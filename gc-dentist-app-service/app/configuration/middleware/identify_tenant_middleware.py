from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from db.db_connection import CentralDatabase
from fastapi import Request
from sqlalchemy import text
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from gc_dentist_shared.core.common.utils import is_valid_uuid
from gc_dentist_shared.core.constants import X_TENANT_UUID
from gc_dentist_shared.core.messages import CustomMessageCode


async def get_db_name_for_tenant(tenant_uuid: str) -> str:
    db_session = await CentralDatabase().get_instance_db()
    async with db_session.begin():
        result = await db_session.execute(
            text("SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"),
            {"tenant_uuid": tenant_uuid},
        )

        db_name = result.scalar_one_or_none()
        return db_name


class IdentifyTenantMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path in configuration.TENANT_EXCLUDE_URLS:
            return await call_next(request)

        tenant_uuid = request.headers.get(X_TENANT_UUID)
        if not tenant_uuid or not is_valid_uuid(tenant_uuid):
            return JSONResponse(
                {"error": CustomMessageCode.TENANT_UUID_HEADER_REQUIRED.title},
                status_code=401,
            )

        db_name = await get_db_name_for_tenant(tenant_uuid)
        if not db_name:
            return JSONResponse(
                {"error": CustomMessageCode.TENANT_NOT_FOUND.title}, status_code=401
            )

        token = set_current_db_name(db_name)
        try:
            response = await call_next(request)
            return response
        finally:
            reset_current_db_name(token)
