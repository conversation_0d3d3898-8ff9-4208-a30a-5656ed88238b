from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.tenant_models.patient_profiles import PatientProfile
from gc_dentist_shared.tenant_models.patient_users import PatientUser
from gc_dentist_shared.tenant_models.reservations import Reservation


class ReservationService:
    async def _update_or_create_patient(
        self, db_session: AsyncSession, data: list[dict]
    ):
        stmt = insert(PatientUser).values(data)
        upsert_stmt = stmt.on_conflict_do_update(
            index_elements=["external_patient_id", "source"],
            set_={
                col.name: getattr(stmt.excluded, col.name)
                for col in PatientUser.__table__.columns
                if col.name not in {"id"}
            },
        ).returning(PatientUser.id, PatientUser.external_patient_id)

        result = await db_session.execute(upsert_stmt)

        return result.fetchall()

    async def _update_or_create_patient_info(
        self, db_session: AsyncSession, data: list[dict]
    ):
        stmt = insert(PatientProfile).values(data)
        upsert_stmt = stmt.on_conflict_do_update(
            index_elements=["patient_user_id"],
            set_={
                col.name: getattr(stmt.excluded, col.name)
                for col in PatientProfile.__table__.columns
                if col.name not in {"id"}
            },
        )

        return await db_session.execute(upsert_stmt)

    async def _update_or_create_reservation(
        self, db_session: AsyncSession, data: list[dict]
    ):
        stmt = insert(Reservation).values(data)

        stmt = stmt.on_conflict_do_update(
            index_elements=["external_reservation_code", "source"],
            set_={
                col.name: getattr(stmt.excluded, col.name)
                for col in Reservation.__table__.columns
                if col.name not in {"id"}
            },
        ).returning(Reservation.id)

        result = await db_session.execute(stmt)

        return result.fetchall()
