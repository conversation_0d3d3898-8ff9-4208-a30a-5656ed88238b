from enums.patient_enum import EmergencyFlag, PatientWaitingStatus
from enums.reservation_status_enum import ReservationStatusEnum
from schemas.requests.reservation_schema import SyncDataReservationToPatientWaiting
from sqlalchemy import text
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common import utils
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.logger.config import log


class PatientWaitingService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="sync_data_from_reservation",
    )
    async def sync_data_from_reservation(
        self, params: SyncDataReservationToPatientWaiting
    ):
        try:
            source = params.source or None

            log.info(
                f"[Reservation-PatientWaiting] Start sync data from source: {source}"
            )

            target_date = utils.get_date_or_now(params.target_date)

            base_sql = """
                INSERT INTO patient_waitings (
                    reservation_id,
                    patient_user_id,
                    status,
                    visit_start_date,
                    visit_start_time,
                    visit_end_date,
                    visit_end_time,
                    coming_time,
                    emergency_flag,
                    created_at
                )
                SELECT
                    r.id AS reservation_id,
                    r.patient_user_id AS patient_user_id,
                    CASE
                        WHEN r.status = CAST(:status_booked AS int) THEN CAST(:status_scheduled AS int)
                    ELSE CAST(:status_canceled AS int)
                    END AS status,
                    r.schedule_visit_date AS visit_start_date,
                    r.schedule_visit_time AS visit_start_time,
                    r.schedule_visit_date AS visit_end_date,
                    (r.extra_data->>'visit_end_time_utc')::time AS visit_end_time,
                    (r.schedule_visit_date + r.schedule_visit_time) AS coming_time,
                    :default_emergency_flag as emergency_flag,
                    now() AS created_at
                FROM reservations r
                LEFT JOIN patient_waitings pw ON pw.reservation_id = r.id
                WHERE r.schedule_visit_date = :target_date
                    AND (
                        r.status = :status_booked
                        OR pw.id IS NOT NULL
                    )
            """

            if source:
                base_sql += " AND r.source = :source"

            base_sql += """
                ON CONFLICT (reservation_id) DO UPDATE
                SET
                    status = EXCLUDED.status,
                    visit_start_date = EXCLUDED.visit_start_date,
                    visit_start_time = EXCLUDED.visit_start_time,
                    visit_end_date = EXCLUDED.visit_end_date,
                    visit_end_time = EXCLUDED.visit_end_time,
                    updated_at = now()
                WHERE patient_waitings.status = ANY(:status_list)
            """

            binding_params = {
                "default_emergency_flag": EmergencyFlag.NORMAL.value,
                "target_date": target_date,
                "status_booked": ReservationStatusEnum.STATUS_BOOKED.value,
                "status_scheduled": PatientWaitingStatus.SCHEDULED.value,
                "status_canceled": ReservationStatusEnum.STATUS_CANCELED.value,
                "status_list": [PatientWaitingStatus.SCHEDULED.value],
            }
            if source:
                binding_params["source"] = source

            async with self.session.begin():
                result = await self.session.execute(text(base_sql), binding_params)
                count_data = result.rowcount or 0

                log.info(
                    f"[Reservation-PatientWaiting] Finish sync data from source: [{source}] "
                    f"with total data : [{count_data}]"
                )

                return count_data
        except Exception as e:
            log.error(f"[Reservation-PatientWaiting] Error sync data: {e}")
            return 0
