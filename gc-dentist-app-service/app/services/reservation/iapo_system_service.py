import json
import re
from datetime import date, datetime

from configuration.settings import Settings
from core.constants import COUNTRY_CODE_JP, IAPO_RESULT_SUCCESS
from enums.patient_enum import Gender
from enums.reservation_status_enum import ReservationStatusEnum
from enums.reservation_system_enum import ReservationSystemEnum
from fastapi import status
from schemas.requests.patient_requests import (
    CreatePatientFromExternalSystem,
    UpdateOrCreatePatientProfile,
)
from schemas.requests.reservation_schema import CreateReservation
from schemas.responses.reservation_schema import GetListReservationResponse
from services.reservation.reservation_service import ReservationService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common import utils
from gc_dentist_shared.core.common.http_request import SingletonRequestApi
from gc_dentist_shared.core.logger.config import log


class IAPOSystemService(ReservationService):
    def __init__(self, session: AsyncSession):
        self.url = Settings().IAPO_URL
        self.headers = {
            "Accept": "application/json",
            "Authorization": Settings().IAPO_ACCESS_TOKEN,
        }
        self.session = session

    async def get_list(self, params: dict, clinic_id: str):
        target_date = (params.target_date or date.today()).isoformat()
        response = await self._request_api_list(target_date, clinic_id)

        reservations = (
            response.get("reservations", [])
            if response.get("result") == IAPO_RESULT_SUCCESS
            else []
        )

        success_count = 0
        if reservations:
            success_count = await self._mapping_data(reservations, clinic_id)

        return GetListReservationResponse(
            list_reservation=reservations, success_count=success_count
        )

    async def _mapping_data(self, reservations, clinic_id: str) -> int:
        if not reservations:
            return 0

        log.info("[IAPO] Start Get list and mapping data reservation")

        try:
            async with self.session.begin():
                data_patient = []
                data_patient_info = []
                data_reservation = []
                processed_external_ids = set()
                count = 0
                for item in reservations:
                    external_patient = await self.get_patient_info(
                        clinic_id, item.get("private_id")
                    )

                    if not external_patient:
                        continue

                    if item.get("private_id") not in processed_external_ids:
                        data_patient.append(
                            self._prepare_data_patient(external_patient).model_dump()
                        )

                        data_patient_info.append(
                            self._prepare_data_patient_profile(
                                external_patient
                            ).model_dump()
                        )

                        processed_external_ids.add(item.get("private_id"))

                    data_reservation.append(
                        self._prepare_data_reservation(item).model_dump()
                    )

                if data_patient:
                    patient_upsert = await self._update_or_create_patient(
                        self.session, data_patient
                    )
                    patient_id_map = {
                        external_id: patient_id
                        for patient_id, external_id in patient_upsert
                    }

                if data_patient_info and patient_id_map:
                    await self._update_or_create_patient_info(
                        self.session,
                        self.assign_patient_user_id_to_profiles(
                            data_patient_info, patient_id_map
                        ),
                    )

                if data_reservation and patient_id_map:
                    reservation_upsert = await self._update_or_create_reservation(
                        self.session,
                        self.assign_patient_user_id_to_reservations(
                            data_reservation, patient_id_map
                        ),
                    )

                    count = len(reservation_upsert)

            log.info(
                f"[IAPO] Finish get list and mapping data reservation with count data : {count}"
            )

            return count
        except Exception as e:
            log.error(f" ❌ ------ [IAPO] Error Mapping data reservation: {e}")
            raise e

    def _prepare_data_patient(
        self, external_patient: dict
    ) -> CreatePatientFromExternalSystem:
        return CreatePatientFromExternalSystem(
            username=external_patient.get("patient_no"),
            patient_no=external_patient.get("patient_no"),
            external_patient_id=str(external_patient.get("private_id")),
            source=ReservationSystemEnum.IAPO.value,
            is_adult=True,
        )

    def _prepare_data_patient_profile(
        self, external_patient: dict
    ) -> UpdateOrCreatePatientProfile:
        return UpdateOrCreatePatientProfile(
            first_name=external_patient["f_name"],
            last_name=external_patient["l_name"],
            first_name_kana=external_patient.get("f_name_kana") or None,
            last_name_kana=external_patient.get("l_name_kana") or None,
            date_of_birth=external_patient["birthday"],
            gender=Gender.MALE if external_patient["sex"] else Gender.FEMALE,
            phone=re.sub(r"-", "", external_patient.get("tel_private") or ""),
            country_code=COUNTRY_CODE_JP,
            external_patient_id=str(external_patient.get("private_id")),
        )

    def _prepare_data_reservation(self, reservation: dict):
        date_merge = (
            f"{reservation.get('tab_date')} {reservation.get('tab_start_time')}:00"
        )
        visit_date_convert = utils.convert_datetime_to_utc_specify_timezone(date_merge)

        visit_date = visit_date_convert.strftime("%Y-%m-%d")
        visit_time = visit_date_convert.strftime("%H:%M:%S")

        schedule_visit_date = datetime.strptime(visit_date, "%Y-%m-%d").date()
        schedule_visit_time = datetime.strptime(visit_time, "%H:%M:%S").time()

        end_date_merge = (
            f"{reservation.get('tab_date')} {reservation.get('tab_end_time')}:00"
        )
        visit_end_time_utc = utils.convert_datetime_to_utc_specify_timezone(
            end_date_merge
        )
        reservation["visit_end_time_utc"] = visit_end_time_utc.strftime("%H:%M:%S")

        is_canceled = bool(reservation.get("tab_delete", 0))
        status = (
            ReservationStatusEnum.STATUS_CANCELED.value
            if is_canceled
            else ReservationStatusEnum.STATUS_BOOKED.value
        )

        return CreateReservation(
            external_reservation_code=reservation.get("tab_id"),
            external_patient_id=str(reservation.get("private_id")),
            source=ReservationSystemEnum.IAPO.value,
            schedule_visit_date=schedule_visit_date,
            schedule_visit_time=schedule_visit_time,
            status=status,
            extra_data=json.dumps(reservation),
        )

    def assign_patient_user_id_to_profiles(
        self, profiles: list[dict], patient_id_map: dict[str, int]
    ) -> list[dict]:
        for profile in profiles:
            external_id = profile["external_patient_id"]
            profile["date_of_birth"] = str(profile["date_of_birth"])
            profile["patient_user_id"] = patient_id_map.get(external_id)
            del profile["external_patient_id"]

        return profiles

    def assign_patient_user_id_to_reservations(
        self, reservations: list[dict], patient_id_map: dict[str, int]
    ) -> list[dict]:
        for reservation in reservations:
            external_id = reservation["external_patient_id"]
            reservation["patient_user_id"] = patient_id_map.get(external_id)

        return reservations

    async def get_patient_info(self, clinic_id: str, external_patient_id: str):
        response = await self._request_api_patient_profile(
            clinic_id, external_patient_id
        )

        return (
            response.get("patient")
            if response.get("result") == IAPO_RESULT_SUCCESS
            else None
        )

    async def _request_api_list(self, target_date: str, clinic_id):
        try:
            client_request = await SingletonRequestApi.get_instance()
            data = {"clinic_id": clinic_id, "tab_date": target_date}

            status_code, response = await client_request.request(
                url=f"{self.url}/asua/get_reservations_list.php",
                method="POST",
                json=data,
                headers=self.headers,
            )

            return {} if status_code != status.HTTP_200_OK else response

        except RuntimeError as e:
            log.error(f"❌ Error in _request_api_list func list: {e}")
            return {}

    async def _request_api_patient_profile(
        self, clinic_id: str, external_patient_id: str
    ):
        try:
            client_request = await SingletonRequestApi.get_instance()
            data = {"clinic_id": clinic_id, "private_id": external_patient_id}

            status_code, response = await client_request.request(
                url=f"{self.url}/asua/get_patient_info_by_id.php",
                method="POST",
                json=data,
                headers=self.headers,
            )

            return {} if status_code != status.HTTP_200_OK else response
        except RuntimeError as e:
            log.error(f"❌ Error in _request_api_patient_profile func : {e}")
            return {}
