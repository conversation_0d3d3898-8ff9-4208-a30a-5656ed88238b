from core.messages import Custom<PERSON><PERSON>ageCode
from db.data_access_object import <PERSON><PERSON><PERSON>
from fastapi import status
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.form_submission import FormSubmissionCreate, FormSubmissionUpdate
from schemas.responses.form_submission import FormSubmissionDetailSchema
from services.form_flows.form_flow_services import FormFlowService
from sqlalchemy import func, select, update
from sqlalchemy.exc import DB<PERSON><PERSON>rror, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    FormSubmission,
    PatientProfile,
    PatientUser,
)


class FormSubmissionService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            <PERSON><PERSON>rror,
            DBAPIError,
        ),
        log_prefix="create_form_submission",
    )
    async def create_form_submission(self, submission_data: Form<PERSON><PERSON>mission<PERSON>reate):
        await self._validate_create_form_submission(submission_data)

        new_submission = FormSubmission(
            form_flow_uuid=submission_data.form_flow_uuid,
            doctor_user_id=submission_data.doctor_user_id,
            patient_user_id=submission_data.patient_user_id,
            form_flow_data=submission_data.form_flow_data.model_dump(),
        )

        async with self.session.begin():
            self.session.add(new_submission)
            await self.session.flush()
            return str(new_submission.uuid)

    async def _validate_create_form_submission(
        self, submission_data: FormSubmissionCreate
    ) -> None:
        submission_dao = BaseDAO(FormSubmission, self.session)
        form_flow_dao = BaseDAO(FormFlow, self.session)
        patient_dao = BaseDAO(PatientUser, self.session)
        doctor_dao = BaseDAO(DoctorUser, self.session)

        if await submission_dao.exists(
            patient_user_id=submission_data.patient_user_id,
            form_flow_uuid=submission_data.form_flow_uuid,
            is_active=True,
        ):
            raise CustomValueError(
                status_code=status.HTTP_409_CONFLICT,
                message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_ALREADY_EXISTS.code,
                message=CustomMessageCode.FORM_SUBMISSION_ERROR_ALREADY_EXISTS.title,
            )

        if not await form_flow_dao.exists(
            uuid=submission_data.form_flow_uuid, is_active=True
        ):
            raise CustomValueError(
                message_code=CustomMessageCode.FORM_FLOW_ERROR_NOT_FOUND.code,
                message=CustomMessageCode.FORM_FLOW_ERROR_NOT_FOUND.title,
            )

        if not await patient_dao.exists(
            id=submission_data.patient_user_id, status=True
        ):
            raise CustomValueError(
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
            )

        if not await doctor_dao.exists(id=submission_data.doctor_user_id, status=True):
            raise CustomValueError(
                message_code=CustomMessageCode.DOCTOR_NOT_FOUND.code,
                message=CustomMessageCode.DOCTOR_NOT_FOUND.title,
            )

        await self._validate_submission_structure(submission_data)

    async def _validate_submission_structure(
        self, submission_data: FormSubmissionCreate
    ) -> None:
        form_flow_service = FormFlowService(self.session)
        db_form_flow_data = await form_flow_service.get_detail_form_flow(
            submission_data.form_flow_uuid
        )

        mapping_data = form_flow_service.mapping_data_uuid(db_form_flow_data)
        db_forms_map = mapping_data["db_forms_map"]
        db_groups_map = mapping_data["db_groups_map"]
        db_items_map = mapping_data["db_items_map"]

        submitted_flow = submission_data.form_flow_data

        for submitted_form in submitted_flow.forms:
            if submitted_form.uuid not in db_forms_map:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_FORM_NOT_FOUND.code,
                    message=CustomMessageCode.FORM_SUBMISSION_ERROR_FORM_NOT_FOUND.title.format(
                        uuid=submitted_form.uuid
                    ),
                )

            for submitted_group in submitted_form.groups:
                if submitted_group.uuid not in db_groups_map:
                    raise CustomValueError(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_GROUP_NOT_FOUND.code,
                        message=CustomMessageCode.FORM_SUBMISSION_ERROR_GROUP_NOT_FOUND.title.format(
                            uuid=submitted_group.uuid
                        ),
                    )

                for submitted_item in submitted_group.items:
                    if submitted_item.uuid not in db_items_map:
                        raise CustomValueError(
                            message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_ITEM_NOT_FOUND.code,
                            message=CustomMessageCode.FORM_SUBMISSION_ERROR_ITEM_NOT_FOUND.title.format(
                                uuid=submitted_item.uuid
                            ),
                        )

            for submitted_item in submitted_form.items:
                if submitted_item.uuid not in db_items_map:
                    raise CustomValueError(
                        message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_ITEM_NOT_FOUND.code,
                        message=CustomMessageCode.FORM_SUBMISSION_ERROR_ITEM_NOT_FOUND.title.format(
                            uuid=submitted_item.uuid
                        ),
                    )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_detail_form_submission",
    )
    async def get_detail_form_submission(self, submission_uuid: str) -> dict:
        submission_dao = BaseDAO(FormSubmission, self.session)
        db_submission = await submission_dao.get_one_by(
            fields=[
                "form_flow_uuid",
                "patient_user_id",
                "doctor_user_id",
                "form_flow_data",
            ],
            uuid=submission_uuid,
            is_active=True,
        )
        if not db_submission:
            raise CustomValueError(
                message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_NOT_FOUND.code,
                message=CustomMessageCode.FORM_SUBMISSION_ERROR_NOT_FOUND.title,
            )
        return FormSubmissionDetailSchema(**db_submission._asdict()).model_dump(
            mode="json"
        )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="list_form_flow_submission",
    )
    async def list_form_flow_submission(self):
        query = (
            select(
                FormSubmission.uuid,
                FormSubmission.form_flow_uuid,
                FormSubmission.patient_user_id,
                FormSubmission.doctor_user_id,
                FormSubmission.form_flow_data["flow_name"]
                .as_string()
                .label("flow_name"),
                func.jsonb_build_object(
                    "last_name",
                    DoctorProfile.last_name,
                    "first_name",
                    DoctorProfile.first_name,
                    "last_name_kana",
                    DoctorProfile.last_name_kana,
                    "first_name_kana",
                    DoctorProfile.first_name_kana,
                ).label("doctor_profile"),
                func.jsonb_build_object(
                    "last_name",
                    PatientProfile.last_name,
                    "first_name",
                    PatientProfile.first_name,
                    "last_name_kana",
                    PatientProfile.last_name_kana,
                    "first_name_kana",
                    PatientProfile.first_name_kana,
                ).label("patient_profile"),
            )
            .select_from(FormSubmission)
            .join(
                DoctorProfile,
                FormSubmission.doctor_user_id == DoctorProfile.doctor_user_id,
            )
            .join(
                PatientProfile,
                FormSubmission.patient_user_id == PatientProfile.patient_user_id,
            )
            .where(FormSubmission.is_active.is_(True))
            .order_by(FormSubmission.created_at.desc())
        )

        return await paginate(self.session, query, unique=False)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="edit_form_submission",
    )
    async def edit_form_submission(
        self, submission_uuid: str, update_data: FormSubmissionUpdate
    ) -> str:
        current_data = await self._validate_edit_form_submission(
            update_data, submission_uuid
        )
        async with self.session.begin():
            await self.session.execute(
                update(FormSubmission)
                .where(FormSubmission.uuid == submission_uuid)
                .values(
                    form_flow_data=current_data,
                )
            )

        return submission_uuid

    async def _validate_edit_form_submission(self, update_data, submission_uuid):
        submission_dao = BaseDAO(FormSubmission, self.session)
        db_submission = await submission_dao.get_one_by(
            fields=[
                "form_flow_data",
            ],
            uuid=submission_uuid,
            is_active=True,
        )
        if not db_submission:
            raise CustomValueError(
                message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_NOT_FOUND.code,
                message=CustomMessageCode.FORM_SUBMISSION_ERROR_NOT_FOUND.title,
            )

        current_data = db_submission.form_flow_data

        items_map = {}
        for form in current_data.get("forms", []):
            for item in form.get("items", []):
                items_map[item["uuid"]] = item

            for group in form.get("groups", []):
                for item in group.get("items", []):
                    items_map[item["uuid"]] = item

        for answer_update in update_data.answers:
            item_uuid_to_update = answer_update.item_uuid
            if item_uuid_to_update not in items_map:
                raise CustomValueError(
                    message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_ITEM_NOT_IN_SUBMISSION.code,
                    message=CustomMessageCode.FORM_SUBMISSION_ERROR_ITEM_NOT_IN_SUBMISSION.title,
                )
            items_map[item_uuid_to_update]["answer"] = answer_update.answer

        return current_data
