from schemas.responses.permission_schema import (
    PermissionListResponse,
    PermissionResponse,
)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.tenant_models.doctor_roles import <PERSON><PERSON><PERSON>
from gc_dentist_shared.tenant_models.permissions import Permission
from gc_dentist_shared.tenant_models.role_permissions import RolePermission


class PermissionService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_current_user_permissions(
        self,
        user_id: int,
        db_session: AsyncSession,
    ) -> PermissionListResponse:
        doctor_role_result = await db_session.execute(
            select(DoctorRole).where(DoctorRole.doctor_user_id == user_id)
        )
        doctor_role = doctor_role_result.scalars().all()

        role_key_ids = [role.role_key_id for role in doctor_role]

        role_permissions_result = await db_session.execute(
            select(
                Permission.permission_key,
                Permission.module,
                Permission.sub_module,
                Permission.action,
            )
            .select_from(RolePermission)
            .join(
                Permission,
                RolePermission.permission_id == Permission.id,
            )
            .where(
                RolePermission.role_key_id.in_(role_key_ids),
                RolePermission.delete_flag.is_(False),
                Permission.delete_flag.is_(False),
            )
        )

        role_permissions = role_permissions_result.mappings().all()

        return PermissionListResponse(
            items=[
                PermissionResponse(
                    action=row.action,
                    permission_key=row.permission_key,
                    module=row.module,
                    subModule=row.sub_module or None,
                )
                for row in role_permissions
            ]
        )
