from typing import Optional

from core.messages import CustomMessageCode
from enums.medical_template import MedicalT<PERSON><PERSON><PERSON>tatus
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.medical_template_requests import (
    CreateMedicalTemplatePayload,
    UpdateMedicalTemplatePayload,
)
from schemas.responses.medical_template_schema import (
    ListMedicalTemplateGroupSchema,
    MedicalTemplateSchema,
)
from sqlalchemy import func, select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models.document_group import DocumentGroup
from gc_dentist_shared.tenant_models.template.medical_template import MedicalTemplate


class MedicalTemplateService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            <PERSON><PERSON><PERSON><PERSON>,
            DBAPIError,
        ),
        log_prefix="create_document_template",
    )
    async def create_medical_template(
        self, data: CreateMedicalTemplatePayload, user_id: Optional[int] = None
    ) -> int:
        """Create a new medical template"""
        exists_document_group = await self._check_document_group(data.document_group_id)
        if not exists_document_group:
            raise CustomValueError(
                message=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
                message_code=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
            )

        async with self.session.begin():
            medical_template = MedicalTemplate(
                name=data.name,
                page_data=data.page_data,
                status=MedicalTemplateStatus.ACTIVATED,
                size=data.size,
                tag=data.tag,
                document_group_id=data.document_group_id,
                created_by=user_id,
            )
            self.session.add(medical_template)
            await self.session.flush()
            return medical_template.id

    async def _check_document_group(self, document_group_id: int):
        if document_group_id is not None:
            stmt = select(DocumentGroup).where(DocumentGroup.id == document_group_id)
            async with self.session:
                result = await self.session.execute(stmt)
                return result.scalar_one_or_none()

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_medical_template",
    )
    async def get_medical_template(
        self, medical_template_id: int
    ) -> MedicalTemplateSchema:
        """Get a medical template by id"""
        async with self.session:
            query = select(
                MedicalTemplate.id,
                MedicalTemplate.name,
                MedicalTemplate.page_data,
                MedicalTemplate.status,
                MedicalTemplate.size,
                MedicalTemplate.tag,
                MedicalTemplate.document_group_id,
            ).where(MedicalTemplate.id == medical_template_id)
            result = await self.session.execute(query)
            row = result.mappings().first()

            if not row:
                raise CustomValueError(
                    message_code=CustomMessageCode.MEDICAL_TEMPLATE_NOT_FOUND.code,
                    message=CustomMessageCode.MEDICAL_TEMPLATE_NOT_FOUND.title,
                )
            return MedicalTemplateSchema(**row)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_medical_template",
    )
    async def update_medical_template(
        self, medical_template_id: int, data: UpdateMedicalTemplatePayload
    ) -> int:
        """Update a medical template by id"""
        async with self.session.begin():
            medical_template = await self.session.get(
                MedicalTemplate, medical_template_id
            )
            if not medical_template:
                raise CustomValueError(
                    message_code=CustomMessageCode.MEDICAL_TEMPLATE_NOT_FOUND.code,
                    message=CustomMessageCode.MEDICAL_TEMPLATE_NOT_FOUND.title,
                )
            exists_document_group = await self._check_document_group(
                data.document_group_id
            )
            if not exists_document_group:
                raise CustomValueError(
                    message=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
                    message_code=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
                )

            for field, value in data.model_dump(exclude_unset=True).items():
                setattr(medical_template, field, value)

            await self.session.flush()
            await self.session.refresh(medical_template)

            return medical_template.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="delete_medical_template",
    )
    async def deactivate_medical_template(self, medical_template_id: int) -> int:
        """Deactivate a medical template by id"""
        async with self.session.begin():
            medical_template = await self.session.get(
                MedicalTemplate, medical_template_id
            )
            if not medical_template:
                raise CustomValueError(
                    message_code=CustomMessageCode.MEDICAL_TEMPLATE_NOT_FOUND.code,
                    message=CustomMessageCode.MEDICAL_TEMPLATE_NOT_FOUND.title,
                )

            medical_template.status = MedicalTemplateStatus.INACTIVATED
            await self.session.flush()
            await self.session.refresh(medical_template)

            return medical_template.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_medical_template",
    )
    async def get_list_medical_template(
        self, search: str | None = None
    ) -> Page[ListMedicalTemplateGroupSchema]:
        medical_template_json = func.json_build_object(
            "id",
            MedicalTemplate.id,
            "name",
            MedicalTemplate.name,
            "size",
            MedicalTemplate.size,
            "tag",
            MedicalTemplate.tag,
            "document_group_id",
            MedicalTemplate.document_group_id,
        )
        async with self.session:
            query = (
                select(
                    DocumentGroup.id.label("document_group_id"),
                    DocumentGroup.name.label("document_group_name"),
                    func.json_agg(medical_template_json).label("templates"),
                )
                .select_from(MedicalTemplate)
                .join(
                    DocumentGroup, MedicalTemplate.document_group_id == DocumentGroup.id
                )
                .where(MedicalTemplate.status == MedicalTemplateStatus.ACTIVATED)
                .group_by(DocumentGroup.id, DocumentGroup.name)
                .order_by(DocumentGroup.name.asc())
            )

            if search:
                search_pattern = f"%{search}%"
                query = query.where(
                    (MedicalTemplate.name.ilike(search_pattern))
                    | (DocumentGroup.name.ilike(search_pattern))
                )

            return await paginate(self.session, query, unique=False)
