from configuration.settings import configuration
from core.common.redis_key import RedisManager
from core.constants import KEY_LENGTH, OTP_EXPIRY_MINUTES
from core.messages import CustomMessageCode
from enums.clinic_enum import RoleEnum
from enums.redis_enum import PrefixEnum, RedisKeyName, RedisRoleEnum
from enums.relationship_enum import (
    RelationshipRequestCount,
    RelationshipRole,
    RelationshipStatus,
)
from fastapi import status
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.patient_relationship_schema import (
    CreateRelationshipRequest,
    OTPVerificationRequest,
)
from schemas.responses.patient_relationship_schema import (
    PatientRelationshipDetail,
    PatientRelationshipListSchema,
    PatientRelationshipSchema,
)
from sqlalchemy import and_, func, select, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.common.twilio import TwilioService
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models.patient_profiles import PatientProfile
from gc_dentist_shared.tenant_models.patient_relationship_requests import (
    PatientRelationshipRequest,
)
from gc_dentist_shared.tenant_models.patient_user_relationship import (
    PatientUserRelationship,
)
from gc_dentist_shared.tenant_models.patient_users import PatientUser

STATUS_ERROR_MAP: dict[RelationshipStatus, CustomMessageCode] = {
    RelationshipStatus.REJECTED: CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_REJECTED,
    RelationshipStatus.DISCONNECTED: CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_DISCONNECTED,
}

RELATIONSHIP_ROLE_MAP: dict[RelationshipRole, dict[str, str]] = {
    RelationshipRole.MAIN: (
        PatientUserRelationship.sub_patient_user_id,
        PatientUserRelationship.patient_user_id,
    ),
    RelationshipRole.SUB: (
        PatientUserRelationship.patient_user_id,
        PatientUserRelationship.sub_patient_user_id,
    ),
}


class PatientRelationshipService:
    def __init__(
        self,
        session: AsyncSession,
        configuration=None,
        redis_manager: RedisManager | None = None,
    ):
        self.session = session
        self.redis_manager = redis_manager or (
            RedisManager(configuration) if configuration else None
        )

    async def validate_full_relationship_state(
        self, requester_user_id: int, target_user_id: int
    ):
        """Validate that both users exist and no active relationship exists"""
        async with self.session:
            query_count = (
                select(func.count())
                .select_from(PatientUser)
                .where(
                    PatientUser.id.in_([requester_user_id, target_user_id]),
                    PatientUser.status.is_(True),
                )
            )
            result_count = await self.session.execute(query_count)
            count = result_count.scalar()
            if count != 2:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=CustomMessageCode.USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.USER_NOT_FOUND.code,
                )

            """Validate that the relationship request already exists"""
            query_request = select(PatientRelationshipRequest).where(
                and_(
                    PatientRelationshipRequest.requester_user_id == requester_user_id,
                    PatientRelationshipRequest.target_user_id == target_user_id,
                )
            )
            result_request = await self.session.execute(query_request)
            request_relation = result_request.scalar_one_or_none()

            if request_relation:
                if request_relation.status not in STATUS_ERROR_MAP:
                    raise CustomValueError(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_CREATION_FAILED.title,
                        message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_CREATION_FAILED.code,
                    )
                return request_relation

            return None

    async def create_relationship_request(
        self,
        request: CreateRelationshipRequest,
    ) -> int:
        relationship_request = await self.validate_full_relationship_state(
            request.requester_user_id, request.target_user_id
        )
        if relationship_request:
            return await self.handle_existing_relationship_request(
                relationship_request, request
            )
        else:
            return await self.create_new_relationship_request(request)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="handle_existing_relationship_request",
    )
    async def handle_existing_relationship_request(
        self,
        relationship_request: PatientRelationshipRequest,
        request: CreateRelationshipRequest,
    ) -> int:
        """Handle update or block for existing relationship request."""
        async with self.session.begin():
            stmt_request = (
                update(PatientRelationshipRequest)
                .where(PatientRelationshipRequest.id == relationship_request.id)
                .values(
                    status=RelationshipStatus.PENDING,
                    updated_by=request.requester_user_id,
                )
            )
            await self.session.execute(stmt_request)

        return relationship_request.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_new_relationship_request",
    )
    async def create_new_relationship_request(
        self, request: CreateRelationshipRequest
    ) -> int:
        """Create new relationship request."""
        async with self.session.begin():
            relationship_request = PatientRelationshipRequest(
                **request.model_dump(),
                status=RelationshipStatus.PENDING,
                request_count=1,
            )
            self.session.add(relationship_request)
            await self.session.flush()
            return relationship_request.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="approve_patient_relationship_request",
    )
    async def approve_patient_relationship_request(self, request_id: int) -> int:
        """Approve a patient relationship request"""
        async with self.session:
            stmt = select(PatientRelationshipRequest).where(
                and_(
                    PatientRelationshipRequest.id == request_id,
                    PatientRelationshipRequest.status == RelationshipStatus.PENDING,
                )
            )
            result = await self.session.execute(stmt)
            relationship_request = result.scalar_one_or_none()

            if not relationship_request:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_APPROVAL_FAILED.title,
                    message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_APPROVAL_FAILED.code,
                )

        phone_requester = await self.get_phone_requester(
            relationship_request.requester_user_id
        )

        async with self.session.begin():
            otp = await self.redis_manager.create_data(
                user_id=relationship_request.requester_user_id,
                role=RedisRoleEnum.PATIENT,
                prefix=PrefixEnum.VERIFY_OTP,
                data={},
                expiry_minutes=OTP_EXPIRY_MINUTES,
                key_length=KEY_LENGTH,
                key_name=RedisKeyName.OTP,
            )
            await self.send_otp_to_phone(phone_requester, otp)

            stmt_request = (
                update(PatientRelationshipRequest)
                .where(PatientRelationshipRequest.id == request_id)
                .values(
                    status=RelationshipStatus.APPROVED,
                    approved_by_id=None,
                    approved_role=RoleEnum.ADMIN,
                    request_count=0,
                )
            )
            await self.session.execute(stmt_request)

        return relationship_request.id

    async def get_phone_requester(self, patient_user_id: int) -> str:
        async with self.session:
            phone_query = select(
                PatientProfile.phone,
                PatientProfile.country_code,
            ).where(PatientProfile.patient_user_id == patient_user_id)

            result = await self.session.execute(phone_query)
            row = result.mappings().first()

        if not row or not row.phone:
            raise CustomValueError(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=CustomMessageCode.PATIENT_PHONE_MISSING.title,
                message_code=CustomMessageCode.PATIENT_PHONE_MISSING.code,
            )

        aes_gcm = AesGCMRotation(configuration)
        decrypted_phone = aes_gcm.decrypt_data(row.phone)
        if not decrypted_phone:
            raise CustomValueError(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=CustomMessageCode.PATIENT_PHONE_MISSING.title,
                message_code=CustomMessageCode.PATIENT_PHONE_MISSING.code,
            )

        phone_number = format_phone_number(
            phone=decrypted_phone,
            country_code=row.country_code,
        )

        if not phone_number:
            raise CustomValueError(
                message=CustomMessageCode.VALUE_ERROR_INVALID_PHONE_NUMBER_FORMAT.title,
                message_code=CustomMessageCode.VALUE_ERROR_INVALID_PHONE_NUMBER_FORMAT.code,
            )

        return phone_number

    def send_otp_to_phone(self, phone_number: str, otp: str):
        """
        Send our generated OTP via SMS using Twilio messaging API.
        """
        twilio_service = TwilioService.get_client(configuration)
        message = CustomMessageCode.VERIFICATION_OTP_MESSAGE.title.format(
            otp=otp, minutes=OTP_EXPIRY_MINUTES
        )
        twilio_service.send_message(to=phone_number, body=message)
        return True

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="reject_patient_relationship_request",
    )
    async def reject_patient_relationship_request(self, request_id: int) -> int:
        """Reject a patient relationship request"""
        async with self.session.begin():
            stmt = select(PatientRelationshipRequest).where(
                and_(
                    PatientRelationshipRequest.id == request_id,
                    PatientRelationshipRequest.status == RelationshipStatus.PENDING,
                )
            )
            result = await self.session.execute(stmt)
            relationship_request = result.scalar_one_or_none()

            if not relationship_request:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_REJECT_FAILED.title,
                    message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_REJECT_FAILED.code,
                )

            next_request_count = relationship_request.request_count + 1
            is_over_limit = (
                next_request_count >= RelationshipRequestCount.MAX_REQUEST_COUNT
            )
            new_status = (
                RelationshipStatus.BLOCKED
                if is_over_limit
                else RelationshipStatus.REJECTED
            )
            stmt_request = (
                update(PatientRelationshipRequest)
                .where(PatientRelationshipRequest.id == request_id)
                .values(
                    status=new_status,
                    request_count=next_request_count,
                )
            )
            await self.session.execute(stmt_request)

        return relationship_request.id

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="verify_patient_relationship_request",
    )
    async def verify_patient_relationship_request(
        self, request: OTPVerificationRequest
    ) -> int:
        """Verify a patient relationship request"""
        relationship_request = await self._get_approved_relationship_request(
            request.request_id
        )
        if not await self._is_requester_and_otp_valid(request, relationship_request):
            raise CustomValueError(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED.title,
                message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED.code,
            )

        await self._mark_success_and_create_relationship(request, relationship_request)
        return request.request_id

    async def _get_approved_relationship_request(
        self, request_id: int
    ) -> PatientRelationshipRequest:
        async with self.session:
            stmt = select(PatientRelationshipRequest).where(
                and_(
                    PatientRelationshipRequest.id == request_id,
                    PatientRelationshipRequest.status == RelationshipStatus.APPROVED,
                )
            )
            result = await self.session.execute(stmt)
            relationship_request = result.scalar_one_or_none()

            if not relationship_request:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED.title,
                    message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED.code,
                )
            return relationship_request

    async def _is_requester_and_otp_valid(self, request, relationship_request) -> bool:
        # TODO: After will check from token authentication
        if relationship_request.requester_user_id != request.user_id:
            return False

        data = await self.redis_manager.get_data(
            PrefixEnum.VERIFY_OTP,
            request.user_id,
            RedisRoleEnum.PATIENT,
            RedisKeyName.OTP,
        )

        if not data:
            return False

        if data.get(RedisKeyName.OTP) != request.otp:
            return False

        await self.redis_manager.delete_data(
            PrefixEnum.VERIFY_OTP,
            request.user_id,
            RedisRoleEnum.PATIENT,
            RedisKeyName.OTP,
        )
        return True

    async def _mark_success_and_create_relationship(
        self,
        request: OTPVerificationRequest,
        relationship_request: PatientRelationshipRequest,
    ) -> None:
        async with self.session.begin():
            stmt_request = (
                update(PatientRelationshipRequest)
                .where(PatientRelationshipRequest.id == request.request_id)
                .values(
                    status=RelationshipStatus.SUCCESS,
                    request_count=0,
                )
            )
            await self.session.execute(stmt_request)

            relationship = await self.session.get(
                PatientUserRelationship, relationship_request.id
            )
            if relationship:
                relationship.is_active = True
            else:
                relationship = PatientUserRelationship(
                    patient_user_id=request.user_id,
                    sub_patient_user_id=relationship_request.target_user_id,
                    relationship=relationship_request.relationship,
                    request_id=relationship_request.id,
                    is_active=True,
                    # TODO: Add created_by after integrating the authentication middleware
                )
            self.session.add(relationship)
            await self.session.flush()

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="release_patient_relationship_request",
    )
    async def release_patient_relationship(self, relationship_id: int) -> int:
        """Release a patient relationship request"""
        async with self.session.begin():
            relationship = await self.session.get(
                PatientUserRelationship, relationship_id
            )

            if not relationship:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_RELEASE_FAILED.title,
                    message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_RELEASE_FAILED.code,
                )

            relationship.is_active = False
            stmt_request = (
                update(PatientRelationshipRequest)
                .where(
                    PatientRelationshipRequest.requester_user_id
                    == relationship.patient_user_id,
                    PatientRelationshipRequest.target_user_id
                    == relationship.sub_patient_user_id,
                )
                .values(
                    status=RelationshipStatus.DISCONNECTED,
                    request_count=0,
                )
            )
            await self.session.execute(stmt_request)
            await self.session.flush()
            return relationship_id

    async def _get_relationship_patients(
        self, patient_user_id: int, relationship_role: RelationshipRole
    ) -> list[PatientRelationshipDetail]:
        """Get relationship patients for a given patient user ID and relationship type"""
        async with self.session:
            join_field, where_field = RELATIONSHIP_ROLE_MAP[relationship_role]

            query = (
                select(
                    PatientUser.id.label("patient_id"),
                    PatientProfile.first_name,
                    PatientProfile.last_name,
                    PatientProfile.first_name_kana,
                    PatientProfile.last_name_kana,
                    PatientUser.patient_no,
                    PatientUserRelationship.relationship,
                )
                .select_from(PatientUserRelationship)
                .join(PatientUser, join_field == PatientUser.id)
                .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
                .where(
                    where_field == patient_user_id,
                    PatientUserRelationship.is_active.is_(True),
                )
            )

            result = await self.session.execute(query)
            return [
                PatientRelationshipDetail(**dict(row))
                for row in result.mappings().all()
            ]

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="get_patient_relationship",
    )
    async def get_patient_relationship(
        self, patient_user_id: int
    ) -> PatientRelationshipSchema:
        async with self.session:
            patient_user = await self.session.get(PatientUser, patient_user_id)
            if not patient_user:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=CustomMessageCode.USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.USER_NOT_FOUND.code,
                )

            query = (
                select(
                    PatientUser.id,
                    PatientUser.patient_no,
                    PatientProfile.first_name,
                    PatientProfile.last_name,
                    PatientProfile.first_name_kana,
                    PatientProfile.last_name_kana,
                )
                .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
                .where(PatientUser.id == patient_user_id, PatientUser.status.is_(True))
            )

            row = (await self.session.execute(query)).mappings().first()
            if not row:
                raise CustomValueError(
                    status_code=status.HTTP_404_NOT_FOUND,
                    message=CustomMessageCode.PATIENT_NOT_FOUND.title,
                    message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                )

            main_patients = await self._get_relationship_patients(
                patient_user_id, RelationshipRole.SUB
            )
            sub_patients = await self._get_relationship_patients(
                patient_user_id, RelationshipRole.MAIN
            )

            row_dict = dict(row)
            row_dict["main_patients"] = main_patients
            row_dict["sub_patients"] = sub_patients

            return PatientRelationshipSchema(**row_dict)

    def relationship_count_subqueries(self):
        def count_subquery(role: RelationshipRole) -> dict:
            from_field = RELATIONSHIP_ROLE_MAP[role][0]
            return (
                select(func.count(PatientUserRelationship.id))
                .where(
                    PatientUserRelationship.is_active.is_(True),
                    from_field == PatientUser.id,
                )
                .scalar_subquery()
            )

        main_patients_count = count_subquery(RelationshipRole.MAIN)
        sub_patients_count = count_subquery(RelationshipRole.SUB)
        return {
            "main_patients_count": main_patients_count,
            "sub_patients_count": sub_patients_count,
        }

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="get_patient_relationship_list",
    )
    async def get_patient_relationship_list(
        self, search: str | None = None
    ) -> Page[PatientRelationshipListSchema]:
        """Get list of patients with relationship counts and pagination"""
        async with self.session:
            count_subqueries = self.relationship_count_subqueries()
            query = (
                select(
                    PatientUser.id,
                    PatientUser.patient_no,
                    PatientProfile.first_name,
                    PatientProfile.last_name,
                    PatientProfile.first_name_kana,
                    PatientProfile.last_name_kana,
                    PatientProfile.gender,
                    count_subqueries["main_patients_count"].label(
                        "main_patients_count"
                    ),
                    count_subqueries["sub_patients_count"].label("sub_patients_count"),
                )
                .select_from(PatientUser)
                .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
                .where(PatientUser.status.is_(True))
                .order_by(PatientUser.created_at.desc())
            )

            if search:
                search_pattern = f"%{search}%"
                query = query.where(
                    (PatientUser.patient_no.ilike(search_pattern))
                    | (PatientProfile.last_name.ilike(search_pattern))
                    | (PatientProfile.first_name.ilike(search_pattern))
                    | (PatientProfile.last_name_kana.ilike(search_pattern))
                    | (PatientProfile.first_name_kana.ilike(search_pattern))
                )

            return await paginate(self.session, query, unique=False)
