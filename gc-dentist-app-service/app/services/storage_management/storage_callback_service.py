from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from sqlalchemy import select

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import TenantConfiguration


async def get_tenant_storage_from_tenant_db(tenant_uuid: str) -> int:
    tenant_session = None
    try:
        tenant_session = await TenantDatabase.get_instance_tenant_db()

        async with tenant_session.begin():
            query = select(TenantConfiguration).where(
                TenantConfiguration.tenant_uuid == tenant_uuid
            )

            result = await tenant_session.execute(query)
            tenant_config = result.scalar_one_or_none()

            if not tenant_config:
                raise CustomValueError(
                    message=CustomMessageCode.TENANT_CONFIGURATION_NOT_FOUND.title,
                    message_code=CustomMessageCode.TENANT_CONFIGURATION_NOT_FOUND.code,
                )

            default_storage = tenant_config.default_storage or 0
            extra_storage = tenant_config.extra_storage or 0
            total_storage = default_storage + extra_storage

            log.info(
                f"📊 Tenant storage from tenant DB for {tenant_uuid}: {total_storage}GB"
            )

            return total_storage

    except CustomValueError:
        raise
    except Exception as e:
        log.error(
            f"❌ Database error getting storage limit for tenant {tenant_uuid}: {e}"
        )
        raise CustomValueError(
            message=CustomMessageCode.TENANT_CONFIGURATION_GET_FAILED.title,
            message_code=CustomMessageCode.TENANT_CONFIGURATION_GET_FAILED.code,
        )
    finally:
        if tenant_session:
            await tenant_session.close()
