from core.messages import CustomMessageCode
from schemas.requests.medical_history_schema import (
    MedicalHistoryCreate,
    MedicalHistoryFilter,
    MedicalHistoryUpdate,
)
from schemas.responses.medical_history_schema import (
    MedicalHistoryFilterData,
    MedicalHistoryResponse,
    MedicalHistoryUpdateResponse,
)
from services.common.validate_service import ValidateService
from sqlalchemy import func, select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import MedicalHistory, PatientWaiting


class MedicalHistoryService:
    def __init__(self, session: AsyncSession = None):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="func_medical_history_with_patient_waiting_id",
    )
    async def medical_history_with_patient_waiting_id(
        self, patient_waiting_id: int, db_session: AsyncSession
    ):
        """
        Check if a medical history record exists for the given patient waiting ID.
        :param patient_waiting_id: ID of the patient waiting record.
        :param db_session: Database session to use for the query.
        :return: Medical history ID if exists, otherwise None.
        """
        medical_history = await db_session.execute(
            select(MedicalHistory.id).where(
                MedicalHistory.patient_waiting_id == patient_waiting_id
            )
        )
        medical_history = medical_history.scalar_one_or_none()
        if medical_history:
            return medical_history

        patient_waiting = await db_session.execute(
            select(PatientWaiting.id).where(PatientWaiting.id == patient_waiting_id)
        )
        patient_waiting_id = patient_waiting.scalar_one_or_none()
        if not patient_waiting_id:
            raise CustomValueError(
                message=CustomMessageCode.PATIENT_WAITING_NOT_FOUND.title,
                message_code=CustomMessageCode.PATIENT_WAITING_NOT_FOUND.code,
            )
        return None

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_medical_history",
    )
    async def create_medical_history(
        self, data: MedicalHistoryCreate, db_session: AsyncSession
    ):
        """
        Create a new medical history record in the database.
        :param data: Data for creating a medical history.
        :return: None
        """

        medical_history_id = await self.medical_history_with_patient_waiting_id(
            patient_waiting_id=data.patient_waiting_id,
            db_session=db_session,
        )
        if medical_history_id:
            return medical_history_id

        medical_history = MedicalHistory(**data.model_dump())
        db_session.add(medical_history)
        await db_session.flush()
        await db_session.refresh(medical_history)

        return medical_history.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_medical_history",
    )
    async def update_medical_history(
        self,
        medical_history_id: int,
        data: MedicalHistoryUpdate,
    ) -> MedicalHistoryUpdateResponse:
        """
        Update an existing medical history record in the database.
        :param data: Data for updating a medical history.
        :return: None
        """
        async with self.session.begin():
            if data.doctor_user_ids:
                await ValidateService().validate_doctor_users(
                    doctor_ids=data.doctor_user_ids,
                    db_session=self.session,
                )
            medical_history = await self.session.get(MedicalHistory, medical_history_id)
            if not medical_history:
                raise CustomValueError(
                    CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.title,
                    CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.code,
                )

            for field, value in data.model_dump(exclude_unset=True).items():
                setattr(medical_history, field, value)

            await self.session.flush()
            await self.session.refresh(medical_history)

        return MedicalHistoryUpdateResponse(medical_history_id=medical_history.id)

    def build_query_get_medical_history(self, obj: MedicalHistoryFilter):
        """
        Build a SQLAlchemy query filter based on the provided MedicalHistoryFilter object.
        :param obj: MedicalHistoryFilter object containing filter criteria.
        :return: SQLAlchemy query filter.
        """
        select_fields = [
            MedicalHistory.id,
            MedicalHistory.patient_user_id,
            MedicalHistory.patient_waiting_id,
            MedicalHistory.doctor_user_ids,
            MedicalHistory.status,
            MedicalHistory.visit_start_datetime,
        ]
        attr_map = {
            "medical_history_id": lambda v: MedicalHistory.id == v,
            "patient_user_id": lambda v: MedicalHistory.patient_user_id == v,
            "patient_waiting_id": lambda v: MedicalHistory.patient_waiting_id == v,
            "status": lambda v: MedicalHistory.status == v,
            "visit_start_datetime": lambda v: func.date(
                MedicalHistory.visit_start_datetime
            )
            == v,
        }
        filters = [
            condition_filter(getattr(obj, attr))
            for attr, condition_filter in attr_map.items()
            if getattr(obj, attr)
        ]
        orders = [MedicalHistory.id.asc()]
        query = select(*select_fields).where(*filters).order_by(*orders)
        return query

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_medical_history",
    )
    async def get_medical_history(
        self, obj: MedicalHistoryFilter
    ) -> MedicalHistoryResponse:
        """
        Retrieve a medical history record by its ID.
        :param medical_history_id: ID of the medical history record.
        :return: MedicalHistory object.
        """
        async with self.session:
            query = self.build_query_get_medical_history(obj)
            results = await self.session.execute(query)
            medical_history = results.mappings().all()
            return MedicalHistoryResponse(
                dataset=[
                    MedicalHistoryFilterData(**record).model_dump(mode="json")
                    for record in medical_history
                ]
            )
