from core.messages import CustomMessageCode
from sqlalchemy import select
from sqlalchemy.exc import DB<PERSON><PERSON>rror, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import Doctor<PERSON><PERSON>, PatientUser


class ValidateService:
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_doctor_user",
    )
    async def validate_doctor_users(
        self,
        doctor_ids: list[int],
        db_session: AsyncSession,
    ):
        """
        Validate if a doctor user exists with the given ID.
        :param doctor_id: ID of the doctor user.
        :return: DoctorUser object if exists, otherwise raises CustomValueError.
        """
        query = select(DoctorUser.id).where(DoctorUser.id.in_(doctor_ids))
        result = await db_session.execute(query)
        doctors = result.scalars().all()
        missing_doctors = set(doctor_ids) - set(doctors)
        if missing_doctors:
            log.error(f"Doctors not found: {missing_doctors}")
            raise CustomValueError(
                message=CustomMessageCode.DOCTOR_NOT_FOUND.title,
                message_code=CustomMessageCode.DOCTOR_NOT_FOUND.code,
            )
        return True

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_patient_user",
    )
    async def validate_patient_user(self, patient_id: int, db_session: AsyncSession):
        """
        Validate if a patient user exists with the given ID.
        :param patient_id: ID of the patient user.
        :return: PatientUser object if exists, otherwise raises CustomValueError.
        """
        patient = await db_session.get(PatientUser, patient_id)
        if not patient:
            raise CustomValueError(
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
            )
        return True
