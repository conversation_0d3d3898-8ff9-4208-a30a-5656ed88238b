from enums.oral_examination_enum import OralExaminationMappingField, OralExaminationType
from schemas.requests.oral_examination_schema import FilterIntraoralExaminationRequest
from schemas.responses.oral_examination_schema import (
    OralExaminationData,
    OralExaminationResponse,
)
from sqlalchemy import func, select
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.tenant_models import OralExamination


class OralExaminationService:
    def __init__(self, session: AsyncSession):
        self.session = session

    def get_condition_for_filter(self, filter_data: FilterIntraoralExaminationRequest):
        fields = [
            ("oral_examination_id", OralExamination.id),
            ("patient_user_id", OralExamination.patient_user_id),
            ("medical_history_id", OralExamination.medical_history_id),
        ]

        conditions = [
            model_field == getattr(filter_data, attr)
            for attr, model_field in fields
            if getattr(filter_data, attr)
        ]

        if filter_data.examination_date:
            conditions.append(
                func.date(OralExamination.examination_date)
                == filter_data.examination_date
            )

        return conditions

    @measure_time
    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="get_oral_examination",
    )
    async def get_oral_examination(
        self,
        oral_examination_type: OralExaminationType,
        filter_data: FilterIntraoralExaminationRequest,
    ) -> OralExaminationResponse:

        select_fields = {
            OralExamination.id,
            OralExamination.patient_user_id,
            OralExamination.medical_history_id,
            OralExamination.examination_date,
            OralExamination.note,
            OralExamination.memo_path,
            OralExamination.tooth_type,
            OralExaminationMappingField.get_mapping(oral_examination_type),
        }

        conditions = self.get_condition_for_filter(filter_data)

        async with self.session:
            stmt = (
                select(*select_fields)
                .where(*conditions)
                .order_by(OralExamination.id.asc())
            )

            result = await self.session.execute(stmt)
            oral_examinations = result.mappings().all()

            return OralExaminationResponse(
                results=[
                    OralExaminationData(
                        **oral_examination,
                        data=oral_examination.get(
                            OralExaminationMappingField.get_mapping(
                                oral_examination_type
                            )
                        ),
                    )
                    for oral_examination in oral_examinations
                ]
            )
