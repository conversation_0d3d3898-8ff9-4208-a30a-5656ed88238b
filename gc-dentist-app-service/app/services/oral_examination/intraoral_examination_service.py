from core.messages import CustomMessageCode
from schemas.requests.oral_examination_schema import (
    CreateIntraoralExaminationRequest,
    UpdateIntraoralExaminationRequest,
)
from schemas.responses.oral_examination_schema import IntraoralExaminationResponse
from sqlalchemy import select
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import MedicalHistory, OralExamination


class IntraoralExaminationService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_intraoral_examination",
    )
    async def check_medical_history_exists(
        self, medical_history_id: int, patient_user_id: int, db_session: AsyncSession
    ):
        """
        Check if a medical history record exists.
        :param medical_history_id: ID of the medical history to check.
        :return: True if the medical history exists, False otherwise.
        """
        medical_history = await db_session.execute(
            select(MedicalHistory.id).where(
                MedicalHistory.id == medical_history_id,
                MedicalHistory.patient_user_id == patient_user_id,
            )
        )
        medical_history = medical_history.scalar_one_or_none()
        if not medical_history:
            raise CustomValueError(
                message=CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.title,
                message_code=CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.code,
            )
        return True

    @measure_time
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_intraoral_examination",
    )
    async def create_intraoral_examination(
        self, data: CreateIntraoralExaminationRequest
    ) -> IntraoralExaminationResponse:
        """
        Create a new intraoral examination record.
        :param data: Data for the intraoral examination.
        :return: Response containing the ID of the created oral examination.
        """
        async with self.session.begin():
            # Validate medical history and patient user
            await self.check_medical_history_exists(
                medical_history_id=data.medical_history_id,
                patient_user_id=data.patient_user_id,
                db_session=self.session,
            )

            # Create the oral examination record
            oral_examination = OralExamination(
                **data.model_dump(exclude={"intraoral_examination"}),
                intraoral_examination=data.intraoral_examination.model_dump(
                    exclude_none=True, mode="json"
                ),
            )
            self.session.add(oral_examination)
            await self.session.flush()
            await self.session.refresh(oral_examination)

            return IntraoralExaminationResponse(oral_examination_id=oral_examination.id)

    @measure_time
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_intraoral_examination",
    )
    async def update_intraoral_examination(
        self, oral_examination_id: int, data: UpdateIntraoralExaminationRequest
    ) -> IntraoralExaminationResponse:
        """
        Update an existing intraoral examination record.
        :param oral_examination_id: ID of the oral examination to update.
        :param data: Updated data for the intraoral examination.
        :return: Response containing the ID of the updated oral examination.
        """
        async with self.session.begin():
            # Fetch the existing oral examination record
            oral_examination = await self.session.get(
                OralExamination, oral_examination_id
            )
            if not oral_examination:
                raise CustomValueError(
                    message=CustomMessageCode.ORAL_EXAMINATION_NOT_FOUND.title,
                    message_code=CustomMessageCode.ORAL_EXAMINATION_NOT_FOUND.code,
                )

            # Update the oral examination record
            for key, value in data.model_dump(
                exclude_none=True, exclude={"intraoral_examination"}
            ).items():
                setattr(oral_examination, key, value)

            oral_examination.intraoral_examination = (
                data.intraoral_examination.model_dump(exclude_none=True, mode="json")
            )

            await self.session.flush()
            await self.session.refresh(oral_examination)

            return IntraoralExaminationResponse(oral_examination_id=oral_examination.id)
