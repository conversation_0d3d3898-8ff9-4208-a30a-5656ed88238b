from configuration.settings import configuration
from enums.clinic_enum import RoleEnum
from schemas.requests.clinic_schema import ClinicInfoSchema
from sqlalchemy import select
from sqlalchemy.exc import <PERSON><PERSON><PERSON>rror, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.tenant_models import (
    ClinicInformation,
    ClinicSourceMapping,
    DoctorProfile,
    DoctorUser,
)


class ClinicService:
    def __init__(self, session: AsyncSession = None):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="init_admin_clinic",
    )
    async def init_admin_clinic(self, data: ClinicInfoSchema, tenant_uuid: str) -> int:
        """
        Create a new clinic account record in the database.
        :param data: Data for creating a clinic.
        :return: The id created clinic object.
        """
        clinic_ifo = ClinicInformation(**data.model_dump(exclude={"manager_info"}))
        admin_user = DoctorUser(
            username=data.manager_info.username,
            required_change_password=data.manager_info.required_change_password,
            role_id=RoleEnum.ADMIN.value,
        )
        admin_user.set_password(data.manager_info.password, tenant_uuid)

        async with self.session.begin():
            # Add clinic information
            self.session.add(clinic_ifo)
            await self.session.flush()
            await self.session.refresh(clinic_ifo)

            # Add admin user
            self.session.add(admin_user)
            await self.session.flush()
            await self.session.refresh(admin_user)

            admin_profile = DoctorProfile(
                doctor_user_id=admin_user.id,
                **data.manager_info.model_dump(
                    exclude={
                        "username",
                        "password",
                        "required_change_password",
                        "is_active",
                    },
                    mode="json",
                ),
            )

            # Encrypt sensitive fields
            aes = AesGCMRotation(configuration)
            admin_profile.phone_hash = aes.sha256_hash(admin_profile.phone)
            admin_profile.email_hash = aes.sha256_hash(admin_profile.email)
            admin_profile.date_of_birth_hash = aes.sha256_hash(
                admin_profile.date_of_birth
            )
            admin_profile.phone = aes.encrypt_data(admin_profile.phone)
            admin_profile.email = aes.encrypt_data(admin_profile.email)
            admin_profile.date_of_birth = aes.encrypt_data(admin_profile.date_of_birth)

            self.session.add(admin_profile)
            await self.session.flush()
            await self.session.refresh(admin_profile)

        return admin_user.id

    async def get_clinic_source_mapping(self, source: str):
        stmt = select(ClinicSourceMapping).where(ClinicSourceMapping.source == source)

        async with self.session:
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
