from enum import IntEnum, StrEnum


class FormFlowType(IntEnum):
    INFORMATION = 1
    SURVEY = 3


class FormFlowVersion(StrEnum):
    V1_0 = "1.0"


class FormItemFieldType(StrEnum):
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    DATETIME = "datetime"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    SELECT = "select"
    RATING = "rating"
    TABLE = "table"


class FormItemSide(StrEnum):
    DOCTOR = "doctor"
    PATIENT = "patient"


class EditActionEnum(StrEnum):
    UPDATE = "UPDATE"
    CREATE = "CREATE"
