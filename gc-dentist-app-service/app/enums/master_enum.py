from enums.base import StrEnum

from gc_dentist_shared.tenant_models import (
    MasterEra,
    MasterOralExamination,
    MasterOralExaminationMatrix,
    MasterPostalCode,
    MasterPrefecture,
)


class MasterModelEnum(StrEnum):
    """Enumeration for master model names."""

    M_ERA = "m_era"
    M_ORAL_EXAMINATIONS = "m_oral_examinations"
    M_ORAL_EXAMINATIONS_MATRIX = "m_oral_examinations_matrix"
    M_PREFECTURE = "m_prefecture"
    M_POSTAL_CODE = "m_postal_code"


class MappingModel:
    M_ERA = MasterEra
    M_ORAL_EXAMINATIONS = MasterOralExamination
    M_ORAL_EXAMINATIONS_MATRIX = MasterOralExaminationMatrix
    M_PREFECTURE = MasterPrefecture
    M_POSTAL_CODE = MasterPostalCode

    def get_model(self, model_name: MasterModelEnum):
        """Get the model class based on the model name."""
        return getattr(self, model_name.value.upper(), None)

    def get_model_fields(self, model_name: MasterModelEnum):
        """Get the model class based on the model name."""
        model = self.get_model(model_name)
        if model:
            return model.__table__.c
        return None

    def get_pk_column_model(self, model_name: MasterModelEnum):
        """Get the primary key column of the model."""
        model = self.get_model(model_name)
        if model:
            return next(iter(model.__table__.primary_key.columns.values()), None)
        return None
