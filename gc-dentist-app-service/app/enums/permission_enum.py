#!/usr/bin/env python3
"""
Permission Key Enums for Admin operations.
Based on the permissions.yaml structure and API usage.
"""

from enums.base import StrEnum


class PermissionRedisKey(StrEnum):
    """Redis cache keys for permission system"""

    PERMISSION_CACHE_PREFIX = "noda_data:user_role_permission"


class AdminPermissionKeyEnum(StrEnum):
    """Admin permission keys for various operations."""

    # S3 Operations
    S3_VIEW = "S3:VIEW"
    S3_CREATE = "S3:CREATE"
    S3_UPDATE = "S3:UPDATE"

    # Patient Management
    PATIENT_VIEW = "Patient:VIEW"
    PATIENT_CREATE = "Patient:CREATE"
    PATIENT_UPDATE = "Patient:UPDATE"
    PATIENT_DELETE = "Patient:DELETE"

    # Patient Waiting Management
    WAITING_PATIENT_VIEW = "WaitingPatient:VIEW"
    WAITING_PATIENT_CREATE = "WaitingPatient:CREATE"
    WAITING_PATIENT_UPDATE = "WaitingPatient:UPDATE"
    WAITING_PATIENT_DELETE = "WaitingPatient:DELETE"

    # Form Management
    FORM_VIEW = "FORM:VIEW"
    FORM_CREATE = "FORM:CREATE"
    FORM_UPDATE = "FORM:UPDATE"
    FORM_DELETE = "FORM:DELETE"

    # Oral Examinations Management
    ORAL_EXAMINATIONS_VIEW = "OralExamination:VIEW"
    ORAL_EXAMINATIONS_CREATE = "OralExamination:CREATE"
    ORAL_EXAMINATIONS_UPDATE = "OralExamination:UPDATE"
    ORAL_EXAMINATIONS_DELETE = "OralExamination:DELETE"

    # Reservation Management
    RESERVATION_VIEW = "Reservation:VIEW"
    RESERVATION_CREATE = "Reservation:CREATE"
    RESERVATION_UPDATE = "Reservation:UPDATE"
    RESERVATION_DELETE = "Reservation:DELETE"

    # Document Management
    DOCUMENT_VIEW = "Document:VIEW"
    DOCUMENT_CREATE = "Document:CREATE"
    DOCUMENT_UPDATE = "Document:UPDATE"
    DOCUMENT_DELETE = "Document:DELETE"
