from enums.base import IntEnum, StrEnum

from gc_dentist_shared.tenant_models import OralExamination


class ToothType(StrEnum):
    ADULT = "adult"
    CHILD = "child"


class ToothTypeInt(IntEnum):
    CHILD = 1
    ADULT = 2


class PositionEnum(StrEnum):
    POSITION1 = "1"
    POSITION2 = "2"
    POSITION3 = "3"
    POSITION4 = "4"
    POSITION5 = "5"
    POSITION6 = "6"


class TeethNameEnum(StrEnum):
    UPPER_RIGHT_1 = "upper_right_1"
    UPPER_RIGHT_2 = "upper_right_2"
    UPPER_RIGHT_3 = "upper_right_3"
    UPPER_RIGHT_4 = "upper_right_4"
    UPPER_RIGHT_5 = "upper_right_5"
    UPPER_RIGHT_6 = "upper_right_6"
    UPPER_RIGHT_7 = "upper_right_7"
    UPPER_RIGHT_8 = "upper_right_8"

    UPPER_LEFT_1 = "upper_left_1"
    UPPER_LEFT_2 = "upper_left_2"
    UPPER_LEFT_3 = "upper_left_3"
    UPPER_LEFT_4 = "upper_left_4"
    UPPER_LEFT_5 = "upper_left_5"
    UPPER_LEFT_6 = "upper_left_6"
    UPPER_LEFT_7 = "upper_left_7"
    UPPER_LEFT_8 = "upper_left_8"

    LOWER_LEFT_1 = "lower_left_1"
    LOWER_LEFT_2 = "lower_left_2"
    LOWER_LEFT_3 = "lower_left_3"
    LOWER_LEFT_4 = "lower_left_4"
    LOWER_LEFT_5 = "lower_left_5"
    LOWER_LEFT_6 = "lower_left_6"
    LOWER_LEFT_7 = "lower_left_7"
    LOWER_LEFT_8 = "lower_left_8"

    LOWER_RIGHT_1 = "lower_right_1"
    LOWER_RIGHT_2 = "lower_right_2"
    LOWER_RIGHT_3 = "lower_right_3"
    LOWER_RIGHT_4 = "lower_right_4"
    LOWER_RIGHT_5 = "lower_right_5"
    LOWER_RIGHT_6 = "lower_right_6"
    LOWER_RIGHT_7 = "lower_right_7"
    LOWER_RIGHT_8 = "lower_right_8"


class OralExaminationType(StrEnum):
    INTRAORAL_EXAMINATION = "intraoral_examination"
    PERIODONTAL_1POINT = "periodontal_1point"
    PERIODONTAL_4POINT = "periodontal_4point"
    PERIODONTAL_6POINT = "periodontal_6point"
    PCR = "pcr"
    PCR_6POINT = "pcr_6point"


class OralExaminationMappingField:
    INTRAORAL_EXAMINATION = OralExamination.intraoral_examination
    PERIODONTAL_1POINT = OralExamination.periodontal_1point
    PERIODONTAL_4POINT = OralExamination.periodontal_4point
    PERIODONTAL_6POINT = OralExamination.periodontal_6point
    PCR = OralExamination.pcr
    PCR_6POINT = OralExamination.pcr_6point

    @classmethod
    def get_mapping(cls, oral_examination_type: OralExaminationType):
        return getattr(cls, oral_examination_type.value.upper(), None)
