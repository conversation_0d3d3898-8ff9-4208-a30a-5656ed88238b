import json
import secrets
import string

from core.constants import <PERSON><PERSON>Y_LENGTH
from core.messages import CustomMessageCode
from enums.redis_enum import RedisR<PERSON>Enum
from fastapi import status

from gc_dentist_shared.core.common.redis import Redis<PERSON><PERSON>
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


class RedisManager:
    """OTP Manager backed by Redis"""

    def __init__(self, configuration):
        self.redis_client = RedisCli(configuration=configuration)

    def generate_value(self, key_length: int = KEY_LENGTH) -> str:
        return "".join(secrets.choice(string.digits) for _ in range(key_length))

    def _get_key(
        self, prefix: str, user_id: int, role: RedisRoleEnum, key_name: str
    ) -> str:
        """Create Redis key"""
        return f"{key_name}:{prefix}_{role}:{user_id}"

    async def create_data(
        self,
        user_id: int,
        role: RedisRoleEnum,
        prefix: str,
        data: dict | None,
        expiry_minutes: int,
        key_length: int,
        key_name: str,
    ) -> str:

        if not data:
            data = {}

        value = self.generate_value(key_length)
        data = {
            **data,
            key_name: value,
        }

        redis_key = self._get_key(prefix, user_id, role, key_name)
        result = await self.redis_client.setex(
            redis_key,
            expiry_minutes * 60,
            json.dumps(data),
        )

        if not result:
            raise CustomValueError(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=CustomMessageCode.REDIS_KEY_CREATION_FAILED.title,
                message_code=CustomMessageCode.REDIS_KEY_CREATION_FAILED.code,
            )

        return value

    async def get_data(
        self, prefix: str, user_id: int, role: RedisRoleEnum, key_name: str
    ) -> dict | None:
        redis_key = self._get_key(prefix, user_id, role, key_name)
        data = await self.redis_client.get(redis_key)
        if not data:
            return None
        return json.loads(data)

    async def verify_data(
        self,
        input_key: str,
        prefix: str,
        user_id: int,
        role: RedisRoleEnum,
        key_name: str,
    ) -> dict | None:
        data = await self.get_data(prefix, user_id, role, key_name)
        if not data:
            return None

        if data.get("key") != input_key:
            return None

        return data

    async def delete_data(
        self, prefix: str, user_id: int, role: RedisRoleEnum, key_name: str
    ) -> None:
        redis_key = self._get_key(prefix, user_id, role, key_name)
        await self.redis_client.delete(redis_key)
