
# Sync db table t_role_permission, m_permission from yaml file:
```bash
PYTHONPATH=$PYTHONPATH:$(pwd) python opa/run_setup.py
```

# OPA Policy Repository

## Defined Constants
- **POLICY_DIRECTORY:** `policies/` — Directory containing OPA Rego policy files.
- **BUNDLE_DIRECTORY:** `bundles/` — Directory for storing bundled policy files.
- **BUNDLE_FILENAME:** `bundle.tar.gz` — Name of the compressed policy bundle.
- **CONFIG_FILENAME:** `opa_config.yaml` — OPA configuration file used to manage settings.

## Directory Structure
```plaintext
policies/
├── example_policy.rego
bundles/
└── bundle.tar.gz
opa_config.yaml
```

## Bundle Management

### About Bundles
Bundles allow distribution and management of OPA policies.

Documentation Reference:
[OPA Bundle Documentation](https://www.openpolicyagent.org/docs/latest/management-bundles/)

### Building OPA Bundle
To generate the bundled policy file (`bundle.tar.gz`), use:

```bash
# BUILD_COMMAND
opa build ${POLICY_DIRECTORY} --output ${BUNDLE_DIRECTORY}/${BUNDLE_FILENAME}
```

Example: opa build policies --output ./bundles/bundle.tar.gz



## Deployment & Server Operations

### Running OPA Server
To launch OPA in server mode and serve policies:

```bash
# SERVER_COMMAND
opa run --server ${POLICY_DIRECTORY}
```
