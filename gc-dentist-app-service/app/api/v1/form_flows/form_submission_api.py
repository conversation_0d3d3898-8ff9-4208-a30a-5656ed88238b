import uuid
from typing import Annotated

from core.common.api_response import <PERSON><PERSON><PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends
from fastapi_pagination import Page
from schemas.requests.form_submission import FormSubmission<PERSON><PERSON>, FormSubmissionUpdate
from schemas.responses.form_submission import (
    FormSubmissionDetailSchema,
    FormSubmissionListSchema,
)
from services.form_flows.form_submission_services import FormSubmissionService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("")
@version(1, 0)
@measure_time
async def create_form_submission(
    obj_requests: FormSubmissionCreate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        submission_service = FormSubmissionService(db_session)
        submission_uuid = await submission_service.create_form_submission(obj_requests)
        return ApiResponse.success(
            data={"submission_uuid": submission_uuid},
            message=CustomMessageCode.FORM_SUBMISSION_CREATE_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_form_submission CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error create_form_submission: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.FORM_SUBMISSION_ERROR_CREATE_FAILED.title,
            message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_CREATE_FAILED.code,
        )


@router.get("/{submission_uuid}", response_model=FormSubmissionDetailSchema)
@version(1, 0)
@measure_time
async def get_detail_form_submission(
    submission_uuid: uuid.UUID,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        submission_service = FormSubmissionService(db_session)
        submission = await submission_service.get_detail_form_submission(
            str(submission_uuid)
        )
        return ApiResponse.success(
            data=submission,
            message=CustomMessageCode.FORM_SUBMISSION_GET_DETAIL_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(f"❌ Error get_detail_form_submission: {e.status_code} - {e.message}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error get_detail_form_submission: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.FORM_SUBMISSION_ERROR_GET_DETAIL_FAILED.title,
            message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_GET_DETAIL_FAILED.code,
        )


@router.get("", response_model=Page[FormSubmissionListSchema])
@version(1, 0)
async def list_form_flow_submission(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        submission_service = FormSubmissionService(db_session)
        paginated_result = await submission_service.list_form_flow_submission()
        return ApiResponse.success(data=paginated_result.model_dump(mode="json"))
    except Exception as e:
        log.error(f"Error in list_form_flow_submission: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.FORM_SUBMISSION_ERROR_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_GET_LIST_FAILED.code,
        )


@router.put("/{submission_uuid}")
@version(1, 0)
@measure_time
async def edit_form_submission(
    submission_uuid: uuid.UUID,
    obj_requests: FormSubmissionUpdate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        submission_service = FormSubmissionService(db_session)
        await submission_service.edit_form_submission(
            str(submission_uuid), obj_requests
        )

        return ApiResponse.success(
            data={"submission_uuid": str(submission_uuid)},
            message=CustomMessageCode.FORM_SUBMISSION_UPDATE_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(f"❌ Error edit_form_submission: {e.status_code} - {e.message}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error edit_form_submission: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.FORM_SUBMISSION_ERROR_UPDATE_FAILED.title,
            message_code=CustomMessageCode.FORM_SUBMISSION_ERROR_UPDATE_FAILED.code,
        )
