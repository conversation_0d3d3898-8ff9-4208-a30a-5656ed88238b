import uuid
from typing import Annotated

from core.common.api_response import <PERSON>pi<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends
from fastapi_pagination import Page
from schemas.requests.form_flow_requests import Form<PERSON><PERSON><PERSON><PERSON>, Form<PERSON>lowUpdate
from schemas.responses.form_flow_responses import ListFormFlowSchema
from services.form_flows.form_flow_services import FormFlowService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("")
@version(1, 0)
@measure_time
async def create_form_flows(
    obj_requests: FormFlowsCreate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        form_flow_service = FormFlowService(db_session)
        form_flow_uuid = await form_flow_service.create_form_flows(obj_requests)
        return ApiResponse.success(
            data={"form_flow_uuid": form_flow_uuid},
            message=CustomMessageCode.FORM_FLOW_CREATE_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_form_flows CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Create form flow Error: {}".format(str(e)))
        return ApiResponse.error(
            message_code=CustomMessageCode.FORM_FLOW_ERROR_CREATE_FAILED.code,
            message=CustomMessageCode.FORM_FLOW_ERROR_CREATE_FAILED.title,
        )


@router.get("/{form_flow_uuid}")
@version(1, 0)
@measure_time
async def get_detail_form_flow(
    form_flow_uuid: uuid.UUID,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        form_service = FormFlowService(db_session)
        detail = await form_service.get_detail_form_flow(str(form_flow_uuid))
        return ApiResponse.success(
            data=detail,
            message=CustomMessageCode.FORM_FLOW_GET_DETAIL_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_detail_form_flow CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Get form flow detail Error: {str(e)}")
        return ApiResponse.error(
            message_code=CustomMessageCode.FORM_FLOW_ERROR_DETAIL_FAILED.code,
            message=CustomMessageCode.FORM_FLOW_ERROR_DETAIL_FAILED.title,
        )


@router.get("", response_model=Page[ListFormFlowSchema])
@version(1, 0)
@measure_time
async def get_list_form_flow(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        form_service = FormFlowService(db_session)
        paginated_result = await form_service.list_form_flow()
        return ApiResponse.success(data=paginated_result.model_dump(mode="json"))
    except Exception as e:
        log.error(f"❌ Error in method get_list_form_flow: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.FORM_FLOW_ERROR_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.FORM_FLOW_ERROR_GET_LIST_FAILED.code,
        )


@router.put("/{form_flow_uuid}")
@version(1, 0)
@measure_time
async def edit_form_flow(
    form_flow_uuid: uuid.UUID,
    obj_requests: FormFlowUpdate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        form_flow_service = FormFlowService(db_session)
        form_flow_uuid = await form_flow_service.edit_form_flow(
            obj_requests, str(form_flow_uuid)
        )
        return ApiResponse.success(
            data={"form_flow_uuid": str(form_flow_uuid)},
            message=CustomMessageCode.FORM_FLOW_EDIT_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error edit_form_flow CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Edit edit_form_flow Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.FORM_FLOW_ERROR_EDIT_FAILED.title,
            message_code=CustomMessageCode.FORM_FLOW_ERROR_EDIT_FAILED.code,
        )


@router.delete("/{form_flow_uuid}")
@version(1, 0)
@measure_time
async def delete_form_flow(
    form_flow_uuid: uuid.UUID,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        form_flow_service = FormFlowService(db_session)
        await form_flow_service.delete_form_flow(str(form_flow_uuid))
        return ApiResponse.success(
            data={"form_flow_uuid": str(form_flow_uuid)},
            message=CustomMessageCode.FORM_FLOW_DELETE_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error delete_form_flow CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Edit delete_form_flow Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.FORM_FLOW_ERROR_DELETE_FAILED.title,
            message_code=CustomMessageCode.FORM_FLOW_ERROR_DELETE_FAILED.code,
        )
