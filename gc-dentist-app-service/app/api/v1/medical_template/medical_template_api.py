from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends  # type: ignore
from fastapi_pagination import Page
from schemas.requests.medical_template_requests import (
    CreateMedicalTemplatePayload,
    UpdateMedicalTemplatePayload,
)
from schemas.responses.medical_template_schema import ListMedicalTemplateGroupSchema
from services.medical_template_service import MedicalTemplateService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("", summary="Create Document Template")
@version(1, 0)
async def create_medical_template(
    payload: CreateMedicalTemplatePayload,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        medical_template_service = MedicalTemplateService(db_session)
        template_id = await medical_template_service.create_medical_template(payload)
        return ApiResponse.success(
            data={"template_id": template_id},
            message=CustomMessageCode.MEDICAL_TEMPLATE_CREATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_document_template CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f" ❌ Error create_document_template: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MEDICAL_TEMPLATE_CREATED_FAILED.title,
            message_code=CustomMessageCode.MEDICAL_TEMPLATE_CREATED_FAILED.code,
        )


@router.get("/{medical_template_id:int}", summary="Get Medical Template")
@version(1, 0)
async def get_medical_template(
    medical_template_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        medical_template_service = MedicalTemplateService(db_session)
        medical_template = await medical_template_service.get_medical_template(
            medical_template_id
        )
        return ApiResponse.success(data=medical_template.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(
            f"❌ Error get_medical_template CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error get_medical_template: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MEDICAL_TEMPLATE_GET_FAILED.title,
            message_code=CustomMessageCode.MEDICAL_TEMPLATE_GET_FAILED.code,
        )


@router.put("/{medical_template_id:int}", summary="Update Medical Template")
@version(1, 0)
async def update_medical_template(
    medical_template_id: int,
    payload: UpdateMedicalTemplatePayload,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        medical_template_service = MedicalTemplateService(db_session)
        medical_template = await medical_template_service.update_medical_template(
            medical_template_id, payload
        )
        return ApiResponse.success(
            data={"template_id": medical_template},
            message=CustomMessageCode.MEDICAL_TEMPLATE_UPDATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_medical_template CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error update_medical_template: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MEDICAL_TEMPLATE_UPDATED_FAILED.title,
            message_code=CustomMessageCode.MEDICAL_TEMPLATE_UPDATED_FAILED.code,
        )


@router.delete("/{medical_template_id:int}", summary="Deactivate Medical Template")
@version(1, 0)
async def deactivate_medical_template(
    medical_template_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        medical_template_service = MedicalTemplateService(db_session)
        medical_template = await medical_template_service.deactivate_medical_template(
            medical_template_id
        )
        return ApiResponse.success(
            data={"template_id": medical_template},
            message=CustomMessageCode.MEDICAL_TEMPLATE_DELETED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_medical_template CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error deactivate_medical_template: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MEDICAL_TEMPLATE_DELETED_FAILED.title,
            message_code=CustomMessageCode.MEDICAL_TEMPLATE_DELETED_FAILED.code,
        )


@router.get(
    "",
    response_model=Page[ListMedicalTemplateGroupSchema],
    summary="Get List Medical Template",
)
@version(1, 0)
async def get_list_medical_template(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    search: str | None = None,
):
    try:
        medical_template_service = MedicalTemplateService(db_session)
        medical_template = await medical_template_service.get_list_medical_template(
            search=search
        )
        return ApiResponse.success(
            data=medical_template.model_dump(mode="json"),
            message=CustomMessageCode.MEDICAL_TEMPLATE_GET_LIST_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_list_medical_template CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error get_list_medical_template: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MEDICAL_TEMPLATE_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.MEDICAL_TEMPLATE_GET_LIST_FAILED.code,
        )
