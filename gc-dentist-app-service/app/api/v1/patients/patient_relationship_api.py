from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends
from fastapi_pagination import Page
from schemas.requests.patient_relationship_schema import (
    CreateRelationshipRequest,
    OTPVerificationRequest,
)
from schemas.responses.patient_relationship_schema import PatientRelationshipListSchema
from services.patient_relationship_service import PatientRelationshipService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/requests")
@version(1, 0)
async def create_patient_relationship_request(
    request: CreateRelationshipRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Create patient relationship request"""
    try:
        service = PatientRelationshipService(db_session)
        result = await service.create_relationship_request(request)
        return ApiResponse.success(
            data={"request_id": result},
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_CREATION_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_patient_relationship_request CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error create_patient_relationship_request: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_CREATION_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_CREATION_FAILED.code,
        )


@router.post("/approve/{request_id}")
@version(1, 0)
async def approve_patient_relationship_request(
    request_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Approve patient relationship request"""
    try:
        service = PatientRelationshipService(db_session, configuration)
        result = await service.approve_patient_relationship_request(request_id)
        return ApiResponse.success(
            data={"request_id": result},
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_APPROVAL_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error approve_patient_relationship_request CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error approve_patient_relationship_request: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_APPROVAL_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_APPROVAL_FAILED.code,
        )


@router.post("/reject/{request_id}")
@version(1, 0)
async def reject_patient_relationship_request(
    request_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Reject patient relationship request"""
    try:
        service = PatientRelationshipService(db_session, configuration)
        result = await service.reject_patient_relationship_request(request_id)
        return ApiResponse.success(
            data={"request_id": result},
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_REJECT_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error reject_patient_relationship_request CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error reject_patient_relationship_request: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_REJECT_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_REJECT_FAILED.code,
        )


@router.post("/verify")
@version(1, 0)
async def verify_patient_relationship_request(
    request: OTPVerificationRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Verify patient relationship request"""
    try:
        service = PatientRelationshipService(db_session, configuration)
        result = await service.verify_patient_relationship_request(request)
        return ApiResponse.success(
            data={"request_id": result},
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error verify_patient_relationship_request CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error verify_patient_relationship_request: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED.code,
        )


@router.post("/release/{relationship_id}")
@version(1, 0)
async def relase_patient_relationship(
    relationship_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Release patient relationship"""
    try:
        service = PatientRelationshipService(db_session)
        result = await service.release_patient_relationship(relationship_id)
        return ApiResponse.success(
            data={"relationship_id": result},
            message=CustomMessageCode.PATIENT_RELATIONSHIP_RELEASE_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error release_patient_relationship CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error release_patient_relationship: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_RELEASE_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_RELEASE_FAILED.code,
        )


@router.get("/{patient_user_id:int}")
@version(1, 0)
async def get_patient_relationship(
    patient_user_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Get patient relationship summary with main and sub patients"""
    try:
        service = PatientRelationshipService(db_session)
        result = await service.get_patient_relationship(patient_user_id)
        return ApiResponse.success(
            data=result.model_dump(),
            message=CustomMessageCode.PATIENT_RELATIONSHIP_SUMMARY_GET_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(f"❌ Error get_patient_relationship_summary: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error get_patient_relationship_summary: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_SUMMARY_GET_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_SUMMARY_GET_FAILED.code,
        )


@router.get("", response_model=Page[PatientRelationshipListSchema])
@version(1, 0)
async def get_patient_relationship_list(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    search: str | None = None,
):
    """Get list of patients with relationships and pagination"""
    try:
        service = PatientRelationshipService(db_session)
        paginated_result = await service.get_patient_relationship_list(search=search)
        return ApiResponse.success(data=paginated_result.model_dump(mode="json"))
    except Exception as e:
        log.error(f"❌ Error get_patient_relationship_list: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_RELATIONSHIP_LIST_GET_FAILED.title,
            message_code=CustomMessageCode.PATIENT_RELATIONSHIP_LIST_GET_FAILED.code,
        )
