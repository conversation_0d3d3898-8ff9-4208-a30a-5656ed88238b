from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends
from schemas.requests.patient_waiting_schema import (
    FilterPatientWaitingSchema,
    PatientWaitingCreate,
    PatientWaitingUpdate,
)
from schemas.responses.patient_waiting_schema import ListPatientWaitingResponseSchema
from services.patient_waiting_service import PatientWaitingService
from sqlalchemy.ext.asyncio import AsyncSession

# from fastapi_versioning import version
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get("", response_model=ListPatientWaitingResponseSchema)
@version(1, 0)
async def get_list_patient_waiting(
    filters: Annotated[FilterPatientWaitingSchema, Depends()],
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        patient_service = PatientWaitingService(db_session)
        result = await patient_service.get_list_waiting_grouped(filters)

        return ApiResponse.success(data=result)
    except Exception as e:
        log.error(f"❌ Error in method get_patient_waiting_list: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_WAITING_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.PATIENT_WAITING_GET_LIST_FAILED.code,
        )


@router.post("")
@version(1, 0)
async def create_patient_waiting(
    obj: PatientWaitingCreate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = PatientWaitingService(db_session)
        data_valid = await service.validate_patient_waiting_data(obj)
        patient_waiting = await service.create_patient_waiting(data_valid)

        return ApiResponse.success(
            message=CustomMessageCode.PATIENT_WAITING_CREATED_SUCCESS.title,
            data=patient_waiting.model_dump(exclude_none=True),
        )
    except CustomValueError as e:
        log.error(f"Error creating patient waiting: {e}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"Unexpected error creating patient waiting: {e}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )


@router.put("/{id}")
@version(1, 0)
async def update_patient_waiting(
    id: int,
    obj: PatientWaitingUpdate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = PatientWaitingService(db_session)
        patient_waiting = await service.update_patient_waiting(id, obj)

        return ApiResponse.success(
            data=patient_waiting.model_dump(exclude_none=True),
        )
    except CustomValueError as e:
        log.error(f"❌ Error updating patient waiting: {e}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Unexpected error updating patient waiting: {e}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
