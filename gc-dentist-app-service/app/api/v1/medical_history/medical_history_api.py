from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Query
from schemas.requests.medical_history_schema import (
    MedicalHistoryFilter,
    MedicalHistoryUpdate,
)
from services.medical_history_service import MedicalHistoryService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.put("/{medical_history_id}")
@version(1, 0)
async def update_medical_history(
    medical_history_id: int,
    obj: MedicalHistoryUpdate,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = MedicalHistoryService(db_session)
        updated_history = await service.update_medical_history(medical_history_id, obj)

        return ApiResponse.success(
            data=updated_history.model_dump(exclude_none=True),
        )
    except CustomValueError as e:
        log.error(f"❌ Error updating medical history: {e}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error updating medical history: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )


@router.get(path="", dependencies=[])
@version(1, 0)
async def get_medical_history(
    obj: Annotated[
        MedicalHistoryFilter, Query(..., description="Filter for medical history")
    ],
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = MedicalHistoryService(db_session)
        medical_history = await service.get_medical_history(obj)
        return ApiResponse.success(
            data=medical_history.model_dump(),
        )
    except CustomValueError as e:
        log.error(f"❌ Error fetching medical history: {e}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error fetching medical history: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
