from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from enums.oral_examination_enum import OralExaminationType
from fastapi import APIRouter, Depends
from schemas.requests.oral_examination_schema import FilterIntraoralExaminationRequest
from services.oral_examination.oral_examination_service import OralExaminationService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get(path="/{oral_examination_type}")
@version(1, 0)
async def get_oral_examination(
    oral_examination_type: OralExaminationType,
    obj: Annotated[
        FilterIntraoralExaminationRequest, Depends(FilterIntraoralExaminationRequest)
    ],
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = OralExaminationService(session=db_session)
        response = await service.get_oral_examination(
            oral_examination_type=oral_examination_type, filter_data=obj
        )
        return ApiResponse.success(
            data=response.model_dump(mode="json").get("results", [])
        )

    except CustomValueError as e:
        log.error(f"❌ CustomValueError oral-examination: {e.message}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Exception oral-examination: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
