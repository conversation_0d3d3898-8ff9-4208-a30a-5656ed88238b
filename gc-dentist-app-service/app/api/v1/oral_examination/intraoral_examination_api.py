from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends
from schemas.requests.oral_examination_schema import (
    CreateIntraoralExaminationRequest,
    UpdateIntraoralExaminationRequest,
)
from services.oral_examination.intraoral_examination_service import (
    IntraoralExaminationService,
)
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(path="")
@version(1, 0)
async def create_intraoral_examination(
    data: CreateIntraoralExaminationRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = IntraoralExaminationService(session=db_session)
        result = await service.create_intraoral_examination(data=data)
        return ApiResponse.success(data=result.model_dump())

    except CustomValueError as e:
        log.error(f"❌ CustomValueError intraoral-examination: {e.message}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Exception intraoral-examination: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )


@router.put(path="/{examination_id}")
@version(1, 0)
async def update_intraoral_examination(
    examination_id: int,
    data: UpdateIntraoralExaminationRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = IntraoralExaminationService(session=db_session)
        result = await service.update_intraoral_examination(
            oral_examination_id=examination_id,
            data=data,
        )
        return ApiResponse.success(data=result.model_dump())

    except CustomValueError as e:
        log.error(f"❌ CustomValueError intraoral-examination: {e.message}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Exception intraoral-examination: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
