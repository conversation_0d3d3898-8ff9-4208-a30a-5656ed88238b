from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request
from schemas.requests.clinic_schema import ClinicInfoSchema
from services.clinic_service import ClinicService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.hmac import depends_hmac_authentication
from gc_dentist_shared.core.constants import X_TENANT_UUID
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    "/admin",
    dependencies=[Depends(depends_hmac_authentication(configuration=configuration))],
)
@version(1, 0)
async def create_profile(
    data: ClinicInfoSchema,
    request: Request,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        user_service = ClinicService(db_session)
        tenant_uuid = request.headers.get(X_TENANT_UUID)
        id = await user_service.init_admin_clinic(data, tenant_uuid)

        return ApiResponse.success(
            data={"doctor_user_id": id},
            message=CustomMessageCode.CLINIC_CREATED_SUCCESS.title,
        )
    except Exception as e:
        log.error("❌ Create Profile Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.CLINIC_CREATED_FAILED.title,
            data={"detail": CustomMessageCode.CLINIC_CREATED_FAILED.description},
        )
