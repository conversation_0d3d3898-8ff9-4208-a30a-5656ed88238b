from typing import Annotated

from core.common.api_response import Api<PERSON><PERSON>ponse
from core.constants import X_TENANT_SLUG, X_TENANT_UUID
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request, status
from schemas.requests.auth_schema import LoginRequestSchema
from services.auth_service import AuthServices
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import (
    ExceptionNotFoundTenantError,
)
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/login")
@version(1, 0)
async def login(request: Request, obj: LoginRequestSchema):
    tenant_slug = request.headers.get(X_TENANT_SLUG)
    tenant_slug = tenant_slug.strip() if tenant_slug else None
    if not tenant_slug:
        return ApiResponse.error(
            message=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
            message_code=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
        )

    try:
        auth_service = AuthServices()
        response = await auth_service.login(tenant_slug, obj)
        return ApiResponse.success(
            data=response.model_dump(),
            message=CustomMessageCode.LOGIN_SUCCESS.title,
        )
    except ExceptionNotFoundTenantError:
        return ApiResponse.error(
            message=CustomMessageCode.TENANT_NOT_FOUND.title,
            message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
        )
    except ValueError as e:
        log.error(f"Login failed for tenant '{tenant_slug}': {str(e)}")
        return ApiResponse.error(
            message=str(e),
            status_code=status.HTTP_401_UNAUTHORIZED,
        )
    except Exception as e:
        log.error(f"Login failed for tenant '{tenant_slug}': {str(e)}")
        return ApiResponse.error(
            message=str(e),
            status_code=status.HTTP_401_UNAUTHORIZED,
        )


@router.post("/me")
@version(1, 0)
async def me(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        clinic_uuid = request.headers.get(X_TENANT_UUID)

        # TODO Get user id from request context or headers
        user_id = request.headers.get("authorization").replace("Bearer ", "").strip()
        user_id = int(user_id) if user_id.isdigit() else None

        auth_service = AuthServices()
        response = await auth_service.get_doctor_profile(
            db_session=db_session,
            clinic_uuid=clinic_uuid,
            user_id=user_id,
        )
        return ApiResponse.success(data=response.model_dump(mode="json"))
    except Exception as e:
        log.error(f"Error in me endpoint: {str(e)}")
        return ApiResponse.error(message=str(e))
