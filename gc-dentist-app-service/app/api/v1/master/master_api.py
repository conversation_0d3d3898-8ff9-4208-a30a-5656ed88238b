from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from enums.master_enum import MasterModelEnum
from fastapi import APIRouter, Depends, Query
from schemas.requests.master_schema import MasterRequestData
from services.master_data_service import MasterDataService
from sqlalchemy.ext.asyncio import AsyncSession

# from fastapi_versioning import version
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get("")
@version(1, 0)
async def get_master_data(
    obj: Annotated[
        MasterRequestData, Query(..., description="List of master model name")
    ],
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = MasterDataService(session=db_session)
        response = await service.get_master_data(master_names=obj)
        return ApiResponse.success(
            data=response.model_dump(mode="json").get("data", [])
        )
    except Exception as e:
        log.error(f"❌ Error fetching master data: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MASTER_DATA_NOT_FOUND.title,
            message_code=CustomMessageCode.MASTER_DATA_NOT_FOUND.code,
        )


@router.get("/items")
@version(1, 0)
async def get_master_data_by_model_name(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    master_name: Annotated[
        MasterModelEnum, Query(..., description="Master model name")
    ],
    items: Annotated[
        list, Query(..., description="Comma-separated list of items to filter")
    ],
    filter: Annotated[str, Query(..., description="Filter condition for the items")],
):
    try:
        service = MasterDataService(session=db_session)
        data_valid = service.validate_master_data(
            master_name=master_name, items=items, filter=filter
        )
        query = service.build_data_query(obj=data_valid)

        response = await service.get_master_data_items(query=query)
        return ApiResponse.success(
            data=response.model_dump(mode="json").get("data", [])
        )

    except ValueError as ve:
        log.error(
            f"❌ Validation error for master data by model name '{master_name}': {str(ve)}"
        )
        return ApiResponse.error(
            message=str(ve),
        )
    except Exception as e:
        log.error(
            f"❌ Error fetching master data by model name '{master_name}': {str(e)}"
        )
        return ApiResponse.error(
            message=CustomMessageCode.MASTER_DATA_NOT_FOUND.title,
            message_code=CustomMessageCode.MASTER_DATA_NOT_FOUND.code,
        )
