from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from enums.reservation_system_enum import ReservationSystemEnum
from fastapi import APIRouter, Depends, Request
from schemas.requests.reservation_schema import (
    GetListExternalReservation,
    SyncDataReservationToPatientWaiting,
)
from services.clinic_service import ClinicService
from services.reservation.patient_waiting_service import PatientWaitingService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/sync-to-patient-waiting")
@version(1, 0)
@measure_time
async def sync_reservation_to_patient_waiting(
    request: Request,
    params: SyncDataReservationToPatientWaiting,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = PatientWaitingService(db_session)
        result = await service.sync_data_from_reservation(params)

        return ApiResponse.success(data={"count_data": result})
    except Exception as e:
        log.error(f"❌ Error in method sync-reservation-to-patient-waiting: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.RESERVATION_SYNC_TO_PATIENT_WAITING.title,
            message_code=CustomMessageCode.RESERVATION_SYNC_TO_PATIENT_WAITING.code,
        )


@router.post("/get-list")
@version(1, 0)
@measure_time
async def get_list_reservation(
    params: GetListExternalReservation,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        clinic_service = ClinicService(db_session)
        clinic_source = await clinic_service.get_clinic_source_mapping(params.source)

        if not clinic_source:
            return ApiResponse.error(
                message=CustomMessageCode.CLINIC_SOURCE_MAPPING_NOT_FOUND.title,
                message_code=CustomMessageCode.CLINIC_SOURCE_MAPPING_NOT_FOUND.code,
            )

        service = _resolve_service(params.source, db_session)
        data = await service.get_list(params, clinic_source.external_clinic_id)

        return ApiResponse.success(data=data.model_dump())
    except ValueError as e:
        log.error(f"❌ Error get list reservation ValueError: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.RESERVATION_GET_LIST_ERROR_VALUE.title,
            message_code=CustomMessageCode.RESERVATION_GET_LIST_ERROR_VALUE.code,
        )
    except Exception as e:
        log.error(f"❌ Error in get list reservation: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.RESERVATION_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.RESERVATION_GET_LIST_FAILED.code,
        )


def _resolve_service(
    source: str,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    if source == ReservationSystemEnum.IAPO.value:
        from services.reservation.iapo_system_service import IAPOSystemService

        return IAPOSystemService(db_session)
    else:
        raise ValueError(f"Unsupported source: {source}")
