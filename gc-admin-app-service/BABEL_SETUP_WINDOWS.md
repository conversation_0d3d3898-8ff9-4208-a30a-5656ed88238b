# Babel Internationalization Setup for Windows

This document provides the correct setup and usage instructions for Babel internationalization commands on Windows systems.

## Root Cause of Previous Issues

The babel commands were failing due to:
1. **PYTHONPATH Issue**: The `gc_dentist_shared` module was not in the Python path
2. **Windows Shell Compatibility**: The `&&` operator in the README examples doesn't work in PowerShell
3. **Path Resolution**: Windows requires different path handling than Unix systems

## Prerequisites

Ensure you have the required dependencies installed:
- `babel==2.17.0` (already in requirements.txt)
- `click` (already in requirements.txt)
- `polib==1.2.0` (already in requirements.txt)

## Solution 1: Using the Provided Scripts (Recommended)

### PowerShell Script Usage

```powershell
# Extract messages
.\babel-commands.ps1 extract

# Extract messages ignoring venv directory
.\babel-commands.ps1 extract -Directory "." -IgnoreDirs "venv"

# Initialize new language locale
.\babel-commands.ps1 init -Language "en_US"
.\babel-commands.ps1 init -Language "ja_<PERSON>"

# Update existing translations
.\babel-commands.ps1 update

# Compile translations
.\babel-commands.ps1 compile -Language "en_US"
.\babel-commands.ps1 compile -Language "ja_JP"
```

### Batch Script Usage

```cmd
REM Extract messages
babel-commands.bat extract

REM Extract messages ignoring venv directory
babel-commands.bat extract . venv

REM Initialize new language locale
babel-commands.bat init en_US
babel-commands.bat init ja_JP

REM Update existing translations
babel-commands.bat update

REM Compile translations
babel-commands.bat compile en_US
babel-commands.bat compile ja_JP
```

## Solution 2: Manual Commands

If you prefer to run commands manually, use these PowerShell commands:

### From the gc-admin-app-service directory:

```powershell
# Set up Python path
$env:PYTHONPATH="$env:PYTHONPATH;C:\Users\<USER>\PycharmProjects\noda-backend"

# Change to app directory
cd app

# Extract messages
python core/common/babel_cli.py extract -d .

# Extract messages ignoring venv
python core/common/babel_cli.py extract -d . --ignore-dirs venv

# Initialize locales
python core/common/babel_cli.py init -l en_US
python core/common/babel_cli.py init -l ja_JP

# Update translations
python core/common/babel_cli.py update

# Compile translations
python core/common/babel_cli.py compile -l en_US
python core/common/babel_cli.py compile -l ja_JP
```

## File Structure

After running the commands, your locale structure should look like:

```
gc-admin-app-service/app/locale/
├── messages.pot                    # Template file
├── default_translations.json       # Default translations
├── en_US/
│   └── LC_MESSAGES/
│       ├── messages.po             # English translations
│       └── messages.mo             # Compiled English
└── ja_JP/
    └── LC_MESSAGES/
        ├── messages.po             # Japanese translations
        └── messages.mo             # Compiled Japanese
```

## Troubleshooting

### Common Issues:

1. **ModuleNotFoundError: No module named 'gc_dentist_shared'**
   - Solution: Ensure PYTHONPATH includes the root directory
   - The provided scripts handle this automatically

2. **PowerShell execution policy errors**
   - Solution: Run `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

3. **Path not found errors**
   - Solution: Ensure you're running commands from the correct directory
   - The scripts handle directory changes automatically

## Workflow

1. **Extract**: Extract translatable strings from your code
2. **Init**: Create new language locale directories (only needed once per language)
3. **Update**: Update existing .po files with new extracted strings
4. **Compile**: Compile .po files to .mo files for use by the application

## Notes

- The scripts automatically set up the correct PYTHONPATH
- All commands should be run from the `gc-admin-app-service` directory
- The babel.cfg file is already properly configured
- Dependencies are already installed in requirements.txt
