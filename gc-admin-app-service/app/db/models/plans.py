from sqlalchemy import Integer  # type: ignore
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>an, Column, Float, String

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class Plans(CentralBase, DateTimeMixin):
    """Plan table"""

    __tablename__ = "plans"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), unique=True, nullable=False)
    price = Column(Float, nullable=False, default=0)
    duration_value = Column(Integer, nullable=False, default=0)
    duration_unit = Column(String(255), nullable=False, default="month")
    features = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
