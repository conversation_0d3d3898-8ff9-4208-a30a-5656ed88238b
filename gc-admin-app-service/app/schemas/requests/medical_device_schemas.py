from datetime import date, time
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType


class FiDataSchema(BaseModel):
    type: MedicalDeviceType
    id: str
    patient_no: int
    examination_date: date


class BfaDataSchema(BaseModel):
    model_config = ConfigDict(extra="forbid")

    business_number: str
    fi_data: FiDataSchema
    auto_cleaning: str
    area_total: float
    area_left: float
    area_right: float
    ave: float
    ave_left: float
    ave_right: float
    max_total: float
    max_left: float
    max_right: float
    force_total: float
    force_left: float
    force_right: float
    comment: str
    image_file_paths: Optional[dict] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class EmgDataSchema(BaseModel):
    model_config = ConfigDict(extra="forbid")

    business_number: str
    fi_data: FiDataSchema
    start_time: time
    end_time: time
    total_time: time
    max_peak: float
    base_peak: float
    total_clenching: int
    clenching_per_hour: float
    burst_total: float
    burst_total_dur: time
    burst_total_ave: time
    burst_per_hour: float
    burst_total_duration_per_hour: time
    clenching_strength_ave: float
    image_file_paths: Optional[dict] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class MvtDataSchema(BaseModel):
    model_config = ConfigDict(extra="forbid")

    business_number: str
    fi_data: FiDataSchema
    image_file_paths: Optional[dict] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class BiteArrayItemSchema(BaseModel):
    level: int
    class_: int = Field(alias="class")
    thickness: str
    area_total: float
    area_left: float
    area_right: float
    point_total: int
    point_left: int
    point_right: int
    rank: str


class BteDataSchema(BaseModel):
    model_config = ConfigDict(extra="forbid")

    business_number: str
    fi_data: FiDataSchema
    bite_array: list[BiteArrayItemSchema]
    comment: Optional[str] = None
    image_file_paths: Optional[dict] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class FileProcessingPayload(BaseModel):
    json_file_path: str
