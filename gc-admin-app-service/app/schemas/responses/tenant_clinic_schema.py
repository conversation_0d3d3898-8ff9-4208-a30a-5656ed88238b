from pydantic import BaseModel, Field


class CreatePlanStorageResponse(BaseModel):
    plan_key_id: int = Field(..., description="plan_key_id of the m_plans")
    tenant_plan_id: int = Field(..., description="tenant_plan_id of the tenant_plans")
    extra_storage_ids: list[int] = Field(..., description="storage_id of the storage")
    total_extra_storage: int = Field(..., description="Sum storage of the tenant")
    default_storage: int = Field(..., description="Default storage size of the plan")
