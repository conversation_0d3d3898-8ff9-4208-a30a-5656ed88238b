"""Init Permission Model

Revision ID: b8e0416771d5
Revises: 4cc104264365
Create Date: 2025-08-20 17:27:21.123514

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b8e0416771d5"  # pragma: allowlist secret
down_revision: Union[str, None] = "4cc104264365"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_permissions",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "module",
            sa.String(length=255),
            nullable=True,
            comment="Module Screen Name allowed to perform",
        ),
        sa.Column(
            "sub_module",
            sa.String(length=255),
            nullable=True,
            comment="Sub Module Screen Name allowed to perform",
        ),
        sa.Column(
            "action", sa.String(length=30), nullable=True, comment="Action for module"
        ),
        sa.Column(
            "permission_key",
            sa.String(length=255),
            nullable=True,
            comment="key permission, Example: Notification:view",
        ),
        sa.Column("delete_flag", sa.Boolean(), nullable=True, comment="Delete flag"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "m_plans",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "code",
            sa.String(length=50),
            nullable=False,
            comment="Plan code (e.g., basic, plus, premium)",
        ),
        sa.Column(
            "price",
            sa.Numeric(precision=10, scale=2),
            nullable=False,
            comment="Plan price",
        ),
        sa.Column("default_storage", sa.Integer(), nullable=False),
        sa.Column(
            "name", sa.Text(), nullable=True, comment="Plan name in multiple languages"
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("code"),
    )
    op.create_table(
        "m_pricing_storages",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "name", sa.String(length=255), nullable=True, comment="Storage plan name"
        ),
        sa.Column(
            "pricing",
            sa.Numeric(precision=10, scale=2),
            nullable=True,
            comment="Price in currency",
        ),
        sa.Column("storage", sa.Integer(), nullable=False),
        sa.Column(
            "period",
            sa.Integer(),
            nullable=False,
            comment="Billing period (1: monthly, 3: yearly, etc.)",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "m_plan_permissions",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("plan_id", sa.Integer(), nullable=False, comment="Reference to plan"),
        sa.Column(
            "permission_id",
            sa.Integer(),
            nullable=False,
            comment="Reference to permission",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["permission_id"], ["m_permissions.id"], ondelete="RESTRICT"
        ),
        sa.ForeignKeyConstraint(["plan_id"], ["m_plans.id"], ondelete="RESTRICT"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tenant_extra_storages",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "name",
            sa.String(length=255),
            nullable=True,
            comment="Storage plan name for tenant",
        ),
        sa.Column("tenant_uuid", sa.UUID(), nullable=True),
        sa.Column("storage_id", sa.Integer(), nullable=True),
        sa.Column(
            "expired_at",
            sa.DateTime(),
            nullable=True,
            comment="Expiration date of the storage plan",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["storage_id"],
            ["m_pricing_storages.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tenant_plans",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            nullable=False,
            comment="Unique identifier of the tenant",
        ),
        sa.Column("plan_id", sa.Integer(), nullable=False, comment="Reference to plan"),
        sa.Column(
            "status",
            sa.String(length=20),
            nullable=False,
            comment="Plan status (active, inactive, suspended, etc.)",
        ),
        sa.Column(
            "start_at",
            sa.DateTime(),
            nullable=False,
            comment="When the plan started for the tenant",
        ),
        sa.Column(
            "end_at",
            sa.DateTime(),
            nullable=True,
            comment="When the plan ends for the tenant (null for unlimited)",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["plan_id"], ["m_plans.id"], ondelete="RESTRICT"),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("tenant_plans")
    op.drop_table("tenant_extra_storages")
    op.drop_table("m_plan_permissions")
    op.drop_table("m_pricing_storages")
    op.drop_table("m_plans")
    op.drop_table("m_permissions")
    # ### end Alembic commands ###
