"""Init master data for plan and storage

Revision ID: e1e9e418d2d9
Revises: a1d9020aae23
Create Date: 2025-08-26 16:36:09.642475

"""

from typing import Sequence, Union

from alembic import op
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision: str = "e1e9e418d2d9"  # pragma: allowlist secret
down_revision: Union[str, None] = "a1d9020aae23"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        text(
            """
            INSERT INTO m_plans (plan_key_id, code, price, default_storage, name, created_at, updated_at)
            VALUES
                (2, 'Basic', 9.99, 10, '{"en": "Basic Plan"}',
                 '2025-08-26 00:00:00', '2025-08-26 00:00:00'),
                (4, 'Premium', 19.99, 50, '{"en": "Premium Plan"}',
                 '2025-08-26 00:00:00', '2025-08-26 00:00:00'),
                 (8, 'Enterprise', 29.99, 100, '{"en": "Enterprise Plan"}',
                 '2025-08-26 00:00:00', '2025-08-26 00:00:00')
            """
        )
    )

    # Add data to m_pricing_storages table
    op.execute(
        text(
            """
            INSERT INTO m_pricing_storages (storage_key_id, name, pricing, storage, period, created_at, updated_at)
            VALUES
                (2, 'Basic Storage', 4.99, 10, 1, '2025-08-26 00:00:00', '2025-08-26 00:00:00'),
                (4, 'Premium Storage', 9.99, 50, 1, '2025-08-26 00:00:00', '2025-08-26 00:00:00'),
                (8, 'Enterprise Storage', 15.99, 100, 1, '2025-08-26 00:00:00', '2025-08-26 00:00:00')
            """
        )
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        text(
            """
            DELETE FROM m_plans
            WHERE plan_key_id IN (2, 4, 8)
            """
        )
    )

    # Delete data from m_pricing_storages based on specific keys
    op.execute(
        text(
            """
            DELETE FROM m_pricing_storages
            WHERE storage_key_id IN (2, 4, 8)
            """
        )
    )
    # ### end Alembic commands ###
