"""add column to oauth tokens

Revision ID: 51b2d672919a
Revises: 96f25c2f6ebf
Create Date: 2025-08-08 16:22:18.628460

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "51b2d672919a"  # pragma: allowlist secret
down_revision: Union[str, None] = "96f25c2f6ebf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "oauth_tokens", sa.<PERSON>umn("clinic_patient_id", sa.Integer(), nullable=True)
    )
    op.add_column(
        "oauth_tokens", sa.Column("clinic_doctor_id", sa.Integer(), nullable=True)
    )
    op.add_column("oauth_tokens", sa.<PERSON>umn("tenant_uuid", sa.TEXT(), nullable=True))
    op.drop_constraint(
        "oauth_tokens_clinic_user_id_fkey", "oauth_tokens", type_="foreignkey"
    )
    op.create_foreign_key(
        None, "oauth_tokens", "tenant_user_mappings", ["clinic_patient_id"], ["id"]
    )
    op.drop_column("oauth_tokens", "clinic_user_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "oauth_tokens",
        sa.Column("clinic_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "oauth_tokens", type_="foreignkey")
    op.create_foreign_key(
        "oauth_tokens_clinic_user_id_fkey",
        "oauth_tokens",
        "tenant_user_mappings",
        ["clinic_user_id"],
        ["id"],
    )
    op.drop_column("oauth_tokens", "tenant_uuid")
    op.drop_column("oauth_tokens", "clinic_doctor_id")
    op.drop_column("oauth_tokens", "clinic_patient_id")
    # ### end Alembic commands ###
