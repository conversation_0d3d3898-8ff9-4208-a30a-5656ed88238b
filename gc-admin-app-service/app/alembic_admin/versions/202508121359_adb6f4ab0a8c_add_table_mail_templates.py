"""add table mail templates

Revision ID: adb6f4ab0a8c
Revises: 51b2d672919a
Create Date: 2025-08-12 13:59:41.262879

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "adb6f4ab0a8c"  # pragma: allowlist secret
down_revision: Union[str, None] = "51b2d672919a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mail_templates",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("category", sa.Integer(), nullable=False),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column("status", sa.<PERSON>(), nullable=True),
        sa.Column("language", sa.String(length=5), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mail_templates_id"), "mail_templates", ["id"], unique=False
    )
    op.drop_constraint(
        "oauth_tokens_clinic_patient_id_fkey", "oauth_tokens", type_="foreignkey"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        "oauth_tokens_clinic_patient_id_fkey",
        "oauth_tokens",
        "tenant_user_mappings",
        ["clinic_patient_id"],
        ["id"],
    )
    op.drop_index(op.f("ix_mail_templates_id"), table_name="mail_templates")
    op.drop_table("mail_templates")
    # ### end Alembic commands ###
