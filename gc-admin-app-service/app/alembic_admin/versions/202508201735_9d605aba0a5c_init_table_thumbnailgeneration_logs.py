"""init table thumbnailgeneration logs

Revision ID: 9d605aba0a5c
Revises: b8e0416771d5
Create Date: 2025-08-20 17:35:43.928354

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9d605aba0a5c"
down_revision: Union[str, None] = "b8e0416771d5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "thumbnail_generation_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "original_image_path",
            sa.Text(),
            nullable=False,
            comment="The S3 path of the original image being processed.",
        ),
        sa.Column(
            "thumbnail_image_path",
            sa.Text(),
            nullable=True,
            comment="The S3 path of the successfully generated thumbnail.",
        ),
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            comment="The status of the file reception. Enums: ThumbnailStatus(IntEnum)",
        ),
        sa.Column(
            "error_message",
            sa.Text(),
            nullable=True,
            comment="Stores the reason for a reception failure.",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("thumbnail_generation_logs")
    # ### end Alembic commands ###
