"""Init mail templates

Revision ID: bed863eccddd
Revises: 6b5cb247c270
Create Date: 2025-08-28 00:29:46.700757

"""

from typing import Sequence, Union

from alembic import op
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision: str = "bed863eccddd"
down_revision: Union[str, None] = "6b5cb247c270"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    login_for_doctor_content_en = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            /* background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%); */
            padding: 15px 30px 20px;
            text-align: center;
        }

        .line-height {
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            height: 10px;
        }

        .logo {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            height: 40px;
        }

        .content {
            padding: 0px 30px 40px;

        }

        .heading-1 {
            color: #099B8E;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .heading-2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            line-height: 1.4;
        }

        .content-text {
            color: #555;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(9, 155, 142, 0.3);
        }

        .action-button:hover {
            background: linear-gradient(135deg, #088079 0%, #099B8E 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(9, 155, 142, 0.4);
        }

        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #099B8E;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-box p:not(:last-child) {
            margin-bottom: 8px;
        }

        .content-otp {
            color: #099B8E;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
            text-align: center;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            color: #888;
            font-size: 14px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 25px 20px;
            }

            .header {
                padding: 20px;
            }

            .heading-1 {
                font-size: 22px;
            }

            .heading-2 {
                font-size: 16px;
            }

            .action-button {
                padding: 12px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="line-height"></div>
    <!-- Header với logo -->
    <div class="header">
        <div class="logo"><img alt="" src="{{logo_url}}" /></div>
    </div>


    <!-- Nội dung chính -->
    <div class="content">
        <!-- HEADING 1 - Tiêu đề chính -->
        <h1 class="heading-1">Account Login Verification</h1>

        <!-- CONTENT - Nội dung chính -->
        <p class="content-text">
            You have requested to log in to your account.
            Please use the following verification code to complete the login process.
        </p>

        <!-- CONTENT - Mã xác thực -->
        <p class="content-otp">
            {{otp}}
        </p>

        <!-- Thông tin quan trọng -->
        <div class="info-box">
            <p><strong>Caution:</strong></p>
            <p>• Please do not share this code with anyone.</p>
            <p>• The verification code expires in {{expires_in}} minutes.</p>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p class="footer-text">
            This is an automated email. Please do not reply to this message.<br>
            © 2026 G-ZONE. All rights reserved.
        </p>
    </div>
</div>
</body>
</html>"""  # noqa: RUF001

    login_for_doctor_content_ja = """<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>メールテンプレート</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            /* background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%); */
            padding: 15px 30px 20px;
            text-align: center;
        }

        .line-height {
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            height: 10px;
        }

        .logo {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            height: 40px;
        }

        .content {
            padding: 0px 30px 40px;

        }

        .heading-1 {
            color: #099B8E;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .heading-2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            line-height: 1.4;
        }

        .content-text {
            color: #555;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(9, 155, 142, 0.3);
        }

        .action-button:hover {
            background: linear-gradient(135deg, #088079 0%, #099B8E 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(9, 155, 142, 0.4);
        }

        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #099B8E;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-box p:not(:last-child) {
            margin-bottom: 8px;
        }

        .content-otp {
            color: #099B8E;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
            text-align: center;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            color: #888;
            font-size: 14px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 25px 20px;
            }

            .header {
                padding: 20px;
            }

            .heading-1 {
                font-size: 22px;
            }

            .heading-2 {
                font-size: 16px;
            }

            .action-button {
                padding: 12px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="line-height"></div>
    <div class="header">
        <div class="logo"><img alt="" src="{{logo_url}}" /></div>
    </div>


    <div class="content">
        <h1 class="heading-1">アカウントログイン認証</h1>

        <p class="content-text">
            アカウントへのログインをリクエストいただきました。
            ログイン手続きを完了するために、以下の認証コードをご利用ください。
        </p>

        <p class="content-otp">
            {{otp}}
        </p>

        <div class="info-box">
            <p><strong>ご注意：</strong></p>
            <p>• このコードを他者とも共有しないでください。
            </p>
            <p>• 認証コードの有効期限は{{expires_in}}分です。</p>
        </div>
    </div>

    <div class="footer">
        <p class="footer-text">
            本メールは自動送信されています。本メールへの返信はできませんのでご了承ください。<br>
            © 2026 G-ZONE. All rights reserved.
        </p>
    </div>
</div>
</body>
</html>"""  # noqa: RUF001

    init_password_for_doctor_content_en = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            /* background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%); */
            padding: 15px 30px 20px;
            text-align: center;
        }

        .line-height {
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            height: 10px;
        }

        .logo {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            height: 40px;
        }

        .content {
            padding: 0px 30px 40px;

        }

        .heading-1 {
            color: #099B8E;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .heading-2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            line-height: 1.4;
        }

        .content-text {
            color: #555;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(9, 155, 142, 0.3);
        }

        .action-button:hover {
            background: linear-gradient(135deg, #088079 0%, #099B8E 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(9, 155, 142, 0.4);
        }

        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #099B8E;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-box p:not(:last-child) {
            margin-bottom: 8px;
        }

        .content-otp {
            color: #099B8E;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
            text-align: center;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            color: #888;
            font-size: 14px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 25px 20px;
            }

            .header {
                padding: 20px;
            }

            .heading-1 {
                font-size: 22px;
            }

            .heading-2 {
                font-size: 16px;
            }

            .action-button {
                padding: 12px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="line-height"></div>
    <!-- Header với logo -->
    <div class="header">

        <div class="logo"><img alt="" src="{{logo_url}}" /></div>
    </div>

    <!-- Nội dung chính -->
    <div class="content">
        <!-- HEADING 1 - Tiêu đề chính -->
        <h1 class="heading-1">Your account has been opened</h1>

        <!-- CONTENT - Nội dung chính -->
        <p class="content-text">
            Welcome to <strong>G-ZONE</strong>! Your account has been successfully created and is ready to use.
        </p>

        <!-- CONTENT - Nội dung bổ sung -->
        <p class="content-text">
            To get started, please click the button below to set up your new password and activate your account.
        </p>

        <!-- ACTION BUTTON - Nút hành động -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{url}}" class="action-button">Set New Password</a>
        </div>

        <!-- Thông tin quan trọng -->
        <div class="info-box">
            <p><strong>Important notes:</strong></p>
            <p>• This link can only be used once</p>
            <p>• The URL will expire in {{expires_in}} days</p>
            <p>• If you didn't request this account, please ignore this email</p>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p class="footer-text">
            This is an automated email. Please do not reply to this message.<br>
            © 2026 G-ZONE. All rights reserved.
        </p>
    </div>
</div>
</body>
</html>"""  # noqa: RUF001

    init_password_for_doctor_content_ja = """<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>メールテンプレート</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            /* background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%); */
            padding: 15px 30px 20px;
            text-align: center;
        }

        .line-height {
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            height: 10px;
        }

        .logo {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            height: 40px;
        }

        .content {
            padding: 0px 30px 40px;

        }

        .heading-1 {
            color: #099B8E;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .heading-2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            line-height: 1.4;
        }

        .content-text {
            color: #555;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(9, 155, 142, 0.3);
        }

        .action-button:hover {
            background: linear-gradient(135deg, #088079 0%, #099B8E 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(9, 155, 142, 0.4);
        }

        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #099B8E;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-box p:not(:last-child) {
            margin-bottom: 8px;
        }

        .content-otp {
            color: #099B8E;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
            text-align: center;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            color: #888;
            font-size: 14px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 25px 20px;
            }

            .header {
                padding: 20px;
            }

            .heading-1 {
                font-size: 22px;
            }

            .heading-2 {
                font-size: 16px;
            }

            .action-button {
                padding: 12px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="line-height"></div>
    <div class="header">
        <div class="logo"><img alt="" src="{{logo_url}}" /></div>
    </div>

    <div class="content">
        <h1 class="heading-1">アカウント開設のお知らせ</h1>

        <p class="content-text">
            <strong>G-ZONE</strong>へようこそ！
            お客様のアカウントが正常に作成され、すぐにご利用いただける状態となりました。
        </p>

        <p class="content-text">
            アカウントを有効化するには、以下のボタンをクリックして新しいパスワードを設定してください。
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{url}}" class="action-button">新しいパスワードを設定する</a>
        </div>

        <div class="info-box">
            <p><strong>重要事項：</strong></p>
            <p>• このリンクは一度のみ有効です。</p>
            <p>• URLの有効期限は{{expires_in}}日間です。</p>
            <p>• 本アカウントの開設にお心当たりがない場合は、このメールを破棄してください。</p>
        </div>
    </div>

    <div class="footer">
        <p class="footer-text">
            本メールは自動送信されています。本メールへの返信はできませんのでご了承ください。<br>
            © 2026 G-ZONE. All rights reserved.
        </p>
    </div>
</div>
</body>
</html>"""  # noqa: RUF001

    reset_password_for_doctor_content_en = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            /* background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%); */
            padding: 15px 30px 20px;
            text-align: center;
        }

        .line-height {
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            height: 10px;
        }

        .logo {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            height: 40px;
        }

        .content {
            padding: 0px 30px 40px;

        }

        .heading-1 {
            color: #099B8E;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .heading-2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            line-height: 1.4;
        }

        .content-text {
            color: #555;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(9, 155, 142, 0.3);
        }

        .action-button:hover {
            background: linear-gradient(135deg, #088079 0%, #099B8E 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(9, 155, 142, 0.4);
        }

        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #099B8E;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-box p:not(:last-child) {
            margin-bottom: 8px;
        }

        .content-otp {
            color: #099B8E;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
            text-align: center;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            color: #888;
            font-size: 14px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 25px 20px;
            }

            .header {
                padding: 20px;
            }

            .heading-1 {
                font-size: 22px;
            }

            .heading-2 {
                font-size: 16px;
            }

            .action-button {
                padding: 12px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="line-height"></div>
    <!-- Header với logo -->
    <div class="header">

        <div class="logo"><img alt="" src="{{logo_url}}" /></div>
    </div>

    <!-- Nội dung chính -->
    <div class="content">
        <!-- HEADING 1 - Tiêu đề chính -->
        <h1 class="heading-1">Your G-ZONE password reset request has been accepted</h1>

        <!-- CONTENT - Nội dung chính -->
        <p class="content-text">
            If you would like to reset your password, please register a new password using the form below.
        </p>

        <p class="content-text">
            <em>If you are not requesting a password reset, you do not need to proceed further.</em>
        </p>

        <!-- ACTION BUTTON - Nút hành động -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{url}}" class="action-button">Reset Password</a>
        </div>

        <!-- Thông tin quan trọng -->
        <div class="info-box">
            <p><strong>Important notes:</strong></p>
            <p>• This link can only be used once</p>
            <p>• The URL will expire in {{expires_in}} hours</p>
            <p>• If you didn't request this password reset, please ignore this email</p>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p class="footer-text">
            This is an automated email. Please do not reply to this message.<br>
            © 2026 G-ZONE. All rights reserved.
        </p>
    </div>
</div>
</body>
</html>"""  # noqa: RUF001

    reset_password_for_doctor_content_ja = """<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>メールテンプレート</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            /* background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%); */
            padding: 15px 30px 20px;
            text-align: center;
        }

        .line-height {
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            height: 10px;
        }

        .logo {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            height: 40px;
        }

        .content {
            padding: 0px 30px 40px;

        }

        .heading-1 {
            color: #099B8E;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .heading-2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            line-height: 1.4;
        }

        .content-text {
            color: #555;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #099B8E 0%, #0BB5A6 100%);
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(9, 155, 142, 0.3);
        }

        .action-button:hover {
            background: linear-gradient(135deg, #088079 0%, #099B8E 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(9, 155, 142, 0.4);
        }

        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #099B8E;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-box p:not(:last-child) {
            margin-bottom: 8px;
        }

        .content-otp {
            color: #099B8E;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.3;
            text-align: center;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            color: #888;
            font-size: 14px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 25px 20px;
            }

            .header {
                padding: 20px;
            }

            .heading-1 {
                font-size: 22px;
            }

            .heading-2 {
                font-size: 16px;
            }

            .action-button {
                padding: 12px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="line-height"></div>
    <div class="header">

        <div class="logo"><img alt="" src="{{logo_url}}" /></div>
    </div>

    <div class="content">
        <h1 class="heading-1">G-ZONE パスワード再設定のご依頼を承りました
        </h1>

        <p class="content-text">
            パスワードを再設定される場合は、以下のフォームより新しいパスワードをご登録ください。
        </p>

        <p class="content-text">
            <em>パスワードの再設定をご希望されない場合は、特にお手続きいただく必要はございません。</em>
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{url}}" class="action-button">パスワードを再設定する</a>
        </div>

        <div class="info-box">
            <p><strong>重要事項：</strong></p>
            <p>• このリンクは一度のみ有効です。</p>
            <p>• URLの有効期限は{{expires_in}}時間です。</p>
            <p>• パスワード再設定をリクエストされていない場合は、このメールを破棄してください。
            </p>
        </div>
    </div>

    <div class="footer">
        <p class="footer-text">
            本メールは自動送信されています。本メールへの返信はできませんのでご了承ください。
            <br>
            © 2026 G-ZONE. All rights reserved.
        </p>
    </div>
</div>
</body>
</html>"""  # noqa: RUF001

    # Execute query to insert data

    conn = op.get_bind()
    conn.execute(
        text(
            """
            INSERT INTO public.mail_templates (category, title, content, status, language)
            VALUES (:category, :title, :content, :status, :language)
            """
        ),
        [
            {
                "category": 1,
                "title": "【G-ZONE】Login Verification Code",
                "content": login_for_doctor_content_en,
                "status": True,
                "language": "en",
            },
            {
                "category": 1,
                "title": "【G-ZONE】ログイン認証コードのご案内",
                "content": login_for_doctor_content_ja,
                "status": True,
                "language": "ja",
            },
            {
                "category": 2,
                "title": "【G-ZONE】Your account is ready!",
                "content": init_password_for_doctor_content_en,
                "status": True,
                "language": "en",
            },
            {
                "category": 2,
                "title": "【G-ZONE】アカウントのご準備ができました！",  # noqa: RUF001
                "content": init_password_for_doctor_content_ja,
                "status": True,
                "language": "ja",
            },
            {
                "category": 3,
                "title": "【G-ZONE】Password reset notice",
                "content": reset_password_for_doctor_content_en,
                "status": True,
                "language": "en",
            },
            {
                "category": 3,
                "title": "【G-ZONE】パスワードリセットのお知らせ",
                "content": reset_password_for_doctor_content_ja,
                "status": True,
                "language": "ja",
            },
        ],
    )


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        text(
            """
            DELETE
            FROM mail_templates
            WHERE category IN (1, 2, 3)
            """
        )
    )
    # ### end Alembic commands ###
