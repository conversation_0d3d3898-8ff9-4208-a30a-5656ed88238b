"""Init Table OAuth

Revision ID: ce1c7971e950
Revises: 215a6943caee
Create Date: 2025-07-30 10:22:35.160492

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ce1c7971e950"
down_revision: Union[str, None] = "215a6943caee"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "auth_providers",
        sa.Column("provider_uuid", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column(
            "provider_type",
            sa.Enum("internal", "oauth2", "oidc", name="providertype"),
            nullable=False,
        ),
        sa.Column("config", sa.<PERSON>(), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("provider_uuid"),
    )
    op.create_table(
        "oauth_clients",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("tenant_uuid", sa.UUID(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("client_id", sa.String(length=48), nullable=True),
        sa.Column("client_secret", sa.String(length=120), nullable=True),
        sa.Column("client_id_issued_at", sa.Integer(), nullable=False),
        sa.Column("client_secret_expires_at", sa.Integer(), nullable=False),
        sa.Column("client_metadata", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_oauth_clients_client_id"), "oauth_clients", ["client_id"], unique=False
    )
    op.create_table(
        "oauth2_authorization_codes",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("code", sa.String(), nullable=False),
        sa.Column("global_user_id", sa.Integer(), nullable=True),
        sa.Column("clinic_user_id", sa.Integer(), nullable=True),
        sa.Column("tenant_uuid", sa.UUID(), nullable=True),
        sa.Column("expires_at", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("client_id", sa.String(length=48), nullable=True),
        sa.Column("redirect_uri", sa.Text(), nullable=True),
        sa.Column("response_type", sa.Text(), nullable=True),
        sa.Column("scope", sa.Text(), nullable=True),
        sa.Column("nonce", sa.Text(), nullable=True),
        sa.Column("auth_time", sa.Integer(), nullable=False),
        sa.Column("acr", sa.Text(), nullable=True),
        sa.Column("amr", sa.Text(), nullable=True),
        sa.Column("code_challenge", sa.Text(), nullable=True),
        sa.Column("code_challenge_method", sa.String(length=48), nullable=True),
        sa.ForeignKeyConstraint(
            ["clinic_user_id"],
            ["tenant_user_mappings.id"],
        ),
        sa.ForeignKeyConstraint(
            ["global_user_id"],
            ["global_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_oauth2_authorization_codes_code"),
        "oauth2_authorization_codes",
        ["code"],
        unique=True,
    )
    op.create_table(
        "oauth_tokens",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("global_user_id", sa.Integer(), nullable=True),
        sa.Column("clinic_user_id", sa.Integer(), nullable=True),
        sa.Column("refresh_token_expires_at", sa.DateTime(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("client_id", sa.String(length=48), nullable=True),
        sa.Column("token_type", sa.String(length=40), nullable=True),
        sa.Column("access_token", sa.String(length=255), nullable=False),
        sa.Column("refresh_token", sa.String(length=255), nullable=True),
        sa.Column("scope", sa.Text(), nullable=True),
        sa.Column("issued_at", sa.Integer(), nullable=False),
        sa.Column("access_token_revoked_at", sa.Integer(), nullable=False),
        sa.Column("refresh_token_revoked_at", sa.Integer(), nullable=False),
        sa.Column("expires_in", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["clinic_user_id"],
            ["tenant_user_mappings.id"],
        ),
        sa.ForeignKeyConstraint(
            ["global_user_id"],
            ["global_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("access_token"),
    )
    op.create_index(
        op.f("ix_oauth_tokens_refresh_token"),
        "oauth_tokens",
        ["refresh_token"],
        unique=False,
    )
    op.add_column("global_users", sa.Column("provider_uuid", sa.UUID(), nullable=True))
    op.add_column(
        "global_users", sa.Column("provider_subject", sa.String(), nullable=True)
    )
    op.create_foreign_key(
        None, "global_users", "auth_providers", ["provider_uuid"], ["provider_uuid"]
    )
    op.add_column(
        "tenant_user_mappings", sa.Column("provider_uuid", sa.UUID(), nullable=True)
    )
    op.add_column(
        "tenant_user_mappings",
        sa.Column("provider_subject", sa.String(), nullable=True),
    )
    op.create_foreign_key(
        None,
        "tenant_user_mappings",
        "auth_providers",
        ["provider_uuid"],
        ["provider_uuid"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "tenant_user_mappings", type_="foreignkey")
    op.drop_column("tenant_user_mappings", "provider_subject")
    op.drop_column("tenant_user_mappings", "provider_uuid")
    op.drop_constraint(None, "global_users", type_="foreignkey")
    op.drop_column("global_users", "provider_subject")
    op.drop_column("global_users", "provider_uuid")
    op.drop_index(op.f("ix_oauth_tokens_refresh_token"), table_name="oauth_tokens")
    op.drop_table("oauth_tokens")
    op.drop_index(
        op.f("ix_oauth2_authorization_codes_code"),
        table_name="oauth2_authorization_codes",
    )
    op.drop_table("oauth2_authorization_codes")
    op.drop_index(op.f("ix_oauth_clients_client_id"), table_name="oauth_clients")
    op.drop_table("oauth_clients")
    op.drop_table("auth_providers")
    # ### end Alembic commands ###
