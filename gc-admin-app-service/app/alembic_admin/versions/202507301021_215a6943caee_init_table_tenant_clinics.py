"""Init Table Tenant Clinics

Revision ID: 215a6943caee
Revises: None
Create Date: 2025-07-30 10:21:17.267535

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "215a6943caee"  # pragma: allowlist secret
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "global_users",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("username", sa.String(), nullable=True),
        sa.Column("password", sa.String(), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("extra_data", sa.JSO<PERSON>(), nullable=True),
        sa.Column("last_login", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_global_users_username"), "global_users", ["username"], unique=True
    )
    op.create_table(
        "tenant_clinics",
        sa.Column("tenant_uuid", sa.UUID(), nullable=False),
        sa.Column("tenant_name", sa.String(), nullable=False),
        sa.Column("tenant_slug", sa.String(), nullable=False),
        sa.Column("db_name", sa.String(), nullable=False),
        sa.Column("db_uri", sa.String(), nullable=True),
        sa.Column("plan_id", sa.Integer(), nullable=True),
        sa.Column("status", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("tenant_uuid"),
        sa.UniqueConstraint("tenant_name"),
        sa.UniqueConstraint("tenant_slug"),
    )
    op.create_table(
        "tenant_user_mappings",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("global_user_id", sa.Integer(), nullable=True),
        sa.Column("tenant_uuid", sa.UUID(), nullable=True),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["global_user_id"],
            ["global_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("tenant_user_mappings")
    op.drop_table("tenant_clinics")
    op.drop_index(op.f("ix_global_users_username"), table_name="global_users")
    op.drop_table("global_users")
    # ### end Alembic commands ###
