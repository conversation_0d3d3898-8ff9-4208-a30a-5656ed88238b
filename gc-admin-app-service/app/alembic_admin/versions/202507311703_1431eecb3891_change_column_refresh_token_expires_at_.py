"""change column refresh_token_expires_at to int

Revision ID: 1431eecb3891
Revises: ce1c7971e950
Create Date: 2025-07-31 17:03:43.455991

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "1431eecb3891"
down_revision: Union[str, None] = "ce1c7971e950"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "oauth_tokens",
        "refresh_token_expires_at",
        existing_type=postgresql.TIMESTAMP(),
        type_=sa.Integer(),
        existing_nullable=True,
        postgresql_using="EXTRACT(epoch FROM refresh_token_expires_at)::integer",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "oauth_tokens",
        "refresh_token_expires_at",
        existing_type=sa.Integer(),
        type_=postgresql.TIMESTAMP(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
