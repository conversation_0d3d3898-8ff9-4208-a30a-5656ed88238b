"""change column id to uuid

Revision ID: 25c4beb966c2
Revises: e0c35cfbc237
Create Date: 2025-08-13 09:24:03.203890

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision: str = "25c4beb966c2"
down_revision: Union[str, None] = "e0c35cfbc237"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Step 1: Add column as nullable
    op.add_column("oauth_tokens", sa.Column("token_uuid", sa.UUID(), nullable=True))

    # Step 2: Generate a UUID for each row
    conn = op.get_bind()
    conn.execute(
        text(
            """
        UPDATE oauth_tokens SET token_uuid = gen_random_uuid()
    """
        )
    )

    # Step 3: Alter column to not nullable and set as primary key
    op.alter_column("oauth_tokens", "token_uuid", nullable=False)
    op.drop_column("oauth_tokens", "id")
    op.create_primary_key("pk_oauth_tokens", "oauth_tokens", ["token_uuid"])

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "oauth_tokens", sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=True)
    )
    conn = op.get_bind()

    # 1. Create the sequence if it doesn't exist
    conn.execute(
        text(
            """
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'oauth_tokens_id_seq') THEN
                CREATE SEQUENCE oauth_tokens_id_seq;
            END IF;
        END
        $$;
    """
        )
    )

    # 2. Set sequence ownership to the id column (optional but recommended)
    conn.execute(
        text(
            """
        ALTER SEQUENCE oauth_tokens_id_seq OWNED BY oauth_tokens.id;
    """
        )
    )

    # 3. Update id values using the sequence
    conn.execute(
        text(
            """
        UPDATE oauth_tokens SET id = nextval('oauth_tokens_id_seq');
    """
        )
    )

    op.alter_column("oauth_tokens", "id", nullable=False)
    op.drop_column("oauth_tokens", "token_uuid")
    op.create_primary_key("pk_oauth_tokens", "oauth_tokens", ["id"])
    # ### end Alembic commands ###
