"""change column type to timestamp

Revision ID: 6b5cb247c270
Revises: e1e9e418d2d9
Create Date: 2025-08-26 17:34:08.575118

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "6b5cb247c270"
down_revision: Union[str, None] = "e1e9e418d2d9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tenant_extra_storages",
        "expired_at",
        existing_type=postgresql.TIMESTAMP(),
        type_=sa.TIMESTAMP(timezone=True),
        existing_comment="Expiration date of the storage plan",
        existing_nullable=True,
    )
    op.alter_column(
        "tenant_plans",
        "start_at",
        existing_type=postgresql.TIMESTAMP(),
        type_=sa.TIMESTAMP(timezone=True),
        existing_comment="When the plan started for the tenant",
        existing_nullable=False,
    )
    op.alter_column(
        "tenant_plans",
        "end_at",
        existing_type=postgresql.TIMESTAMP(),
        type_=sa.TIMESTAMP(timezone=True),
        existing_comment="When the plan ends for the tenant (null for unlimited)",
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tenant_plans",
        "end_at",
        existing_type=sa.TIMESTAMP(timezone=True),
        type_=postgresql.TIMESTAMP(),
        existing_comment="When the plan ends for the tenant (null for unlimited)",
        existing_nullable=True,
    )
    op.alter_column(
        "tenant_plans",
        "start_at",
        existing_type=sa.TIMESTAMP(timezone=True),
        type_=postgresql.TIMESTAMP(),
        existing_comment="When the plan started for the tenant",
        existing_nullable=False,
    )
    op.alter_column(
        "tenant_extra_storages",
        "expired_at",
        existing_type=sa.TIMESTAMP(timezone=True),
        type_=postgresql.TIMESTAMP(),
        existing_comment="Expiration date of the storage plan",
        existing_nullable=True,
    )
    # ### end Alembic commands ###
