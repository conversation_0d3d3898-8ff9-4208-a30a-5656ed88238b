"""change table tenant plant

Revision ID: 0883d6da6c11
Revises: 9d605aba0a5c
Create Date: 2025-08-26 11:51:27.907368

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0883d6da6c11"
down_revision: Union[str, None] = "9d605aba0a5c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # --- m_pricing_storages ---
    op.alter_column(
        "m_pricing_storages",
        "storage",
        existing_type=sa.INTEGER(),
        comment="Storage",
        existing_nullable=False,
    )

    # --- tenant_plans ---
    op.drop_column("tenant_plans", "status")

    op.add_column(
        "tenant_plans",
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            server_default="1",
            comment="Enums TenantPlanStatus",
        ),
    )

    op.create_unique_constraint(
        "uq_tenant_plan", "tenant_plans", ["tenant_uuid", "plan_id"]
    )


def downgrade() -> None:
    op.drop_constraint("uq_tenant_plan", "tenant_plans", type_="unique")

    op.drop_column("tenant_plans", "status")

    op.add_column(
        "tenant_plans",
        sa.Column(
            "status",
            sa.VARCHAR(length=20),
            nullable=False,
            server_default="active",
            comment="Plan status (active, inactive, suspended, etc.)",
        ),
    )

    # --- m_pricing_storages ---
    op.alter_column(
        "m_pricing_storages",
        "storage",
        existing_type=sa.INTEGER(),
        comment=None,
        existing_comment="Storage",
        existing_nullable=False,
    )
