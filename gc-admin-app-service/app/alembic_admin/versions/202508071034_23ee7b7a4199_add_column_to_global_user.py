"""add column to global user

Revision ID: 23ee7b7a4199
Revises: 866d02bd00d0
Create Date: 2025-08-07 10:34:58.326956

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "23ee7b7a4199"  # pragma: allowlist secret
down_revision: Union[str, None] = "866d02bd00d0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "global_users", sa.Column("username_hash", sa.String(), nullable=True)
    )
    op.add_column(
        "global_users",
        sa.Column(
            "day_of_birth", sa.String(), nullable=True, comment="Encrypted day of birth"
        ),
    )
    op.add_column(
        "global_users", sa.Column("day_of_birth_hash", sa.String(), nullable=True)
    )
    op.create_index(
        op.f("ix_global_users_day_of_birth_hash"),
        "global_users",
        ["day_of_birth_hash"],
        unique=False,
    )
    op.create_index(
        op.f("ix_global_users_username_hash"),
        "global_users",
        ["username_hash"],
        unique=False,
    )
    op.add_column(
        "tenant_user_mappings", sa.Column("patient_no", sa.String(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenant_user_mappings", "patient_no")
    op.drop_index(op.f("ix_global_users_username_hash"), table_name="global_users")
    op.drop_index(op.f("ix_global_users_day_of_birth_hash"), table_name="global_users")
    op.drop_column("global_users", "day_of_birth_hash")
    op.drop_column("global_users", "day_of_birth")
    op.drop_column("global_users", "username_hash")
    # ### end Alembic commands ###
