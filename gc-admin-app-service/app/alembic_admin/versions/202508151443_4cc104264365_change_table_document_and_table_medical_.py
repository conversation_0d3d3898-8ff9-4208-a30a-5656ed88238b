"""change table document and table medical device

Revision ID: 4cc104264365
Revises: 25c4beb966c2
Create Date: 2025-08-15 14:43:27.761682

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4cc104264365"
down_revision: Union[str, None] = "25c4beb966c2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "medical_device_sync_logs_json_file_path_key",
        "medical_device_sync_logs",
        type_="unique",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "medical_device_sync_logs_json_file_path_key",
        "medical_device_sync_logs",
        ["json_file_path"],
    )
    # ### end Alembic commands ###
