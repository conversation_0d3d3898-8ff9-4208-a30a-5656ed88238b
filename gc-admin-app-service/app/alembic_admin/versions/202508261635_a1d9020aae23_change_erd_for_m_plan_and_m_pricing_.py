"""Change ERD for m_plan and m_pricing_storages

Revision ID: a1d9020aae23
Revises: e9996f8073df
Create Date: 2025-08-26 16:35:28.496549

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a1d9020aae23"  # pragma: allowlist secret
down_revision: Union[str, None] = "e9996f8073df"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # 1. Add new columns to tables
    op.add_column(
        "m_plans",
        sa.Column("plan_key_id", sa.Integer(), autoincrement=False, nullable=False),
    )
    op.add_column(
        "m_plan_permissions",
        sa.Column(
            "plan_key_id", sa.Integer(), nullable=False, comment="Reference to plan"
        ),
    )
    op.add_column(
        "m_pricing_storages",
        sa.Column("storage_key_id", sa.Integer(), autoincrement=False, nullable=False),
    )
    op.add_column(
        "tenant_extra_storages",
        sa.Column("storage_key_id", sa.Integer(), nullable=True),
    )
    op.add_column(
        "tenant_plans",
        sa.Column(
            "plan_key_id", sa.Integer(), nullable=False, comment="Reference to plan"
        ),
    )

    # 2. Drop all existing foreign key constraints
    op.drop_constraint(
        op.f("m_plan_permissions_plan_id_fkey"),
        "m_plan_permissions",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("tenant_extra_storages_storage_id_fkey"),
        "tenant_extra_storages",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("tenant_plans_plan_id_fkey"), "tenant_plans", type_="foreignkey"
    )

    # 3. Drop old primary keys
    op.drop_constraint("m_plans_pkey", "m_plans", type_="primary")
    op.drop_constraint("m_pricing_storages_pkey", "m_pricing_storages", type_="primary")

    # 4. Create new primary keys on new columns
    op.create_primary_key("m_plans_pkey", "m_plans", ["plan_key_id"])
    op.create_primary_key(
        "m_pricing_storages_pkey", "m_pricing_storages", ["storage_key_id"]
    )

    # 5. Create new foreign keys
    op.create_foreign_key(
        "fk_m_plan_permissions_plan_key_id",
        "m_plan_permissions",
        "m_plans",
        ["plan_key_id"],
        ["plan_key_id"],
        ondelete="RESTRICT",
    )
    op.create_foreign_key(
        "fk_tenant_extra_storages_storage_key_id",
        "tenant_extra_storages",
        "m_pricing_storages",
        ["storage_key_id"],
        ["storage_key_id"],
    )
    op.create_foreign_key(
        "fk_tenant_plans_plan_key_id",
        "tenant_plans",
        "m_plans",
        ["plan_key_id"],
        ["plan_key_id"],
        ondelete="RESTRICT",
    )

    # 6. Handle unique constraints
    op.drop_constraint(op.f("uq_tenant_plan"), "tenant_plans", type_="unique")
    op.create_unique_constraint(
        "uq_tenant_plan", "tenant_plans", ["tenant_uuid", "plan_key_id"]
    )

    # 7. Drop old columns
    op.drop_column("m_plan_permissions", "plan_id")
    op.drop_column("tenant_extra_storages", "storage_id")
    op.drop_column("tenant_plans", "plan_id")
    op.drop_column("m_pricing_storages", "id")
    op.drop_column("m_plans", "id")


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 1. Add back old columns
    op.add_column(
        "m_plans",
        sa.Column(
            "id",
            sa.INTEGER(),
            autoincrement=True,
            nullable=False,
        ),
    )
    op.add_column(
        "m_pricing_storages",
        sa.Column(
            "id",
            sa.INTEGER(),
            autoincrement=True,
            nullable=False,
        ),
    )
    op.add_column(
        "tenant_plans",
        sa.Column(
            "plan_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Reference to plan",
        ),
    )
    op.add_column(
        "tenant_extra_storages",
        sa.Column("storage_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "m_plan_permissions",
        sa.Column(
            "plan_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Reference to plan",
        ),
    )

    # 2. Drop unique constraints
    op.drop_constraint("uq_tenant_plan", "tenant_plans", type_="unique")

    # 3. Drop new foreign keys
    op.drop_constraint(
        "fk_tenant_plans_plan_key_id", "tenant_plans", type_="foreignkey"
    )
    op.drop_constraint(
        "fk_tenant_extra_storages_storage_key_id",
        "tenant_extra_storages",
        type_="foreignkey",
    )
    op.drop_constraint(
        "fk_m_plan_permissions_plan_key_id", "m_plan_permissions", type_="foreignkey"
    )

    # 4. Drop new primary keys
    op.drop_constraint("m_plans_pkey", "m_plans", type_="primary")
    op.drop_constraint("m_pricing_storages_pkey", "m_pricing_storages", type_="primary")

    # 5. Create old primary keys
    op.create_primary_key("m_plans_pkey", "m_plans", ["id"])
    op.create_primary_key("m_pricing_storages_pkey", "m_pricing_storages", ["id"])

    # 6. Create old foreign keys
    op.create_foreign_key(
        op.f("tenant_plans_plan_id_fkey"),
        "tenant_plans",
        "m_plans",
        ["plan_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    op.create_foreign_key(
        op.f("tenant_extra_storages_storage_id_fkey"),
        "tenant_extra_storages",
        "m_pricing_storages",
        ["storage_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("m_plan_permissions_plan_id_fkey"),
        "m_plan_permissions",
        "m_plans",
        ["plan_id"],
        ["id"],
        ondelete="RESTRICT",
    )

    # 7. Create unique constraint
    op.create_unique_constraint(
        op.f("uq_tenant_plan"),
        "tenant_plans",
        ["tenant_uuid", "plan_id"],
        postgresql_nulls_not_distinct=False,
    )

    # 8. Drop new columns
    op.drop_column("tenant_plans", "plan_key_id")
    op.drop_column("tenant_extra_storages", "storage_key_id")
    op.drop_column("m_pricing_storages", "storage_key_id")
    op.drop_column("m_plan_permissions", "plan_key_id")
    op.drop_column("m_plans", "plan_key_id")

    # ### end Alembic commands ###
