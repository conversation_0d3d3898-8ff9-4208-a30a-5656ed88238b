"""change medical device support list image

Revision ID: e0c35cfbc237
Revises: adb6f4ab0a8c
Create Date: 2025-08-13 15:12:28.408560

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "e0c35cfbc237"  # pragma: allowlist secret
down_revision: Union[str, None] = "adb6f4ab0a8c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "import_file_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "source",
            sa.String(),
            nullable=False,
            comment="The source channel that sent the file, e.g., 'MIDDLEWARE_MEDICAL_APP'.",
        ),
        sa.Column(
            "json_file_path",
            sa.Text(),
            nullable=False,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            comment="The status of the file reception. Enums: SourceFileStatus",
        ),
        sa.Column(
            "error_message",
            sa.Text(),
            nullable=True,
            comment="Stores the reason for a reception failure.",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("json_file_path"),
    )
    op.create_index(
        op.f("ix_import_file_logs_source"), "import_file_logs", ["source"], unique=False
    )
    op.drop_index("ix_source_file_logs_source", table_name="source_file_logs")
    op.drop_table("source_file_logs")
    op.add_column(
        "medical_device_bfa",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.alter_column(
        "medical_device_bfa",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_bfa", "image_file_path")
    op.add_column(
        "medical_device_bte",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.alter_column(
        "medical_device_bte",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_bte", "image_file_path")
    op.add_column(
        "medical_device_emg",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.alter_column(
        "medical_device_emg",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_emg", "image_file_path")
    op.add_column(
        "medical_device_mvt",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.alter_column(
        "medical_device_mvt",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_mvt", "image_file_path")
    op.add_column(
        "medical_device_sync_logs",
        sa.Column(
            "json_file_path",
            sa.Text(),
            nullable=True,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
    )
    op.drop_constraint(
        "medical_device_sync_logs_s3_file_path_key",
        "medical_device_sync_logs",
        type_="unique",
    )
    op.create_unique_constraint(
        "medical_device_sync_logs_json_file_path_key",
        "medical_device_sync_logs",
        ["json_file_path"],
    )
    op.drop_column("medical_device_sync_logs", "s3_file_path")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "medical_device_sync_logs",
        sa.Column(
            "s3_file_path",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
    )
    op.drop_constraint(
        "medical_device_sync_logs_json_file_path_key",
        "medical_device_sync_logs",
        type_="unique",
    )
    op.create_unique_constraint(
        "medical_device_sync_logs_s3_file_path_key",
        "medical_device_sync_logs",
        ["s3_file_path"],
    )
    op.drop_column("medical_device_sync_logs", "json_file_path")
    op.add_column(
        "medical_device_mvt",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.alter_column(
        "medical_device_mvt",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_mvt", "image_file_paths")
    op.add_column(
        "medical_device_emg",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.alter_column(
        "medical_device_emg",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_emg", "image_file_paths")
    op.add_column(
        "medical_device_bte",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.alter_column(
        "medical_device_bte",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_bte", "image_file_paths")
    op.add_column(
        "medical_device_bfa",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.alter_column(
        "medical_device_bfa",
        "patient_user_id",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="ID of the patient in the internal system (if available)",
    )
    op.drop_column("medical_device_bfa", "image_file_paths")
    op.create_table(
        "source_file_logs",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "source",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="The source channel that sent the file, e.g., 'MIDDLEWARE_MEDICAL_APP'.",
        ),
        sa.Column(
            "status",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="The status of the file reception. Enums: SourceFileStatus",
        ),
        sa.Column(
            "error_message",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Stores the reason for a reception failure.",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "s3_file_path",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
        sa.PrimaryKeyConstraint("id", name="source_file_logs_pkey"),
        sa.UniqueConstraint("s3_file_path", name="source_file_logs_s3_file_path_key"),
    )
    op.create_index(
        "ix_source_file_logs_source", "source_file_logs", ["source"], unique=False
    )
    op.drop_index(op.f("ix_import_file_logs_source"), table_name="import_file_logs")
    op.drop_table("import_file_logs")
    # ### end Alembic commands ###
