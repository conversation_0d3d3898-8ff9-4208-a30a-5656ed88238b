"""change column type access_token refresh_token to text

Revision ID: 866d02bd00d0
Revises: 1431eecb3891
Create Date: 2025-08-05 15:01:40.702816

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "866d02bd00d0"
down_revision: Union[str, None] = "1431eecb3891"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "oauth_tokens",
        "access_token",
        existing_type=sa.VARCHAR(length=255),
        type_=sa.TEXT(),
        existing_nullable=False,
    )
    op.alter_column(
        "oauth_tokens",
        "refresh_token",
        existing_type=sa.VARCHAR(length=255),
        type_=sa.TEXT(),
        nullable=False,
    )
    op.drop_index("ix_oauth_tokens_refresh_token", table_name="oauth_tokens")
    op.drop_constraint("oauth_tokens_access_token_key", "oauth_tokens", type_="unique")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "oauth_tokens_access_token_key", "oauth_tokens", ["access_token"]
    )
    op.create_index(
        "ix_oauth_tokens_refresh_token", "oauth_tokens", ["refresh_token"], unique=False
    )
    op.alter_column(
        "oauth_tokens",
        "refresh_token",
        existing_type=sa.TEXT(),
        type_=sa.VARCHAR(length=255),
        nullable=True,
    )
    op.alter_column(
        "oauth_tokens",
        "access_token",
        existing_type=sa.TEXT(),
        type_=sa.VARCHAR(length=255),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
