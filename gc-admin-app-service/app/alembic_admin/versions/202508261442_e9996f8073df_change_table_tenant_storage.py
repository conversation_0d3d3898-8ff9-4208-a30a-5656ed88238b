"""change table tenant storage

Revision ID: e9996f8073df
Revises: 0883d6da6c11
Create Date: 2025-08-26 14:42:37.757334

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e9996f8073df"  # pragma: allowlist secret
down_revision: Union[str, None] = "0883d6da6c11"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tenant_extra_storages",
        "storage_id",
        existing_type=sa.INTEGER(),
        nullable=False,
    )
    op.drop_column("tenant_extra_storages", "name")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tenant_extra_storages",
        sa.Column(
            "name",
            sa.VARCHAR(length=255),
            autoincrement=False,
            nullable=True,
            comment="Storage plan name for tenant",
        ),
    )
    op.alter_column(
        "tenant_extra_storages", "storage_id", existing_type=sa.INTEGER(), nullable=True
    )
    # ### end Alembic commands ###
