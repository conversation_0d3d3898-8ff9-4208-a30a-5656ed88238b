"""init table medical device

Revision ID: 96f25c2f6ebf
Revises: 23ee7b7a4199
Create Date: 2025-08-07 14:55:50.676504

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "96f25c2f6ebf"
down_revision: Union[str, None] = "23ee7b7a4199"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_device_sync_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "s3_file_path",
            sa.Text(),
            nullable=True,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
        sa.Column(
            "business_number",
            sa.String(),
            nullable=True,
            comment="Business number of the imported data",
        ),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=True,
            comment="Patient number of the imported data",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=True,
            comment="Device data ID of the imported data",
        ),
        sa.Column(
            "device_type",
            sa.String(),
            nullable=False,
            comment="Device type (BFA, EMG, MVT, BTE),Enums: MedicalDeviceType ",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=True,
            comment="Examination date of the imported data",
        ),
        sa.Column(
            "central_sync_status",
            sa.Integer(),
            nullable=False,
            comment="Synchronization status to Central DB (SUCCESS, FAILED), Enums: MedicalDeviceSyncStatus",
        ),
        sa.Column(
            "tenant_sync_status",
            sa.Integer(),
            nullable=False,
            comment="Synchronization status to Tenant DB (SUCCESS, FAILED), Enums: MedicalDeviceSyncStatus",
        ),
        sa.Column(
            "error_message",
            sa.Text(),
            nullable=True,
            comment="Detailed error message if any process fails",
        ),
        sa.Column(
            "raw_data_payload",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Original JSON payload, used for debugging or reprocessing",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("s3_file_path"),
    )
    op.create_table(
        "source_file_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "source",
            sa.String(),
            nullable=False,
            comment="The source channel that sent the file, e.g., 'MIDDLEWARE_MEDICAL_APP'.",
        ),
        sa.Column(
            "s3_file_path",
            sa.Text(),
            nullable=True,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            comment="The status of the file reception. Enums: SourceFileStatus",
        ),
        sa.Column(
            "error_message",
            sa.Text(),
            nullable=True,
            comment="Stores the reason for a reception failure.",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("s3_file_path"),
    )
    op.create_index(
        op.f("ix_source_file_logs_source"), "source_file_logs", ["source"], unique=False
    )
    op.create_table(
        "medical_device_bfa",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.String(),
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "auto_cleaning",
            sa.String(),
            nullable=True,
            comment="Auto-cleaning mode (ON/OFF), Enums: BfaDeviceAutoCleaning",
        ),
        sa.Column(
            "area_total",
            sa.Float(),
            nullable=False,
            comment="Total occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_left",
            sa.Float(),
            nullable=False,
            comment="Left occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_right",
            sa.Float(),
            nullable=False,
            comment="Right occlusion contact area (mm^2)",
        ),
        sa.Column(
            "ave", sa.Float(), nullable=False, comment="Average pressure - Total (MPa)"
        ),
        sa.Column(
            "ave_left",
            sa.Float(),
            nullable=False,
            comment="Average pressure - Left side (MPa)",
        ),
        sa.Column(
            "ave_right",
            sa.Float(),
            nullable=False,
            comment="Average pressure - Right side (MPa)",
        ),
        sa.Column(
            "max_total",
            sa.Float(),
            nullable=False,
            comment="Maximum pressure - Total (MPa)",
        ),
        sa.Column(
            "max_left",
            sa.Float(),
            nullable=False,
            comment="Maximum pressure - Left side (MPa)",
        ),
        sa.Column(
            "max_right",
            sa.Float(),
            nullable=False,
            comment="Maximum pressure - Right side (MPa)",
        ),
        sa.Column(
            "force_total", sa.Float(), nullable=False, comment="Total bite force (N)"
        ),
        sa.Column(
            "force_left", sa.Float(), nullable=False, comment="Left bite force (N)"
        ),
        sa.Column(
            "force_right", sa.Float(), nullable=False, comment="Right bite force (N)"
        ),
        sa.Column(
            "comment", sa.Text(), nullable=True, comment="Additional notes or comments"
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_bfa_business_patient_device",
        ),
    )
    op.create_table(
        "medical_device_bte",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.String(),
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "bite_array",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="Data array containing detailed information of the bites",
        ),
        sa.Column(
            "comment", sa.Text(), nullable=True, comment="Additional notes or comments"
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_bte_business_patient_device",
        ),
    )
    op.create_table(
        "medical_device_emg",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.String(),
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "start_time", sa.Time(), nullable=False, comment="Measurement start time"
        ),
        sa.Column(
            "end_time", sa.Time(), nullable=False, comment="Measurement end time"
        ),
        sa.Column(
            "total_time",
            sa.Time(),
            nullable=False,
            comment="Total analysis time (string format)",
        ),
        sa.Column(
            "max_peak",
            sa.Float(),
            nullable=False,
            comment="Maximum clenching peak value (mV)",
        ),
        sa.Column(
            "base_peak",
            sa.Float(),
            nullable=False,
            comment="Baseline section peak value (mV)",
        ),
        sa.Column(
            "total_clenching",
            sa.Float(),
            nullable=False,
            comment="Total number of clenches",
        ),
        sa.Column(
            "clenching_per_hour",
            sa.Float(),
            nullable=False,
            comment="Clenching frequency per hour",
        ),
        sa.Column(
            "burst_total", sa.Float(), nullable=False, comment="Total number of bursts"
        ),
        sa.Column(
            "burst_total_dur",
            sa.Time(),
            nullable=False,
            comment="Total duration of bursts (string format)",
        ),
        sa.Column(
            "burst_total_ave",
            sa.Time(),
            nullable=False,
            comment="Average duration of bursts (string format)",
        ),
        sa.Column(
            "burst_per_hour",
            sa.Float(),
            nullable=False,
            comment="Number of bursts per hour",
        ),
        sa.Column(
            "burst_total_duration_per_hour",
            sa.Time(),
            nullable=False,
            comment="Total duration of bursts per hour (string format)",
        ),
        sa.Column(
            "clenching_strength_ave",
            sa.Float(),
            nullable=False,
            comment="Average clenching intensity",
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_emg_business_patient_device",
        ),
    )
    op.create_table(
        "medical_device_mvt",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.String(),
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_mvt_business_patient_device",
        ),
    )
    op.add_column(
        "tenant_clinics",
        sa.Column(
            "business_number",
            sa.String(),
            nullable=True,
            comment="Identifier for the business establishment/clinic",
        ),
    )
    op.execute("UPDATE tenant_clinics SET business_number = tenant_slug")
    op.alter_column(
        "tenant_clinics", "business_number", existing_type=sa.String(), nullable=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenant_clinics", "business_number")
    op.drop_table("medical_device_mvt")
    op.drop_table("medical_device_emg")
    op.drop_table("medical_device_bte")
    op.drop_table("medical_device_bfa")
    op.drop_index(op.f("ix_source_file_logs_source"), table_name="source_file_logs")
    op.drop_table("source_file_logs")
    op.drop_table("medical_device_sync_logs")
    # ### end Alembic commands ###
