import io
import json
import pathlib
from time import time
from typing import Optional

from core.constants import ALLOWED_IMAGE_TYPES
from core.messages import CustomMessageCode
from enums.import_file_enums import ImportFileStatus, SourceImportFile
from fastapi import UploadFile
from schemas.requests.medical_device_schemas import (
    BfaDataSchema,
    BteDataSchema,
    EmgDataSchema,
    MvtDataSchema,
)
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import ImportFileLog
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType
from gc_dentist_shared.core.enums.s3_enums import S3Folder
from gc_dentist_shared.core.exception_handler.custom_exception import (
    CustomValue<PERSON><PERSON>r,
    S3BucketExceptionError,
)
from gc_dentist_shared.core.logger.config import log


class ImportFileService:
    def __init__(self, session: AsyncSession, s3_client: S3Client):
        self.session = session
        self.s3_client = s3_client

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="medical_device_upload_file",
    )
    async def medical_device_upload_file(
        self, json_file: UploadFile, image_files: Optional[list[UploadFile]] = None
    ) -> dict:
        # Step 1: Prepare, validate, and build paths for all files.
        prepared_data = await self._prepare_and_validate_data(json_file, image_files)

        # Step 2: Create an initial log record with 'UPLOADING' status.
        import_file_log_id = await self._create_import_file_log(
            s3_json_path=prepared_data["s3_json_path"],
            s3_image_paths_data=prepared_data["image_paths_data"],
        )

        # Step 3: Upload all files to S3 concurrently.
        result_upload_s3 = await self._prepare_and_upload_batch_to_s3(
            json_data_dict=prepared_data["data"],
            s3_json_path=prepared_data["s3_json_path"],
            json_file=json_file,
            image_files=image_files,
            s3_image_paths=prepared_data["s3_image_paths"],
        )

        # Step 4: Update the log record with the final status.
        async with self.session.begin():
            log_to_update = await self.session.get(ImportFileLog, import_file_log_id)
            if log_to_update:
                log_to_update.status = result_upload_s3["final_status"]
                log_to_update.error_message = result_upload_s3["error_message"]

        # Step 5: If the S3 upload failed, raise a structured error.
        if result_upload_s3["final_status"] == ImportFileStatus.RECEIVE_ERROR:
            raise CustomValueError(
                message=result_upload_s3["error_message"],
                message_code=CustomMessageCode.S3_BUCKET_ERROR.code,
            )

        # Step 6: Return the ID and json file path of the log record upon success.
        return {
            "import_file_log_id": import_file_log_id,
            "json_file_path": prepared_data["s3_json_path"],
        }

    async def _prepare_and_validate_data(
        self, json_file: UploadFile, image_files: Optional[list[UploadFile]]
    ) -> dict:
        """
        Consolidated function to:
        1. Extract data from JSON
        2. Validate and generate S3 paths
        3. Add image paths to the JSON data
        4. Perform final validation on the complete data object
        """
        medical_schema_map = {
            MedicalDeviceType.BFA: BfaDataSchema,
            MedicalDeviceType.EMG: EmgDataSchema,
            MedicalDeviceType.MVT: MvtDataSchema,
            MedicalDeviceType.BTE: BteDataSchema,
        }
        try:
            # 1. Extract data from JSON
            json_contents = await json_file.read()
            data = json.loads(json_contents)
            fi_data = data.get("fi_data", {})
            device_type = fi_data.get("type")
            data_device_id = fi_data.get("id")
            business_number = data.get("business_number")

            if not business_number:
                raise CustomValueError(
                    message=CustomMessageCode.IMPORT_FILE_MISSING_BUSINESS_NUMBER.title,
                    message_code=CustomMessageCode.IMPORT_FILE_MISSING_BUSINESS_NUMBER.code,
                )

            # 3. Validate and generate S3 paths
            timestamp = int(time() * 1000)
            folder_base = (
                f"{business_number}/{device_type}/{data_device_id}_{timestamp}"
            )
            s3_json_path = (
                f"{S3Folder.MEDICAL_DEVICES_FILE_IMPORT.value}/{folder_base}.json"
            )
            s3_image_paths = []
            image_paths_data = {}

            if image_files:
                for i, image in enumerate(image_files):
                    if (image.content_type or "").lower() not in ALLOWED_IMAGE_TYPES:
                        raise CustomValueError(
                            message=CustomMessageCode.IMPORT_FILE_INVALID_IMAGE_TYPE.title,
                            message_code=CustomMessageCode.IMPORT_FILE_INVALID_IMAGE_TYPE.code,
                        )

                    ext = pathlib.Path(image.filename or "").suffix or f"_{i}"
                    image_path = f"{S3Folder.MEDICAL_DEVICES_FILE_IMPORT.value}/{folder_base}{ext}"
                    s3_image_paths.append(image_path)
                    image_paths_data[i + 1] = image_path

            # 3. Add image paths to the JSON data
            data.pop("image_file_name", None)
            data["image_file_paths"] = image_paths_data if image_paths_data else None

            # 4. Perform final validation on the complete data object
            validator_schema = medical_schema_map.get(device_type)
            if not validator_schema:
                raise CustomValueError(
                    message=CustomMessageCode.IMPORT_FILE_INVALID_DEVICE_TYPE.title,
                    message_code=CustomMessageCode.IMPORT_FILE_INVALID_DEVICE_TYPE.code,
                )
            validator_schema(**data)

            return {
                "data": data,
                "s3_json_path": s3_json_path,
                "s3_image_paths": s3_image_paths,
                "image_paths_data": image_paths_data,
            }

        except CustomValueError as e:
            raise e
        except Exception as e:
            log.error(f"❌ _prepare_and_validate_data error: {e}")
            raise CustomValueError(
                message=CustomMessageCode.IMPORT_FILE_INVALID_FORMAT.title,
                message_code=CustomMessageCode.IMPORT_FILE_INVALID_FORMAT.code,
            )

    async def _create_import_file_log(
        self, s3_json_path: str, s3_image_paths_data: Optional[list]
    ) -> int:
        """Creates the initial log record in the database."""
        row = ImportFileLog(
            source=SourceImportFile.MIDDLEWARE_MEDICAL_APP,
            json_file_path=s3_json_path,
            image_file_paths=s3_image_paths_data,
            status=ImportFileStatus.UPLOADING,
        )
        async with self.session.begin():
            self.session.add(row)
        return row.id

    async def _prepare_and_upload_batch_to_s3(
        self,
        json_data_dict: dict,
        s3_json_path: str,
        json_file: UploadFile,
        image_files: Optional[list[UploadFile]],
        s3_image_paths: list[str],
    ) -> dict:
        """
        Prepares the list of files and uploads them concurrently using the S3 client's
        upload_batch_file method.
        """
        final_status = ImportFileStatus.RECEIVE_SUCCESS
        error_message = None

        try:
            # 1. Prepare the JSON file for the batch list
            files_to_upload = [
                {
                    "file": io.BytesIO(json.dumps(json_data_dict).encode("utf-8")),
                    "object_name": s3_json_path,
                    "content_type": json_file.content_type,
                }
            ]

            # 2. Prepare all image files for the batch list
            if image_files:
                image_bytes_list = [await img.read() for img in image_files]
                for i, image_patch in enumerate(s3_image_paths):
                    files_to_upload.append(
                        {
                            "file": io.BytesIO(image_bytes_list[i]),
                            "object_name": image_patch,
                            "content_type": image_files[i].content_type,
                            "tagging": "create_thumbnail=true",
                        }
                    )

            # 3. Call the S3 client with the correctly formatted list
            await self.s3_client.upload_batch_file(files_to_upload)

        except (S3BucketExceptionError, Exception) as e:
            log.error(f"❌ Upload source file S3 error: {e}")
            final_status = ImportFileStatus.RECEIVE_ERROR
            error_message = str(e)

        return dict(final_status=final_status, error_message=error_message)
