from sqlalchemy import select

from gc_dentist_shared.central_models import TenantClinic


async def get_tenant_uuid_by_db_name(central_db_session, db_name: str) -> str:
    async with central_db_session.begin():
        result = await central_db_session.execute(
            select(TenantClinic.tenant_uuid).where(TenantClinic.db_name == db_name)
        )
        tenant_uuid = result.scalar_one_or_none()
        return tenant_uuid
