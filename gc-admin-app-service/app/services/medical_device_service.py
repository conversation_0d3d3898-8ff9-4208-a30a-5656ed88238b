import copy
import json
import os
import uuid
from typing import ClassVar, Optional

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from schemas.requests.medical_device_schemas import (
    BfaDataSchema,
    BteDataSchema,
    EmgDataSchema,
    MvtDataSchema,
)
from services.tenant_clinics_service import TenantClinicService
from sqlalchemy import func, select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.base.tenant_declarative_base import TenantBase
from gc_dentist_shared.central_models import MedicalDeviceBFA as CentralMedicalDeviceBFA
from gc_dentist_shared.central_models import MedicalDeviceBTE as CentralMedicalDeviceBTE
from gc_dentist_shared.central_models import MedicalDeviceEMG as CentralMedicalDeviceEMG
from gc_dentist_shared.central_models import MedicalDeviceMVT as CentralMedicalDeviceMVT
from gc_dentist_shared.central_models import MedicalDeviceSyncLogs, TenantClinic
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import serializer_for_json
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentExtension,
    DocumentGroupKeyName,
    DocumentStatus,
)
from gc_dentist_shared.core.enums.medical_device import (
    MedicalDeviceSyncStatus,
    MedicalDeviceType,
)
from gc_dentist_shared.core.enums.s3_enums import S3Folder, S3RoleEnum
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import DocumentGroup, DocumentManagement
from gc_dentist_shared.tenant_models import MedicalDeviceBFA as TenantMedicalDeviceBFA
from gc_dentist_shared.tenant_models import MedicalDeviceBTE as TenantMedicalDeviceBTE
from gc_dentist_shared.tenant_models import MedicalDeviceEMG as TenantMedicalDeviceEMG
from gc_dentist_shared.tenant_models import MedicalDeviceMVT as TenantMedicalDeviceMVT
from gc_dentist_shared.tenant_models import PatientUser


class MedicalDeviceService:
    DEVICE_MAP: ClassVar[dict] = {
        MedicalDeviceType.BFA: {
            "central_model": CentralMedicalDeviceBFA,
            "tenant_model": TenantMedicalDeviceBFA,
            "schema": BfaDataSchema,
            "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
            "document_group_key_name": DocumentGroupKeyName.BITE_FORCE_ANALYZER.value,
            "document_extension": DocumentExtension.IMAGE.value,
        },
        MedicalDeviceType.EMG: {
            "central_model": CentralMedicalDeviceEMG,
            "tenant_model": TenantMedicalDeviceEMG,
            "schema": EmgDataSchema,
            "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
            "document_group_key_name": DocumentGroupKeyName.WEARABLE_ELECTROMYOGRAPH.value,
            "document_extension": DocumentExtension.IMAGE.value,
        },
        MedicalDeviceType.MVT: {
            "central_model": CentralMedicalDeviceMVT,
            "tenant_model": TenantMedicalDeviceMVT,
            "schema": MvtDataSchema,
            "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
            "document_group_key_name": DocumentGroupKeyName.MOTION_TRAINER.value,
            "document_extension": DocumentExtension.IMAGE.value,
        },
        MedicalDeviceType.BTE: {
            "central_model": CentralMedicalDeviceBTE,
            "tenant_model": TenantMedicalDeviceBTE,
            "schema": BteDataSchema,
            "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
            "document_group_key_name": DocumentGroupKeyName.BITE_EYE.value,
            "document_extension": DocumentExtension.IMAGE.value,
        },
    }

    def __init__(self, central_db_session: AsyncSession, s3_client: S3Client):
        self.central_db_session = central_db_session
        self.s3_client = s3_client

    async def process_file_from_s3(self, json_file_path: str):
        # Step 1: Download and validate the file to get initial data.
        validated_payload = await self._download_and_validate_s3_file(json_file_path)
        medical_data = validated_payload["data"]
        device_config = validated_payload["device_config"]

        # Step 2:Transform data, get tenant uuid, patient_user_id,..
        device_type = medical_data.get("fi_data", {}).get("type")
        tenant_info = await self._transform_data_and_get_tenant_info(medical_data)

        sync_ids = await self._process_sync_items(
            [
                {
                    "json_file_path": json_file_path,
                    "device_type": device_type,
                    "medical_data": medical_data,
                    "device_config": device_config,
                    "tenant_db_name": tenant_info.db_name,
                }
            ]
        )

        return sync_ids[0]

    async def _download_and_validate_s3_file(self, s3_path: str) -> dict:
        """Downloads, parses, validates, and maps the JSON file from S3."""
        try:
            contents = await self.s3_client.get_object(key=s3_path)
            data = json.loads(contents)
            device_type = data.get("fi_data", {}).get("type")
            device_config = self.DEVICE_MAP.get(device_type)
            if not device_config:
                raise CustomValueError(
                    message=CustomMessageCode.PROCESS_FILE_NO_CONFIG_FOUND.title,
                    message_code=CustomMessageCode.PROCESS_FILE_NO_CONFIG_FOUND.code,
                )
            validator_schema = device_config.get("schema")
            data = validator_schema(**data).model_dump()
            return {"data": data, "device_config": device_config}
        except CustomValueError as e:
            raise e
        except Exception as e:
            log.error(
                f"❌ Failed to download or validate S3 file {s3_path}. Error: {e}"
            )
            raise CustomValueError(
                message=CustomMessageCode.IMPORT_FILE_INVALID_FORMAT.title,
                message_code=CustomMessageCode.IMPORT_FILE_INVALID_FORMAT.code,
            )

    async def _transform_data_and_get_tenant_info(self, medical_data) -> TenantClinic:
        # Transform data
        try:
            common_data = medical_data.pop("fi_data")
            medical_data["external_patient_no"] = str(common_data.get("patient_no"))
            medical_data["examination_date"] = common_data.get("examination_date")
            medical_data["device_data_id"] = common_data.get("id")

            business_number = medical_data.get("business_number")

            # Get Tenant info
            tenant_info = await TenantClinicService(
                self.central_db_session
            ).get_tenant_info_by_business_number(business_number)
            if not tenant_info:
                raise CustomValueError(
                    message=CustomMessageCode.PROCESS_FILE_TENANT_NOT_FOUND.title,
                    message_code=CustomMessageCode.PROCESS_FILE_TENANT_NOT_FOUND.code,
                )
            medical_data["tenant_uuid"] = tenant_info.tenant_uuid

            # Get Patient user id
            patient_user_id = await self._get_patient_user_id_by_patient_no(
                tenant_db_name=tenant_info.db_name,
                patient_no=str(medical_data["external_patient_no"]),
            )
            if not patient_user_id:
                raise CustomValueError(
                    message=CustomMessageCode.PROCESS_FILE_PATIENT_USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.PROCESS_FILE_PATIENT_USER_NOT_FOUND.code,
                )

            medical_data["patient_user_id"] = patient_user_id

            return tenant_info
        except CustomValueError as e:
            raise e
        except Exception as e:
            log.error(f"❌ Failed to _transform_data_and_get_tenant_info. Error: {e}")
            raise e

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="process_file_from_s3 - _sync_device_data_for_central",
    )
    async def _get_patient_user_id_by_patient_no(
        self, tenant_db_name: str, patient_no: str
    ) -> int:
        token = set_current_db_name(tenant_db_name)
        try:
            tenant_db_session = await TenantDatabase.get_instance_tenant_db()
            async with tenant_db_session:
                result = await tenant_db_session.execute(
                    select(PatientUser.id).where(
                        PatientUser.patient_no == patient_no,
                        PatientUser.status.is_(True),
                    )
                )
                patient_user_id = result.scalar_one_or_none()
            return patient_user_id
        except Exception as e:
            log.error(f"❌ Failed to _get_patient_user_id_by_patient_no. Error: {e}")
            raise e
        finally:
            reset_current_db_name(token)

    async def _process_sync_items(self, items: list[dict]) -> list[int]:
        """
        items:
            {
              "json_file_path": str,
              "device_type": str,
              "medical_data": dict,
              "device_config": dict,
              "tenant_db_name": str,
            }
        """
        results: list[int] = []

        for it in items:
            json_file_path: str = it["json_file_path"]
            device_type: str = it["device_type"]
            medical_data: dict = it["medical_data"]
            device_config: dict = it["device_config"]
            tenant_db_name: str = it["tenant_db_name"]

            # --- Step 1: Create initial sync log
            sync_log = await self._create_initial_sync_log(
                json_file_path, device_type, medical_data
            )

            # --- Step 2: Build new S3 image paths & update medical_data
            image_file_paths = self.build_new_path_images(
                old_image_file_paths=medical_data["image_file_paths"],
                tenant_uuid=medical_data["tenant_uuid"],
                patient_user_id=medical_data["patient_user_id"],
                device_type=device_type,
                device_data_id=medical_data["device_data_id"],
            )
            medical_data["image_file_paths"] = image_file_paths.get(
                "new_main_image_file_paths"
            )
            medical_data["preview_document_data"] = image_file_paths.get(
                "new_sub_image_file_paths"
            )

            # --- Step 3: Move/validate S3 images
            await self._validate_and_relocate_images(
                image_file_paths.get("payload_move_images_s3")
            )

            # --- Step 4: Sync to Central DB
            error_messages: list[str] = []
            central_result = await self._sync_device_data_for_central(
                medical_data, device_config
            )
            central_status = central_result["status"]
            if central_result["error"]:
                error_messages.append(f"CentralDB: {central_result['error']}")

            # --- Step 5: Sync to Tenant DB
            tenant_result = await self._sync_device_data_for_tenant(
                medical_data, tenant_db_name, device_config
            )
            tenant_status = tenant_result["status"]
            if tenant_result["error"]:
                error_messages.append(f"TenantDB: {tenant_result['error']}")

            # --- Step 6: Update final log
            await self._update_final_log_status(
                medical_data, sync_log.id, central_status, tenant_status, error_messages
            )

            results.append(sync_log.id)

        return results

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="process_file_from_s3 - _create_initial_sync_log",
    )
    async def _create_initial_sync_log(
        self, json_file_path: str, device_type: str, medical_data: dict
    ) -> Optional[MedicalDeviceSyncLogs]:
        """Atomically creates an initial sync log with extracted data."""
        try:
            async with self.central_db_session.begin():
                sync_log = MedicalDeviceSyncLogs(
                    json_file_path=json_file_path,
                    business_number=medical_data.get("business_number"),
                    external_patient_no=medical_data.get("external_patient_no"),
                    device_data_id=medical_data.get("device_data_id"),
                    device_type=device_type,
                    examination_date=medical_data.get("examination_date"),
                    central_sync_status=MedicalDeviceSyncStatus.PROCESSING,
                    tenant_sync_status=MedicalDeviceSyncStatus.PROCESSING,
                    raw_data_payload=None,
                )
                self.central_db_session.add(sync_log)
            return sync_log
        except Exception as e:
            log.error(f"❌ Failed to _create_initial_sync_log. Error: {e}")
            raise e

    def build_new_path_images(
        self,
        old_image_file_paths: dict,
        tenant_uuid,
        patient_user_id,
        device_type,
        device_data_id,
    ):
        try:
            payload_move_images_s3 = []
            base_prefix = (
                f"{tenant_uuid}/{S3RoleEnum.PATIENT.value}/{patient_user_id}/{S3Folder.DOCUMENTS.value}/"
                f"{S3Folder.MEDICAL_DEVICES.value}/{device_type}"
            )
            main_prefix = f"{S3Folder.MAIN.value}/{base_prefix}"
            sub_prefix = f"{S3Folder.SUB.value}/{base_prefix}"

            new_main_image_file_paths = {}
            new_sub_image_file_paths = {}
            for index, old_key in old_image_file_paths.items():
                _, file_ext = os.path.splitext(old_key)
                new_object_name = f"{device_data_id}_{index}{file_ext}"

                new_main_key = self.s3_client.generate_key_s3(
                    prefix=main_prefix, object_name=new_object_name, add_date_path=False
                )
                new_sub_key = self.s3_client.generate_key_s3(
                    prefix=sub_prefix, object_name=new_object_name, add_date_path=False
                )
                new_main_image_file_paths[index] = new_main_key
                new_sub_image_file_paths[index] = new_sub_key

                payload_move_images_s3.append(
                    {
                        "old_key": old_key,
                        "new_key": new_main_key,
                        "delete_old_object": False,
                    }
                )

            return dict(
                new_main_image_file_paths=new_main_image_file_paths,
                new_sub_image_file_paths=new_sub_image_file_paths,
                payload_move_images_s3=payload_move_images_s3,
            )
        except Exception as e:
            log.error(f"❌ Failed to build_new_path_images. Error: {e}")
            raise e

    async def _validate_and_relocate_images(self, payload_move_images_s3):
        # TODO Validate check limit tenant storage

        # Move the main image to Tenant Storage,
        # and there is a Lambda function triggered in this folder to generate the thumbnail afterward.

        await self.s3_client.copy_objects_batch(items=payload_move_images_s3)

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="process_file_from_s3 - _sync_device_data_for_central",
    )
    async def _sync_device_data_for_central(
        self, data: dict, device_config: dict
    ) -> dict:
        try:
            medical_data = copy.deepcopy(data)
            medical_data.pop("preview_document_data")

            central_model_class = device_config["central_model"]
            stmt = insert(central_model_class).values(**medical_data)
            constraint_columns = [
                "business_number",
                "external_patient_no",
                "device_data_id",
            ]
            immutable_cols = ["id", "created_at"]
            column_to_update = {
                c.name: getattr(stmt.excluded, c.name)
                for c in central_model_class.__table__.columns
                if c.name not in (constraint_columns + immutable_cols)
            }
            column_to_update["updated_at"] = func.now()
            final_stmt = stmt.on_conflict_do_update(
                index_elements=constraint_columns, set_=column_to_update
            )
            async with self.central_db_session.begin():
                await self.central_db_session.execute(final_stmt)
            return {"status": MedicalDeviceSyncStatus.SUCCESS, "error": None}
        except Exception as e:
            log.error(f"❌ Failed to sync to Central DB: {e}")
            return {"status": MedicalDeviceSyncStatus.FAILED, "error": str(e)}

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="process_file_from_s3 - _sync_device_data_for_tenant",
    )
    async def _sync_device_data_for_tenant(
        self, data: dict, tenant_db_name: str, device_config: dict
    ) -> dict:
        medical_data = copy.deepcopy(data)

        medical_data.pop("business_number")
        medical_data.pop("tenant_uuid")
        tenant_model_class = device_config["tenant_model"]

        token = set_current_db_name(tenant_db_name)
        try:
            tenant_db_session = await TenantDatabase.get_instance_tenant_db()
            async with tenant_db_session.begin():
                record_id = await self._create_medical_device_data(
                    tenant_db_session=tenant_db_session,
                    medical_data=medical_data,
                    tenant_model_class=tenant_model_class,
                )

                await self._create_document_management(
                    tenant_db_session=tenant_db_session,
                    medical_data=medical_data,
                    device_config=device_config,
                    record_id=record_id,
                    tenant_model_class=tenant_model_class,
                )
            return {"status": MedicalDeviceSyncStatus.SUCCESS, "error": None}
        except Exception as e:
            log.error(f"❌ Failed to sync to tenant DB: {e}")
            return {"status": MedicalDeviceSyncStatus.FAILED, "error": str(e)}
        finally:
            reset_current_db_name(token)

    async def _create_medical_device_data(
        self, tenant_db_session, medical_data: dict, tenant_model_class: TenantBase
    ):
        payload = copy.deepcopy(medical_data)
        payload.pop("preview_document_data")

        stmt = insert(tenant_model_class).values(**payload)
        constraint_columns = [
            "external_patient_no",
            "device_data_id",
        ]
        immutable_cols = ["id", "created_at"]
        column_to_update = {
            c.name: getattr(stmt.excluded, c.name)
            for c in tenant_model_class.__table__.columns
            if c.name not in (constraint_columns + immutable_cols)
        }
        column_to_update["updated_at"] = func.now()
        final_stmt = stmt.on_conflict_do_update(
            index_elements=constraint_columns, set_=column_to_update
        ).returning(tenant_model_class.id)

        result = await tenant_db_session.execute(final_stmt)
        return result.scalar_one()

    async def _create_document_management(
        self,
        tenant_db_session: AsyncSession,
        medical_data: dict,
        device_config: dict,
        record_id: int,
        tenant_model_class: TenantBase,
    ):

        document_group_key_name = device_config["document_group_key_name"]

        document_group_mapping = await self.get_document_group_mapping(
            tenant_db_session
        )
        document_group_id = document_group_mapping.get(document_group_key_name)

        document_management = DocumentManagement(
            patient_user_id=medical_data.get("patient_user_id"),
            name=medical_data.get("device_data_id"),
            status=DocumentStatus.ACTIVATED.value,
            data_type=DocumentDataType.ORIGINAL.value,
            document_data=medical_data.get("image_file_paths"),
            preview_document_data=medical_data.get("preview_document_data"),
            examination_date=medical_data.get("examination_date"),
            medical_history_id=None,
            document_uuid=str(uuid.uuid4()),
            display_mode=device_config["display_mode"],
            document_extension=device_config["document_extension"],
            extra_data={
                "original_table": tenant_model_class.__tablename__,
                "record_id": record_id,
            },
            document_group_id=document_group_id,
        )
        tenant_db_session.add(document_management)

    async def _update_final_log_status(
        self,
        medical_data: dict,
        sync_log_id: int,
        central_status: MedicalDeviceSyncStatus,
        tenant_status: MedicalDeviceSyncStatus,
        errors: list,
    ):
        async with self.central_db_session.begin():
            log_to_update = await self.central_db_session.get(
                MedicalDeviceSyncLogs, sync_log_id
            )
            log_to_update.central_sync_status = central_status
            log_to_update.tenant_sync_status = tenant_status
            log_to_update.error_message = "; ".join(errors) if errors else None
            if (
                central_status != MedicalDeviceSyncStatus.SUCCESS
                or tenant_status != MedicalDeviceSyncStatus.SUCCESS
            ):
                data_to_log = serializer_for_json(copy.deepcopy(medical_data))
                log_to_update.raw_data_payload = data_to_log

    @staticmethod
    async def get_document_group_mapping(db_session: AsyncSession) -> dict[str, int]:
        stmt = select(DocumentGroup.id, DocumentGroup.key_name)
        result = await db_session.execute(stmt)
        rows = result.all()
        mapping = {row.key_name: row.id for row in rows}
        return mapping
