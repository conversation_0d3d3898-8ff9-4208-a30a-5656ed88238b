import ast

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import ClinicRedisKey, TenantClinicStatus
from core.permission.process_sync_permissions import process_sync_tenant
from db.db_connection import TenantDatabase
from schemas.responses.tenant_clinic_schema import CreatePlanStorageResponse
from schemas.tenant_clinic_requests import (
    CreateClinicInfoSchema,
    CreateTenantConfigSchema,
    CreateTenantSchema,
    ExtraStoragesSchema,
    PlanSchema,
    TenantPayloads,
)
from sqlalchemy import func, select, text
from sqlalchemy.exc import DB<PERSON>IError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPlan,
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
    TenantPlan,
)
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.redis import Redis<PERSON><PERSON>
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.role_enum import ROLE_KEY_MAPPING, <PERSON><PERSON>eyEnum
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.permissions.load_file_yaml import load_tenant_roles_yaml
from gc_dentist_shared.tenant_models import (
    ClinicInformation,
    DoctorProfile,
    DoctorRole,
    DoctorUser,
    Roles,
    TenantConfiguration,
)


class TenantClinicService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant",
    )
    async def create_tenant(self, data: TenantPayloads) -> TenantClinic:
        tenant = TenantClinic(
            tenant_name=data.tenant_name,
            tenant_slug=data.tenant_slug,
            business_number=data.business_number,
            db_name=data.db_name,
            db_uri=data.db_uri,
            plan_id=data.plan_id,
        )
        async with self.session.begin():
            self.session.add(tenant)
            await self.session.flush()
            await self.session.refresh(tenant)
            return tenant

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_tenant_status",
    )
    async def update_tenant_status(self, tenant_uuid: str, status: int) -> bool:
        async with self.session.begin():
            result = await self.session.execute(
                text(
                    """
                    UPDATE tenant_clinics
                    SET status = :status
                    WHERE tenant_uuid = :tenant_uuid
                    RETURNING tenant_uuid
                """
                ),
                {"tenant_uuid": tenant_uuid, "status": status},
            )
            row = result.mappings().first()
            return row

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_db_name_for_tenant",
    )
    async def get_db_name_for_tenant(self, tenant_uuid: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                text(
                    "SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"
                ),
                {"tenant_uuid": tenant_uuid},
            )
            db_name = result.scalar_one_or_none()
            return db_name

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_db_by_name",
    )
    async def get_tenant_db_by_name(self, tenant_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.db_name).where(
                    TenantClinic.tenant_name == tenant_name
                )
            )
            db_name = result.scalar_one_or_none()
            return db_name

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_data_by_name",
    )
    async def get_tenant_data_by_name(self, tenant_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic).where(TenantClinic.tenant_name == tenant_name)
            )
            tenant_obj = result.scalar_one_or_none()
            return tenant_obj

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_info_by_business_number",
    )
    async def get_tenant_info_by_business_number(
        self, business_number: str
    ) -> TenantClinic:
        async with self.session:
            stmt = select(TenantClinic).where(
                TenantClinic.business_number == business_number
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()
            return tenant

    async def validate_data_create_tenant(self, data: CreateTenantSchema) -> bool:
        # Validate role_key_id
        roles_data = load_tenant_roles_yaml()
        for role in roles_data.get("roles", []):
            if role.get("role_key_id") not in ROLE_KEY_MAPPING.values():
                log.error(f"❌ Invalid role_key_id {role.get('role_key_id')}")
                return False

        async with self.session:
            # Validate plan_key_id
            plan_key_id = data.plan_info.plan_key_id
            result = await self.session.execute(
                select(MasterPlan.plan_key_id).where(
                    MasterPlan.plan_key_id == plan_key_id
                )
            )
            plan = result.scalar_one_or_none()
            if not plan:
                log.error(f"❌ Invalid plan_key_id {plan_key_id}")
                return False

            # Validate extra storage ids
            extra_storage_ids = [
                storage.storage_key_id for storage in data.extra_storages_info
            ]
            if extra_storage_ids:
                result = await self.session.execute(
                    select(MasterPricingStorage.storage_key_id).where(
                        MasterPricingStorage.storage_key_id.in_(extra_storage_ids)
                    )
                )
                valid_storage_ids = {
                    row["storage_key_id"] for row in result.mappings().all()
                }
                invalid_ids = set(extra_storage_ids) - valid_storage_ids
                if invalid_ids:
                    log.error(f"❌ Invalid extra_storage_ids {invalid_ids}")
                    return False
        return True

    async def create_tenant_multi_plan(
        self, session: AsyncSession, tenant_uuid: str, objs: list[PlanSchema]
    ):
        if not objs:
            return []
        plan_ids = []
        for obj in objs:
            try:
                plan = TenantPlan(
                    tenant_uuid=tenant_uuid,
                    **obj.model_dump(mode="json"),
                )
                session.add(plan)
                await session.flush()
                await session.refresh(plan)
                plan_ids.append(plan.id)
            except Exception as e:
                log.error(f"❌ Error creating tenant plan: {str(e)}")
        return plan_ids

    async def create_tenant_plan(
        self, session: AsyncSession, tenant_uuid: str, obj: PlanSchema
    ):
        if not obj:
            return 0
        try:
            plan = TenantPlan(
                tenant_uuid=tenant_uuid,
                **obj.model_dump(mode="json"),
            )
            session.add(plan)
            await session.flush()
            await session.refresh(plan)
            return plan.id
        except Exception as e:
            log.error(f"❌ Error creating tenant plan: {str(e)}")

        return 0

    async def create_tenant_multi_extra_storages(
        self, session: AsyncSession, tenant_uuid: str, objs: list[ExtraStoragesSchema]
    ):
        if not objs:
            return []
        storage_ids = []
        for obj in objs:
            try:
                storage = TenantExtraStorage(
                    tenant_uuid=tenant_uuid,
                    **obj.model_dump(mode="json"),
                )
                session.add(storage)
                await session.flush()
                await session.refresh(storage)
                storage_ids.append(storage.id)
            except Exception as e:
                log.error(f"❌ Error creating tenant extra storage: {str(e)}")
        return storage_ids

    async def sum_extra_storage_of_tenant(
        self, session: AsyncSession, extra_storage_ids: list[int]
    ) -> int:
        if not extra_storage_ids:
            return 0
        total_size = 0
        try:
            result = await session.execute(
                select(func.sum(MasterPricingStorage.storage)).where(
                    MasterPricingStorage.storage_key_id.in_(extra_storage_ids)
                )
            )
            total_size = result.scalar_one_or_none() or 0
        except Exception as e:
            log.error(f"❌ Error summing storage sizes: {str(e)}")

        return total_size

    async def get_default_stograge(
        self, session: AsyncSession, plan_key_id: int
    ) -> int:
        try:
            result = await session.execute(
                select(MasterPlan.default_storage).where(
                    MasterPlan.plan_key_id == plan_key_id
                )
            )
            default_storage = result.scalar_one_or_none() or 0

            return default_storage
        except Exception as e:
            log.error(f"❌ Error getting default storage: {str(e)}")
            return 0

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant_plans_and_storages",
    )
    async def create_tenant_plans_and_storages(
        self,
        tenant_uuid: str,
        plan: PlanSchema,
        extra_storages: list[ExtraStoragesSchema],
    ) -> CreatePlanStorageResponse:
        async with self.session.begin():
            # Create tenant plan
            tenant_plan_id = await self.create_tenant_plan(
                self.session, tenant_uuid, plan
            )
            # Create tenant relation storages
            tenant_relation_storage_ids = await self.create_tenant_multi_extra_storages(
                self.session, tenant_uuid, extra_storages
            )
            # Calculate total extra storage
            master_extra_storage_ids = [
                storage.storage_key_id for storage in extra_storages
            ]
            total_extra_storage = await self.sum_extra_storage_of_tenant(
                session=self.session, extra_storage_ids=master_extra_storage_ids
            )
            # Get default storage from plan
            default_storage = await self.get_default_stograge(
                session=self.session,
                plan_key_id=plan.plan_key_id,
            )
            return CreatePlanStorageResponse(
                tenant_plan_id=tenant_plan_id,
                plan_key_id=plan.plan_key_id,
                default_storage=default_storage,
                extra_storage_ids=tenant_relation_storage_ids,
                total_extra_storage=total_extra_storage,
            )

    async def create_tenant_clinic_doctor(
        self, session: AsyncSession, data: CreateClinicInfoSchema
    ) -> int:
        """
        Create a new clinic account record in the database.
        :param data: Data for creating a clinic.
        :return: The id created clinic doctor.
        """
        clinic_ifo = ClinicInformation(
            **data.model_dump(exclude={"manager_info", "clinic_db_name"})
        )
        admin_user = DoctorUser(
            username=data.manager_info.email,
            required_change_password=data.manager_info.required_change_password,
        )
        admin_user.set_password(data.manager_info.password, data.clinic_uuid)

        # Add clinic information
        session.add(clinic_ifo)
        await session.flush()
        await session.refresh(clinic_ifo)

        # Add admin user
        session.add(admin_user)
        await session.flush()
        await session.refresh(admin_user)
        return admin_user.id

    async def create_tenant_clinic_doctor_profile(
        self, session: AsyncSession, data: CreateClinicInfoSchema, doctor_user_id: int
    ) -> int:
        admin_profile = DoctorProfile(
            doctor_user_id=doctor_user_id,
            **data.manager_info.model_dump(
                exclude={
                    "password",
                    "required_change_password",
                    "is_active",
                },
                mode="json",
            ),
        )

        # Encrypt sensitive fields
        aes = AesGCMRotation(configuration)
        admin_profile.phone_hash = aes.sha256_hash(admin_profile.phone)
        admin_profile.email_hash = aes.sha256_hash(admin_profile.email)
        admin_profile.date_of_birth_hash = aes.sha256_hash(admin_profile.date_of_birth)
        admin_profile.phone = aes.encrypt_data(admin_profile.phone)
        admin_profile.email = aes.encrypt_data(admin_profile.email)
        admin_profile.date_of_birth = aes.encrypt_data(admin_profile.date_of_birth)

        session.add(admin_profile)
        await session.flush()
        await session.refresh(admin_profile)
        return admin_profile.id

    async def create_roles_default_for_clinic(self, session: AsyncSession) -> None:
        roles_data = load_tenant_roles_yaml()
        for role in roles_data.get("roles", []):
            role_obj = Roles(**role)
            session.add(role_obj)
        await session.flush()

    async def create_relation_role_doctor(
        self, session: AsyncSession, doctor_user_id: int
    ):
        doctor = DoctorRole(
            doctor_user_id=doctor_user_id,
            role_key_id=RoleKeyEnum.CLINIC_ADMIN.value,
        )
        session.add(doctor)
        await session.flush()
        return doctor.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant_config",
    )
    async def create_tenant_config(
        self, session: AsyncSession, obj: CreateTenantConfigSchema
    ):
        try:
            async with session.begin():
                config = TenantConfiguration(
                    **obj.model_dump(),
                )
                session.add(config)
                await session.flush()
                await session.refresh(config)
                return config.tenant_uuid
        except Exception as e:
            log.error(f"❌ Error creating tenant configuration: {str(e)}")

        return 0

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_admin_and_role_for_tenant",
    )
    async def create_admin_and_role_for_tenant(
        self,
        session: AsyncSession,
        clinic_data: CreateClinicInfoSchema,
    ) -> int:
        async with session.begin():
            doctor_user_id = await self.create_tenant_clinic_doctor(
                session=session,
                data=clinic_data,
            )
            await self.create_tenant_clinic_doctor_profile(
                session=session,
                data=clinic_data,
                doctor_user_id=doctor_user_id,
            )
            await self.create_roles_default_for_clinic(session=session)
            await self.create_relation_role_doctor(
                session=session,
                doctor_user_id=doctor_user_id,
            )
        return doctor_user_id

    async def create_tenant_clinic_settings(
        self,
        clinic_data: CreateClinicInfoSchema,
        plan_info: PlanSchema,
        extra_storages_info: list[ExtraStoragesSchema],
    ):
        token = set_current_db_name(clinic_data.clinic_db_name)
        try:
            session = await TenantDatabase.get_instance_tenant_db()
            doctor_user_id = await self.create_admin_and_role_for_tenant(
                session=session,
                clinic_data=clinic_data,
            )
            log.info(
                f"✅ Created clinic admin and roles for {clinic_data.clinic_slug} (id={doctor_user_id})"
            )

            plan_storage = await self.create_tenant_plans_and_storages(
                tenant_uuid=clinic_data.clinic_uuid,
                plan=plan_info,
                extra_storages=extra_storages_info,
            )
            log.info(
                f"✅ Created tenant plans and storages for {clinic_data.clinic_slug}"
            )

            # Create tenant configuration info
            await self.create_tenant_config(
                session=session,
                obj=CreateTenantConfigSchema(
                    tenant_uuid=clinic_data.clinic_uuid,
                    tenant_name=clinic_data.clinic_slug,
                    tenant_slug=clinic_data.clinic_db_name,
                    plan_id=plan_info.plan_key_id,
                    default_storage=plan_storage.default_storage,
                    extra_storage=plan_storage.total_extra_storage,
                ),
            )
            log.info(f"✅ Created clinic settings for {clinic_data.clinic_slug}")
            await process_sync_tenant(
                [clinic_data.clinic_db_name], plan_info.plan_key_id
            )

        except (OperationalError, DBAPIError) as e:
            log.error(f"❌ Database error creating clinic settings: {str(e)}")
            raise e
        except Exception as e:
            log.error(f"❌ Error creating clinic info: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token)
            await self.delete_cache_clinic_slug()

    async def delete_cache_clinic_slug(self) -> None:
        """
        Delete the clinic slug cache.
        :return: None
        """
        try:
            redis_cli = await RedisCli.get_instance(configuration)
            prefix = ClinicRedisKey.CLINICS_CACHE.value
            await redis_cli.delete(prefix)
        except Exception as e:
            log.error(f"❌ Error setting cache for clinic slugs: {str(e)}")

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_clinic_slugs",
    )
    async def get_list_clinic_from_central_db(self):
        """
        Get a list of all clinics.
        :return: List of ClinicInformation objects.
        """
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.tenant_slug).where(
                    TenantClinic.status == TenantClinicStatus.SUCCESS.value
                )
            )
            rows = result.scalars().all()
            return [row for row in rows if row is not None]

    async def set_cache_clinic_slug(self, list_clinic: list[str]) -> None:
        """
        Set the clinic slug in cache.
        :param tenant_slug: The slug of the clinic.
        """
        try:
            redis_cli = await RedisCli.get_instance(configuration)
            prefix = ClinicRedisKey.CLINICS_CACHE.value

            if list_clinic:
                await redis_cli.set(prefix, str(list_clinic))
            else:
                await redis_cli.delete(prefix)
        except Exception as e:
            log.error(f"❌ Error setting cache for clinic slugs: {str(e)}")

    async def get_cache_clinic_slug(self) -> list[str]:
        """
        Get the clinic slug from cache.
        :return: List of clinic slugs.
        """
        redis_cli = await RedisCli.get_instance(configuration)
        prefix = ClinicRedisKey.CLINICS_CACHE.value
        cached_value = await redis_cli.get(prefix)

        if cached_value:
            try:
                list_clinic = ast.literal_eval(cached_value)
                if isinstance(list_clinic, list):
                    return list_clinic
            except (SyntaxError, ValueError) as e:
                log.error(f"❌ Error parsing cached clinic slugs: {str(e)}")
        return []

    async def check_clinic_slug_exists(self, tenant_slug: str) -> bool:
        """
        Check if the clinic slug exists in the cache.
        :param tenant_slug: The slug of the clinic.
        :return: True if the slug exists, False otherwise.
        """
        list_clinic = await self.get_cache_clinic_slug()
        if not list_clinic:
            list_clinic = await self.get_list_clinic_from_central_db()
            await self.set_cache_clinic_slug(list_clinic)

        return tenant_slug in list_clinic if list_clinic else False

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_uuid_by_db_name",
    )
    async def get_tenant_uuid_by_db_name(self, db_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.tenant_uuid).where(TenantClinic.db_name == db_name)
            )
            tenant_uuid = result.scalar_one_or_none()
            return tenant_uuid
