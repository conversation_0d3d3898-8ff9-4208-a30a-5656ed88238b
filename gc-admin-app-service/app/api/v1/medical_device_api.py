from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import ApiR<PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends
from schemas.requests.medical_device_schemas import FileProcessingPayload
from services.medical_device_service import MedicalDeviceService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.dependencies.api_verify_key_depend import (
    DependsAPIVerifyKey,
)
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    path="",
    summary="Api process handle file medical device json form s3",
    dependencies=[
        DependsAPIVerifyKey(configuration.LAMBDA_MEDICAL_DEVICE_PROCESS_FILE_SECRET_KEY)
    ],
)
@version(1, 0)
@measure_time
async def medical_device_process_file(
    db_central_session: Annotated[
        AsyncSession, Depends(CentralDatabase.get_db_session)
    ],
    obj_request: FileProcessingPayload,
):
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        medical_device_service = MedicalDeviceService(db_central_session, s3_client)
        sync_log_id = await medical_device_service.process_file_from_s3(
            json_file_path=obj_request.json_file_path,
        )
        return ApiResponse.success(
            data={"medical_device_sync_log_id": sync_log_id},
            message=CustomMessageCode.PROCESS_FILE_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error medical_device_process_file CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ medical_device_process_file error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.PROCESS_FILE_FAILED.title,
            message_code=CustomMessageCode.PROCESS_FILE_FAILED.code,
        )
