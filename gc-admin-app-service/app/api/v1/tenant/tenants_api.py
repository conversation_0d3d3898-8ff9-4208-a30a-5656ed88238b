from typing import Annotated

from core.common.api_response import ApiR<PERSON>ponse
from core.constants import X_TENANT_SLUG
from core.messages import ERROR_PROCESS, INIT_CLINIC_INPROCESS, CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends, Request, status
from fastapi.responses import JSONResponse, StreamingResponse
from schemas.tenant_clinic_requests import (
    CreateClinicInfoSchema,
    CreateTenantSchema,
    MigrationTenantPayloads,
)
from services.migration_service import (
    create_tenant_stream,
    migration_multi_database_stream,
    process_migrate_tenant,
)
from services.tenant_clinics_service import TenantClinicService
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_utils import create_database, database_exists

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.common.i18n import i18n as _

router = APIRouter()


@router.post("")
@version(1, 0)
async def create_tenant(
    data: CreateTenantSchema,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    db_uri = (
        CentralDatabase()
        .get_url_db_sync(db_name=data.db_name)
        .render_as_string(hide_password=False)
    )

    tenant_service = TenantClinicService(db_session)
    valid_check = await tenant_service.validate_data_create_tenant(data=data)
    if not valid_check:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": ERROR_PROCESS},
        )
    try:
        tenant_obj = await tenant_service.create_tenant(data)
        if not database_exists(db_uri):
            log.info(f"🚀 Start Create Database {str(db_uri)}")
            create_database(db_uri)

        await process_migrate_tenant(tenant_obj.tenant_uuid, data)
        await tenant_service.create_tenant_clinic_settings(
            clinic_data=CreateClinicInfoSchema(
                **data.clinic_info.model_dump(),
                clinic_slug=data.tenant_slug,
                clinic_uuid=str(tenant_obj.tenant_uuid),
                clinic_db_name=data.db_name,
                manager_info=data.manager_info,
            ),
            plan_info=data.plan_info,
            extra_storages_info=data.extra_storages_info,
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"detail": INIT_CLINIC_INPROCESS},
        )
    except Exception as e:
        log.error("❌ Create New DataBase Tenant Error: {}".format(str(e)))
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": ERROR_PROCESS},
        )


@router.post("/streaming", response_class=StreamingResponse)
@version(1, 0)
async def create_tenant_streaming(
    data: CreateTenantSchema,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    stream = create_tenant_stream(data, db_session)
    return StreamingResponse(stream, media_type="text/plain")


@router.post("/migration/streaming", response_class=StreamingResponse)
@version(1, 0)
async def migration_tenant_streaming(
    data: MigrationTenantPayloads,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    stream = migration_multi_database_stream(data)
    return StreamingResponse(stream, media_type="text/plain")


@router.get("/slugs", dependencies=[])
@version(1, 0)
async def check_clinic_slugs(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    tenant_slug = request.headers.get(X_TENANT_SLUG)
    tenant_slug = tenant_slug.strip() if tenant_slug else None
    if not tenant_slug:
        return ApiResponse.error(
            message=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
            message_code=CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
        )
    try:
        clinic_service = TenantClinicService(db_session)
        if await clinic_service.check_clinic_slug_exists(tenant_slug):
            return ApiResponse.success(
                data={"slug": tenant_slug, "exists": True},
            )
    except Exception as e:
        log.error("❌ Get Clinic Slugs Error: {}".format(str(e)))

    return ApiResponse.error(
        data={"slug": tenant_slug, "exists": False},
        message=CustomMessageCode.CLINIC_NOT_FOUND.title,
    )
