import io
from copy import deepcopy
from unittest.mock import AsyncMock

import pytest
from core.messages import CustomMessageCode
from enums.import_file_enums import ImportFileStatus
from sqlalchemy import select
from tests.helpers.file_mock import create_mock_image_file, create_mock_json_file

from gc_dentist_shared.central_models import ImportFileLog

VALID_BFA_DATA = {
    "fi_data": {
        "type": "BFA",
        "id": "bfa-device-123",
        "patient_no": 101,
        "examination_date": "2025-08-25",
    },
    "business_number": "business-bfa-001",
    "auto_cleaning": "ON",
    "area_total": 100.5,
    "area_left": 50.2,
    "area_right": 50.3,
    "ave": 80.1,
    "ave_left": 80.2,
    "ave_right": 80.0,
    "max_total": 150.0,
    "max_left": 140.0,
    "max_right": 145.0,
    "force_total": 200.0,
    "force_left": 100.0,
    "force_right": 100.0,
    "comment": "BFA test comment",
}

VALID_EMG_DATA = {
    "fi_data": {
        "type": "EMG",
        "id": "emg-device-456",
        "patient_no": 102,
        "examination_date": "2025-08-25",
    },
    "business_number": "business-emg-002",
    "start_time": "09:00:00",
    "end_time": "09:30:00",
    "total_time": "00:30:00",
    "max_peak": 120.5,
    "base_peak": 20.1,
    "total_clenching": 15,
    "clenching_per_hour": 30.0,
    "burst_total": 5.0,
    "burst_total_dur": "00:05:00",
    "burst_total_ave": "00:00:20",
    "burst_per_hour": 10.0,
    "burst_total_duration_per_hour": "00:10:00",
    "clenching_strength_ave": 75.5,
}

VALID_MVT_DATA = {
    "fi_data": {
        "type": "MVT",
        "id": "mvt-device-789",
        "patient_no": 103,
        "examination_date": "2025-08-25",
    },
    "business_number": "business-mvt-003",
}

VALID_BTE_DATA = {
    "fi_data": {
        "type": "BTE",
        "id": "bte-device-987",
        "patient_no": 104,
        "examination_date": "2025-08-25",
    },
    "business_number": "business-bte-004",
    "bite_array": [
        {
            "level": 1,
            "class": 3,
            "thickness": "0-149",
            "area_total": 45.7,
            "area_left": 28.8,
            "area_right": 16.9,
            "point_total": 14,
            "point_left": 9,
            "point_right": 5,
            "rank": "E",
        }
    ],
    "comment": "BTE test comment",
}


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "device_type, json_data, image_nums",
    [
        ("BFA", VALID_BFA_DATA, 1),
        ("EMG", VALID_EMG_DATA, 1),
        ("MVT", VALID_MVT_DATA, 1),
        ("BTE", VALID_BTE_DATA, 1),
    ],
)
async def test_medical_device_upload_success_for_all_types(
    async_client,
    mocker,
    async_central_db_session_object,
    device_type,
    json_data,
    image_nums,
):
    """
    Tests successful upload for all supported medical device types.
    """
    # Arrange
    mock_s3_client = AsyncMock()
    mock_s3_client.upload_batch_file = AsyncMock()
    mocker.patch(
        "services.import_file_service.S3Client.get_instance",
        return_value=mock_s3_client,
    )

    files_payload = [
        ("json_file", create_mock_json_file(json_data)),
    ]

    for i in range(image_nums):
        files_payload.append(
            (
                "image_files",
                create_mock_image_file(f"image_{i + 1}.jpg", "image/jpeg"),
            )
        )

    # Act
    response = await async_client.post(
        "/v1_0/import-files/medical-devices", files=files_payload
    )

    # Assert
    assert (
        response.status_code == 200
    ), f"Failed for device type {device_type}. Response: {response.json()}"

    expected_file_count = 1 + image_nums
    files_sent_to_s3 = mock_s3_client.upload_batch_file.call_args[0][0]
    assert len(files_sent_to_s3) == expected_file_count

    response_json = response.json()
    log_id = response_json["data"]["import_file_log_id"]
    async with async_central_db_session_object:
        log_entry = await async_central_db_session_object.get(ImportFileLog, log_id)
        assert log_entry is not None
        assert log_entry.status == ImportFileStatus.RECEIVE_SUCCESS


@pytest.mark.asyncio
async def test_medical_device_upload_malformed_json(async_client, mocker):
    """
    Tests that the API returns a 400 error if the uploaded JSON file is malformed.
    """
    malformed_json_string = b'{"business_number": "123", "fi_data": {"type": "BFA"},}'  # "," invalid json content

    files_payload = [
        (
            "json_file",
            ("bad.json", io.BytesIO(malformed_json_string), "application/json"),
        ),
    ]

    mocker.patch("services.import_file_service.S3Client.get_instance")

    # Act
    response = await async_client.post(
        "/v1_0/import-files/medical-devices", files=files_payload
    )

    # Assert
    assert response.status_code == 400
    assert (
        response.json()["messageCode"]
        == CustomMessageCode.IMPORT_FILE_INVALID_FORMAT.code
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, base_data, field_to_remove",
    [
        (
            "bfa_missing_required_field",
            VALID_BFA_DATA,
            "area_total",
        ),
        (
            "emg_missing_required_field",
            VALID_EMG_DATA,
            "max_peak",
        ),
        (
            "bte_missing_required_field",
            VALID_BTE_DATA,
            "bite_array",
        ),
    ],
)
async def test_medical_device_upload_missing_required_field(
    async_client, mocker, test_id, base_data, field_to_remove
):
    """
    Tests for 400 error when the JSON data is missing a required field
    for a specific device type schema.
    """
    # Arrange
    invalid_data = deepcopy(base_data)

    del invalid_data[field_to_remove]

    files_payload = [
        ("json_file", create_mock_json_file(invalid_data)),
    ]

    mocker.patch("services.import_file_service.S3Client.get_instance")

    # Act
    response = await async_client.post(
        "/v1_0/import-files/medical-devices", files=files_payload
    )

    # Assert
    assert response.status_code == 400, f"Failed on test: {test_id}"

    assert (
        response.json()["messageCode"]
        == CustomMessageCode.IMPORT_FILE_INVALID_FORMAT.code
    )


@pytest.mark.asyncio
async def test_medical_device_upload_s3_failure(
    async_client, mocker, async_central_db_session_object
):
    """
    Tests that the API correctly handles an S3 upload failure by returning a 400 error
    and updating the database log status to RECEIVE_ERROR.
    """
    # Arrange
    # 1. Mock the S3 client to raise an exception on upload
    mock_s3_client = AsyncMock()
    s3_error_message = "Could not connect to S3 bucket"
    mock_s3_client.upload_batch_file = AsyncMock(
        side_effect=Exception(s3_error_message)
    )
    mocker.patch(
        "services.import_file_service.S3Client.get_instance",
        return_value=mock_s3_client,
    )

    # 2. Prepare a valid JSON and file payload
    files_payload = [
        ("json_file", create_mock_json_file(VALID_BTE_DATA)),
        ("image_files", create_mock_image_file("image.jpg", "image/jpeg")),
    ]

    # Act
    response = await async_client.post(
        "/v1_0/import-files/medical-devices", files=files_payload
    )

    # Assert: Part 1 - API Response
    assert response.status_code == 400
    response_json = response.json()
    assert response_json["messageCode"] == CustomMessageCode.S3_BUCKET_ERROR.code
    assert response_json["message"] == s3_error_message

    async with async_central_db_session_object:
        query = select(ImportFileLog).order_by(ImportFileLog.created_at.desc())
        result = await async_central_db_session_object.execute(query)
        log_entry = result.scalars().first()
        assert log_entry.status == ImportFileStatus.RECEIVE_ERROR
        assert log_entry.error_message == s3_error_message
