from unittest.mock import patch

import pytest
from core.constants import ClinicRedisKey
from core.messages import CustomMessageCode
from tests.helpers.redis_mock import mock_redis_client


def set_header_to_redis(tenant_slug):
    mock_redis_client.set(
        name=ClinicRedisKey.CLINICS_CACHE.value, value=str([tenant_slug])
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "header_tenant_slug, expected_status, is_redis_set, message, exists",
    [
        (True, 200, True, None, True),
        (
            "invalid-slug",
            400,
            False,
            CustomMessageCode.CLINIC_NOT_FOUND.title,
            False,
        ),
        (
            "",
            400,
            False,
            CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
            False,
        ),
        (True, 200, False, None, True),
    ],
)
async def test_clinic_slug_check(
    async_client,
    tenant_slug,
    header_tenant_slug,
    expected_status,
    is_redis_set,
    message,
    exists,
):
    if is_redis_set:
        set_header_to_redis(tenant_slug)

    headers = {"X-Tenant-Slug": f"{header_tenant_slug}"}

    if isinstance(header_tenant_slug, bool):
        headers = {"X-Tenant-Slug": tenant_slug}
    with patch(
        "gc_dentist_shared.core.common.redis.RedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = await async_client.get("/v1_0/tenants/slugs", headers=headers)
        result = response.json()
        assert response.status_code == expected_status
        assert result.get("message") == message
