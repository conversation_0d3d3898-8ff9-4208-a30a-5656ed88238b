import uuid
from unittest.mock import AsyncMock, patch

import pytest
from app.services.migration_service import process_migrate_tenant_streaming
from core.constants import TenantClinicStatus
from schemas.tenant_clinic_requests import TenantPayloads


@pytest.mark.asyncio
async def test_process_migrate_tenant_streaming_success():
    tenant_uuid = str(uuid.uuid4())
    data = TenantPayloads(
        tenant_name="Clinic A",
        tenant_slug="clinic-a",
        business_number="987654321",
        db_name="clinic_a_db",
        db_uri="postgresql://clinic_a",
        plan_id=1,
    )
    with (
        patch(
            "app.services.migration_service.run_alembic_upgrade_and_capture",
            return_value=["log1\n", "log2\n"],
        ),
        patch("app.services.migration_service.TenantClinicService") as mock_service,
        patch(
            "app.services.migration_service.CentralDatabase.get_instance_db",
            new_callable=AsyncMock,
        ),
    ):
        mock_service = mock_service.return_value
        mock_service.update_tenant_status = AsyncMock()
        result = []
        async for line in process_migrate_tenant_streaming(tenant_uuid, data):
            result.append(line)
        assert result[0].startswith("🚀 Start Migration Database")
        assert "log1" in result[1]
        assert "log2" in result[2]
        mock_service.update_tenant_status.assert_awaited_with(
            tenant_uuid, TenantClinicStatus.SUCCESS
        )


@pytest.mark.asyncio
async def test_process_migrate_tenant_streaming_failure():
    tenant_uuid = str(uuid.uuid4())
    data = TenantPayloads(
        tenant_name="Clinic B",
        tenant_slug="clinic-b",
        business_number="123456789",
        db_name="clinic_b_db",
        db_uri="postgresql://clinic_b",
        plan_id=2,
    )
    with (
        patch(
            "app.services.migration_service.run_alembic_upgrade_and_capture",
            side_effect=Exception("migrate error"),
        ),
        patch("app.services.migration_service.TenantClinicService") as mock_service,
        patch(
            "app.services.migration_service.CentralDatabase.get_instance_db",
            new_callable=AsyncMock,
        ),
    ):
        mock_service = mock_service.return_value
        mock_service.update_tenant_status = AsyncMock()
        result = []
        async for line in process_migrate_tenant_streaming(tenant_uuid, data):
            result.append(line)
        assert result[-1].startswith("❌ Migration DataBase Tenant Error")
        mock_service.update_tenant_status.assert_awaited_with(
            tenant_uuid, TenantClinicStatus.FAILED
        )
