"""add column doctor_user_id

Revision ID: 64c273aaf085
Revises: b8129ecf932a
Create Date: 2025-07-11 17:24:16.243655

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "64c273aaf085"  # pragma: allowlist secret
down_revision: Union[str, None] = "b8129ecf932a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "doctor_profiles",
        sa.Column("doctor_user_id", sa.Integer(), nullable=False),
    )
    op.create_foreign_key(
        None, "doctor_profiles", "doctor_users", ["doctor_user_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "doctor_profiles", type_="foreignkey")
    op.drop_column("doctor_profiles", "doctor_user_id")
    # ### end Alembic commands ###
