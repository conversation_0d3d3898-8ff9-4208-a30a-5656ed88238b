"""update_tbl_document_managements

Revision ID: dd78c47884ae
Revises: 52e63d0cc027
Create Date: 2025-08-20 16:52:06.278497

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy import text

from gc_dentist_shared.core.enums.document import DocumentExtension, DocumentS3Status

# revision identifiers, used by Alembic.
revision: str = "dd78c47884ae"
down_revision: Union[str, None] = "52e63d0cc027"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "document_managements",
        "version_uuid",
        new_column_name="document_uuid",
        existing_type=sa.String(),
        nullable=False,
        comment="UUID represents a group of documents with multiple versions.",
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "version_id",
            sa.Integer(),
            nullable=True,
            server_default="1",
            comment="The version corresponding to a group of documents.",
        ),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "is_latest",
            sa.Boolean(),
            nullable=False,
            server_default="1",
            comment="Represents the latest version",
        ),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "s3_status",
            sa.Integer(),
            nullable=False,
            server_default=text(str(DocumentS3Status.AVAILABLE.value)),
            comment="S3 status",
        ),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "document_extension",
            sa.Integer(),
            nullable=False,
            server_default=text(str(DocumentExtension.PAGE.value)),
            comment="Extension of the document",
        ),
    )

    op.create_unique_constraint(
        "uq_document_version",
        "document_managements",
        ["patient_user_id", "document_uuid", "version_id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("uq_document_version", "document_managements", type_="unique")
    op.alter_column(
        "document_managements",
        "document_uuid",
        new_column_name="version_uuid",
        existing_type=sa.String(),
        nullable=False,
        comment="UUID of the latest version",
    )
    op.drop_column("document_managements", "is_latest")
    op.drop_column("document_managements", "version_id")
    op.drop_column("document_managements", "s3_status")
    op.drop_column("document_managements", "document_extension")
    # ### end Alembic commands ###
