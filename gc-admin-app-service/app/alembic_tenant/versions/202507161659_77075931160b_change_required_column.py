"""change required column

Revision ID: 77075931160b
Revises: 4df871ec9d9d
Create Date: 2025-07-16 16:59:40.903541

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "77075931160b"
down_revision: Union[str, None] = "4df871ec9d9d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "reservation_id",
        existing_type=sa.INTEGER(),
        nullable=True,
        comment="Reservation ID",
    )
    op.alter_column(
        "patient_waitings",
        "visit_start_date",
        existing_type=sa.DATE(),
        nullable=False,
        existing_comment="Visit start date",
    )
    op.alter_column(
        "patient_waitings",
        "visit_start_time",
        existing_type=postgresql.TIME(),
        nullable=False,
        existing_comment="Visit start time",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "visit_start_time",
        existing_type=postgresql.TIME(),
        nullable=True,
        existing_comment="Visit start time",
    )
    op.alter_column(
        "patient_waitings",
        "visit_start_date",
        existing_type=sa.DATE(),
        nullable=True,
        existing_comment="Visit start date",
    )
    op.alter_column(
        "patient_waitings",
        "reservation_id",
        existing_type=sa.INTEGER(),
        nullable=False,
        comment=None,
        existing_comment="Reservation ID",
    )
    # ### end Alembic commands ###
