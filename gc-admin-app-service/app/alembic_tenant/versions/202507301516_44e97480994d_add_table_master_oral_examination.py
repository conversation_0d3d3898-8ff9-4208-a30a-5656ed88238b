"""add table master oral examination

Revision ID: 44e97480994d
Revises: 0866d0b110c8
Create Date: 2025-07-30 15:16:48.249872

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "44e97480994d"
down_revision: Union[str, None] = "0866d0b110c8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_oral_examination_matrix",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "m_oral_examination_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the oral examination in the master table",
        ),
        sa.Column(
            "m_oral_examination_depend_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the dependent oral examination in the master table",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "m_oral_examination_id",
            "m_oral_examination_depend_id",
            name="uq_master_oral_examination_matrix_depend",
        ),
    )

    headers = [
        "健康",
        "欠損",
        "ｼｰﾗﾝﾄ",
        "残根",
        "根面板",
        "C0",
        "C1",
        "C2",
        "C3",
        "C4",
        "CR充",
        "AF",
        "In",
        "In白",
        "InG",
        "On",
        "コア",
        "FMC",
        "CAD",
        "冠白",
        "冠G",
        "前CK",
        "JC",
        "MB",
        "IP",
        "IP義",
        "TEK",
        "Br",
        "Pon",
        "PonMB",
        "Pon白",
        "PonG",
        "WSD",
        "Per",
        "半埋伏",
        "ダツリ",
        "GA",
        'C"',
        "フテキ",
        "全埋伏",
        "ハセツ",
        "治療中",
    ]

    matrix = {
        "健康": "111111111111111111111111111111110000000000",
        "欠損": "111111111111111111111111111111111111111111",
        "ｼｰﾗﾝﾄ": "111111111111111111111111111111110000000000",
        "残根": "111111111111111111111111111111110000000000",
        "根面板": "111111111111111111111111111111110000000000",
        "C0": "111110001100000011111111111111110000000000",
        "C1": "111110001100000011111111111111110000000000",
        "C2": "111110001100000011111111111111110000000000",
        "C3": "111111111111111111111111111111110000000000",
        "C4": "111111111111111111111111111111110000000000",
        "CR充": "111110001100000011111111111111110000000000",
        "AF": "111110001100000011111111111111110000000000",
        "In": "111110001100000011111111111111110000000000",
        "In白": "111110001100000011111111111111110000000000",
        "InG": "111110001100000011111111111111110000000000",
        "On": "111110001100000011111111111111110000000000",
        "コア": "111111111111111111111111111111110000000000",
        "FMC": "111111111111111111111111111111110000000000",
        "CAD": "111111111111111111111111111111110000000000",
        "冠白": "111111111111111111111111111111110000000000",
        "冠G": "111111111111111111111111111111110000000000",
        "前CK": "111111111111111111111111111111110000000000",
        "JC": "111111111111111111111111111111110000000000",
        "MB": "111111111111111111111111111111110000000000",
        "IP": "111111111111111111111111111111110000000000",
        "IP義": "111111111111111111111111111111110000000000",
        "TEK": "111111111111111111111111111111110000000000",
        "Br": "111111111111111111111111111111110000000000",
        "Pon": "111111111111111111111111111111110000000000",
        "PonMB": "111111111111111111111111111111110000000000",
        "Pon白": "111111111111111111111111111111110000000000",
        "PonG": "111111111111111111111111111111110000000000",
        "WSD": "010000000000000000000000000000001000000000",
        "Per": "010000000000000000000000000000000100000000",
        "半埋伏": "010000000000000000000000000000000010000000",
        "ダツリ": "010000000000000000000000000000000001000000",
        "GA": "010000000000000000000000000000000000100000",
        'C"': "010000000000000000000000000000000000010000",
        "フテキ": "010000000000000000000000000000000000001000",
        "全埋伏": "010000000000000000000000000000000000000100",
        "ハセツ": "010000000000000000000000000000000000000010",
        "治療中": "010000000000000000000000000000000000000001",
    }

    # get id headers from master oral examination
    conn = op.get_bind()
    metadata = sa.MetaData()
    m_oral_examinations = sa.Table(
        "m_oral_examinations", metadata, autoload_with=op.get_bind()
    )
    query = sa.select(m_oral_examinations.c.name_jp, m_oral_examinations.c.id).where(
        m_oral_examinations.c.name_jp.in_(headers),
        ~m_oral_examinations.c.is_hidden,
    )

    results = conn.execute(query)
    results = results.fetchall()
    header_ids = {row.name_jp: row.id for row in results}

    # insert data into m_oral_examination_matrix
    insert_data = []
    for header, row in matrix.items():
        if header not in header_ids:
            continue
        for i, value in enumerate(row):
            if value == "1":
                insert_data.append(
                    {
                        "m_oral_examination_id": header_ids[header],
                        "m_oral_examination_depend_id": header_ids[headers[i]],
                    }
                )

    if insert_data:
        metadata = sa.MetaData()
        m_oral_examinations = sa.Table(
            "m_oral_examination_matrix", metadata, autoload_with=op.get_bind()
        )
        op.bulk_insert(m_oral_examinations, insert_data)

    # Add initial data to the table

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("m_oral_examination_matrix")
    # ### end Alembic commands ###
