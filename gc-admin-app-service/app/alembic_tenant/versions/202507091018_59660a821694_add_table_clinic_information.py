"""add table clinic information

Revision ID: 59660a821694
Revises: 3bd9fcc572c5
Create Date: 2025-07-09 10:18:58.156278

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "59660a821694"  # pragma: allowlist secret
down_revision: Union[str, None] = "3bd9fcc572c5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "clinic_informations",
        sa.Column("clinic_uuid", sa.String(), nullable=False),
        sa.Column("clinic_name", sa.String(), nullable=False),
        sa.Column("clinic_slug", sa.String(), nullable=False),
        sa.Column("phone_number", sa.String(), nullable=False),
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("address_1", sa.String(), nullable=False),
        sa.Column("address_2", sa.String(), nullable=False),
        sa.Column("address_3", sa.String(), nullable=False),
        sa.Column("latitude", sa.String(), nullable=True),
        sa.Column("longitude", sa.String(), nullable=True),
        sa.Column("logo_url", sa.String(), nullable=True),
        sa.Column("opening_hours", sa.JSON(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("clinic_uuid"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("clinic_informations")
    # ### end Alembic commands ###
