"""init_medical_notes_and_template_table

Revision ID: d69b8f12196b
Revises: 89cc49e13045
Create Date: 2025-08-07 17:59:59.517505

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "d69b8f12196b"  # pragma: allowlist secret
down_revision: Union[str, None] = "89cc49e13045"  # pragma: allowlist secret
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_templates",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column(
            "page_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="Data of the page",
        ),
        sa.Column(
            "status", sa.Integer(), nullable=False, comment="Status of the template"
        ),
        sa.Column("size", sa.Integer(), nullable=True, comment="Size of the template"),
        sa.Column(
            "label", sa.Integer(), nullable=True, comment="Label of the template"
        ),
        sa.Column("tag", sa.Integer(), nullable=True, comment="Tag of the template"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who updated the record",
        ),
        sa.Column(
            "deleted_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who deleted the record",
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "medical_notes",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False, comment="Name of the note"),
        sa.Column(
            "data_note",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="Data of the note",
        ),
        sa.Column("file_url", sa.String(), nullable=True, comment="URL of the file"),
        sa.Column(
            "file_export_url",
            sa.String(),
            nullable=True,
            comment="URL of the exported file",
        ),
        sa.Column(
            "file_export_mapping",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Mapping of the exported file",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who updated the record",
        ),
        sa.Column(
            "deleted_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who deleted the record",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("medical_notes")
    op.drop_table("medical_templates")
    # ### end Alembic commands ###
