"""add_city_type_in_postal_code_table

Revision ID: c148fe8097b1
Revises: 5ad28de2458e
Create Date: 2025-07-08 17:22:50.922175

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c148fe8097b1"  # pragma: allowlist secret
down_revision: Union[str, None] = "5ad28de2458e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "m_postal_code",
        sa.Column("city_type", sa.String(length=32), nullable=True),
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("m_postal_code", "city_type")
    # ### end Alembic commands ###
