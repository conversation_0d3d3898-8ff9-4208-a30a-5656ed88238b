"""patient user add column clinical_no

Revision ID: e7ed1e3751a6
Revises: b68f0bf0a2fa
Create Date: 2025-06-27 10:49:01.696541

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e7ed1e3751a6"
down_revision: Union[str, None] = "b68f0bf0a2fa"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_users", sa.Column("clinical_no", sa.String(), nullable=False)
    )
    op.create_unique_constraint(None, "patient_users", ["clinical_no"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "patient_users", type_="unique")
    op.drop_column("patient_users", "clinical_no")
    # ### end Alembic commands ###
