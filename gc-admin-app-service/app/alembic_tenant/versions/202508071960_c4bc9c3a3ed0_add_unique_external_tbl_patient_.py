"""add_unique_external_tbl_patient_reservation

Revision ID: c4bc9c3a3ed0
Revises: d69b8f12196b
Create Date: 2025-08-08 22:34:45.778134

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c4bc9c3a3ed0"  # pragma: allowlist secret
down_revision: Union[str, None] = "d69b8f12196b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "uq_patient_user_id", "patient_profiles", ["patient_user_id"]
    )
    op.add_column(
        "patient_users", sa.Column("external_patient_id", sa.String(), nullable=True)
    )
    op.add_column(
        "patient_users",
        sa.Column("source", sa.String(), nullable=True, comment="System name"),
    )
    op.create_unique_constraint(
        "uq_external_patient_source", "patient_users", ["external_patient_id", "source"]
    )
    op.drop_column("patient_users", "iapo_patient_id")
    op.create_unique_constraint(
        "uq_reservation_code_source",
        "reservations",
        ["external_reservation_code", "source"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("uq_reservation_code_source", "reservations", type_="unique")
    op.add_column(
        "patient_users",
        sa.Column(
            "iapo_patient_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="ID from IAPO system",
        ),
    )
    op.drop_constraint("uq_external_patient_source", "patient_users", type_="unique")
    op.drop_column("patient_users", "source")
    op.drop_column("patient_users", "external_patient_id")
    op.drop_constraint("uq_patient_user_id", "patient_profiles", type_="unique")
    # ### end Alembic commands ###
