"""init form flow

Revision ID: 5f91735adcb5
Revises: 3e800f53f3f8
Create Date: 2025-07-22 11:08:24.492367

"""

from datetime import datetime, timezone
from typing import Sequence, Union
from uuid import uuid4

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5f91735adcb5"  # pragma: allowlist secret
down_revision: Union[str, None] = "3e800f53f3f8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    conn = op.get_bind()
    now = datetime.now(timezone.utc)

    form_flow_uuid = str(uuid4())
    flow_name = "大人用問診票"

    form_uuid = str(uuid4())
    form_name = "基本情報 ページ"

    group_uuid = str(uuid4())
    title_group = "個人情報"

    # Insert form_flows
    conn.execute(
        sa.text(
            """
            INSERT INTO form_flows (uuid, flow_name, flow_type, version, is_active, is_deletable, created_at)
            VALUES (:uuid, :flow_name, :flow_type, :version, TRUE, TRUE, :created_at)
        """
        ),
        {
            "uuid": form_flow_uuid,
            "flow_name": flow_name,
            "flow_type": 1,
            "version": "1.0",
            "created_at": now,
        },
    )

    # Insert forms
    conn.execute(
        sa.text(
            """
            INSERT INTO forms (uuid, form_flow_uuid, form_name, order_index, is_active, is_deletable, created_at)
            VALUES (:uuid, :form_flow_uuid, :form_name, :order_index, TRUE, TRUE, :created_at)
        """
        ),
        {
            "uuid": form_uuid,
            "form_flow_uuid": form_flow_uuid,
            "form_name": form_name,
            "order_index": 0,
            "created_at": now,
        },
    )

    # Insert group
    conn.execute(
        sa.text(
            """
            INSERT INTO form_item_groups (uuid, form_uuid, title, order_index, is_active, is_deletable, created_at)
            VALUES (:uuid, :form_uuid, :title, :order_index, TRUE, TRUE, :created_at)
        """
        ),
        {
            "uuid": group_uuid,
            "form_uuid": form_uuid,
            "title": title_group,
            "order_index": 0,
            "created_at": now,
        },
    )

    # Insert items
    item_values = [
        {
            "uuid": str(uuid4()),
            "form_uuid": form_uuid,
            "group_uuid": group_uuid,
            "label": label,
            "field_type": field_type,
            "required": required,
            "order_index": order_index,
            "created_at": now,
        }
        for label, field_type, required, order_index in [
            ("お名前", "text", True, 0),
            ("フリガナ", "text", False, 1),
            ("携帯番号", "text", True, 2),
            ("生年月日", "text", True, 3),
            ("性別", "number", True, 4),
        ]
    ]

    conn.execute(
        sa.text(
            """
            INSERT INTO form_items (
                uuid, form_uuid, form_item_group_id, label, field_type, item_side,
                required, is_favorite, is_deletable, order_index,
                is_active, created_at
            ) VALUES (
                :uuid, :form_uuid, :group_uuid, :label, :field_type, 'doctor',
                :required, FALSE, TRUE, :order_index,
                TRUE, :created_at
            )
        """
        ),
        item_values,
    )


def downgrade() -> None:
    """Downgrade schema."""
