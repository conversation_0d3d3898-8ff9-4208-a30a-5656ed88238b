"""add column country code

Revision ID: 441255d2a029
Revises: 828e6db19ae0
Create Date: 2025-07-21 09:49:31.667551

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "441255d2a029"
down_revision: Union[str, None] = "828e6db19ae0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_profiles", sa.Column("country_code", sa.String(), nullable=True)
    )
    op.create_unique_constraint(
        "uq_patient_profiles_phone", "patient_profiles", ["phone"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("patient_profiles", "country_code")
    op.drop_constraint("uq_patient_profiles_phone", "patient_profiles", type_="unique")
    # ### end Alembic commands ###
