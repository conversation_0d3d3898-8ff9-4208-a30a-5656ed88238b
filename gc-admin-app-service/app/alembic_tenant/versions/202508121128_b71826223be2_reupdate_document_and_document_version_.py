"""Reupdate Document And Document Version Model

Revision ID: b71826223be2
Revises: eb3ee6c5ca35
Create Date: 2025-08-12 11:28:34.197621

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "b71826223be2"
down_revision: Union[str, None] = "eb3ee6c5ca35"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_management_versions",
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
    )
    op.add_column(
        "document_management_versions",
        sa.Column(
            "version_uuid", sa.String(), nullable=False, comment="UUID of the version"
        ),
    )
    op.create_foreign_key(
        None,
        "document_management_versions",
        "patient_users",
        ["patient_user_id"],
        ["id"],
    )
    op.add_column(
        "document_managements",
        sa.Column("document_group_id", sa.Integer(), nullable=True),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "display_mode",
            sa.Integer(),
            nullable=False,
            comment="Display mode of the document. Enums: DocumentDisplayMode",
        ),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "version_uuid",
            sa.String(),
            nullable=False,
            comment="UUID of the latest version",
        ),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "extra_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Extra data of the document",
        ),
    )
    op.create_foreign_key(
        None, "document_managements", "document_group", ["document_group_id"], ["id"]
    )
    op.drop_column("document_managements", "source")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_managements",
        sa.Column(
            "source",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Source of the document. Enums: DocumentSource",
        ),
    )
    op.drop_constraint(None, "document_managements", type_="foreignkey")
    op.drop_column("document_managements", "extra_data")
    op.drop_column("document_managements", "version_uuid")
    op.drop_column("document_managements", "display_mode")
    op.drop_column("document_managements", "document_group_id")
    op.drop_constraint(None, "document_management_versions", type_="foreignkey")
    op.drop_column("document_management_versions", "version_uuid")
    op.drop_column("document_management_versions", "patient_user_id")
    # ### end Alembic commands ###
