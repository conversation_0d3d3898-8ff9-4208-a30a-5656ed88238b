"""init table user

Revision ID: b68f0bf0a2fa
Revises: None
Create Date: 2025-06-26 17:08:14.480684

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "b68f0bf0a2fa"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "doctor_profiles",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("first_name", sa.String(length=255), nullable=False),
        sa.Column("last_name", sa.String(length=255), nullable=False),
        sa.Column(
            "postal_code",
            sa.String(length=255),
            nullable=True,
            comment="Data encrypted, ensure original length max 8",
        ),
        sa.Column(
            "address_1",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
        sa.Column(
            "address_2",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
        sa.Column(
            "address_3",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
        sa.Column(
            "birthday",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 10",
        ),
        sa.Column(
            "birthday_hash",
            sa.String(),
            nullable=True,
            comment="Birthday hash, sha-256",
        ),
        sa.Column("gender_id", sa.Integer(), nullable=True, comment="Gender id"),
        sa.Column("country_code", sa.String(length=10), nullable=True),
        sa.Column("phone", sa.Integer(), nullable=True),
        sa.Column(
            "phone_hash",
            sa.String(),
            nullable=True,
            comment="Phone hash, sha-256",
        ),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column(
            "email_hash",
            sa.String(),
            nullable=True,
            comment="Email hash, sha-256",
        ),
        sa.Column("avatar_url", sa.String(), nullable=True),
        sa.Column("extra_data", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "doctor_users",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("uuid", sa.String(), nullable=True),
        sa.Column("username", sa.String(), nullable=False),
        sa.Column("password", sa.String(), nullable=True),
        sa.Column("login_failed", sa.Integer(), nullable=False),
        sa.Column("required_change_password", sa.Boolean(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("last_login", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("username"),
    )
    op.create_table(
        "patient_profiles",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("first_name", sa.String(length=255), nullable=False),
        sa.Column("last_name", sa.String(length=255), nullable=False),
        sa.Column(
            "postal_code",
            sa.String(length=255),
            nullable=True,
            comment="Data encrypted, ensure original length max 8",
        ),
        sa.Column(
            "address_1",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
        sa.Column(
            "address_2",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
        sa.Column(
            "address_3",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
        sa.Column(
            "birthday",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 10",
        ),
        sa.Column(
            "birthday_hash",
            sa.String(),
            nullable=True,
            comment="Birthday hash, sha-256",
        ),
        sa.Column("gender_id", sa.Integer(), nullable=True, comment="Gender id"),
        sa.Column("country_code", sa.String(length=10), nullable=True),
        sa.Column("phone", sa.Integer(), nullable=True),
        sa.Column(
            "phone_hash",
            sa.String(),
            nullable=True,
            comment="Phone hash, sha-256",
        ),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column(
            "email_hash",
            sa.String(),
            nullable=True,
            comment="Email hash, sha-256",
        ),
        sa.Column("avatar_url", sa.String(), nullable=True),
        sa.Column("extra_data", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "patient_users",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("uuid", sa.String(), nullable=True),
        sa.Column("username", sa.String(), nullable=False),
        sa.Column("password", sa.String(), nullable=True),
        sa.Column("login_failed", sa.Integer(), nullable=False),
        sa.Column("required_change_password", sa.Boolean(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("last_login", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("username"),
    )
    op.create_table(
        "roles",
        sa.Column(
            "role_scope",
            sa.Integer(),
            nullable=False,
            comment="1: doctor, 2: patient",
        ),
        sa.Column("level", sa.Integer(), nullable=True, comment="Level Of Role"),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "name_json",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Master name",
        ),
        sa.Column("sort", sa.Integer(), nullable=True, comment="Sort"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("sort"),
    )
    op.create_table(
        "tenant_configuration",
        sa.Column("tenant_id", sa.String(), nullable=False),
        sa.Column("tenant_name", sa.String(), nullable=False),
        sa.Column("tenant_slug", sa.String(), nullable=False),
        sa.Column("plan_id", sa.Integer(), nullable=False),
        sa.Column("config", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("tenant_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("tenant_configuration")
    op.drop_table("roles")
    op.drop_table("patient_users")
    op.drop_table("patient_profiles")
    op.drop_table("doctor_users")
    op.drop_table("doctor_profiles")
    # ### end Alembic commands ###
