"""oral examination change type column

Revision ID: 0ac331d584a0
Revises: 5f91735adcb5
Create Date: 2025-07-22 13:55:45.457988

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "0ac331d584a0"  # pragma: allowlist secret
down_revision: Union[str, None] = "5f91735adcb5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "oral_examinations",
        sa.Column(
            "intraoral_examination",
            sa.JSON(),
            nullable=True,
            comment="Details of the oral examination",
        ),
    )
    op.alter_column(
        "oral_examinations",
        "tooth_type",
        existing_type=sa.INTEGER(),
        type_=sa.String(),
        existing_comment="Type of tooth examined",
        existing_nullable=True,
    )
    op.drop_column("oral_examinations", "oral_examination")
    op.alter_column(
        "patient_profiles",
        "phone",
        existing_type=sa.VARCHAR(),
        comment="Data encrypted, original value must be unique and max 255",
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_profiles",
        "phone",
        existing_type=sa.VARCHAR(),
        comment="Data encrypted, ensure original length max 255",
        existing_comment="Data encrypted, original value must be unique and max 255",
        existing_nullable=True,
    )
    op.add_column(
        "oral_examinations",
        sa.Column(
            "oral_examination",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="Details of the oral examination",
        ),
    )
    op.alter_column(
        "oral_examinations",
        "tooth_type",
        existing_type=sa.String(),
        type_=sa.INTEGER(),
        existing_comment="Type of tooth examined",
        existing_nullable=True,
    )
    op.drop_column("oral_examinations", "intraoral_examination")
    # ### end Alembic commands ###
