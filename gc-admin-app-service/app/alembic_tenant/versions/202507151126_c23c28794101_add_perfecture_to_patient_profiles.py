"""add_perfecture_to_patient_profiles

Revision ID: c23c28794101
Revises: 64c273aaf085
Create Date: 2025-07-10 14:38:18.645144

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c23c28794101"  # pragma: allowlist secret
down_revision: Union[str, None] = "64c273aaf085"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_profiles", sa.Column("prefecture_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        None, "patient_profiles", "m_prefecture", ["prefecture_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "patient_profiles", type_="foreignkey")
    op.drop_column("patient_profiles", "prefecture_id")
    # ### end Alembic commands ###
