"""Init document_management model and remove medical note

Revision ID: 61f852ffcc41
Revises: c4bc9c3a3ed0
Create Date: 2025-08-08 17:59:34.588215

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "61f852ffcc41"  # pragma: allowlist secret
down_revision: Union[str, None] = "c4bc9c3a3ed0"  # pragma: allowlist secret
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_managements",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False, comment="Name of the document"),
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            comment="Status of the document. Enums: DocumentStatus",
        ),
        sa.Column(
            "source",
            sa.Integer(),
            nullable=False,
            comment="Source of the document. Enums: DocumentSource",
        ),
        sa.Column(
            "data_type",
            sa.Integer(),
            nullable=False,
            comment="Type of the document. Enums: DocumentDataType",
        ),
        sa.Column(
            "document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="Data of the document",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=True,
            comment="Date of the examination/measurement",
        ),
        sa.Column("medical_history_id", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who updated the record",
        ),
        sa.Column(
            "deleted_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who deleted the record",
        ),
        sa.ForeignKeyConstraint(
            ["medical_history_id"],
            ["medical_histories.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "document_management_versions",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("document_management_id", sa.Integer(), nullable=False),
        sa.Column(
            "document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="Data of the document",
        ),
        sa.Column(
            "version", sa.Integer(), nullable=False, comment="Version of the document"
        ),
        sa.Column(
            "data_type",
            sa.Integer(),
            nullable=False,
            comment="Type of the document. Enums: DocumentType",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who updated the record",
        ),
        sa.Column(
            "deleted_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who deleted the record",
        ),
        sa.ForeignKeyConstraint(
            ["document_management_id"],
            ["document_managements.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "document_management_id",
            "version",
            name="uq_document_management_versions_document_management_id_version",
        ),
    )
    op.drop_table("medical_notes")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_notes",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "name",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Name of the note",
        ),
        sa.Column(
            "data_note",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
            comment="Data of the note",
        ),
        sa.Column(
            "file_url",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="URL of the file",
        ),
        sa.Column(
            "file_export_url",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="URL of the exported file",
        ),
        sa.Column(
            "file_export_mapping",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="Mapping of the exported file",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "created_by",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the user who updated the record",
        ),
        sa.Column(
            "deleted_by",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the user who deleted the record",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="medical_notes_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_notes_pkey"),
    )
    op.drop_table("document_management_versions")
    op.drop_table("document_managements")
    # ### end Alembic commands ###
