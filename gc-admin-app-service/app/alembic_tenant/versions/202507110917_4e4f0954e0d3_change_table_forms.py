"""change table forms

Revision ID: 4e4f0954e0d3
Revises: 59660a821694
Create Date: 2025-07-11 09:17:15.193387

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4e4f0954e0d3"
down_revision: Union[str, None] = "59660a821694"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "form_flows",
        sa.Column(
            "is_deletable",
            sa.<PERSON>(),
            nullable=False,
            comment="Allow delete Form Flows",
        ),
    )
    op.add_column(
        "form_item_groups",
        sa.Column(
            "is_deletable",
            sa.<PERSON>(),
            nullable=False,
            comment="Allow delete Form Item Groups",
        ),
    )
    op.drop_constraint(
        "form_item_groups_form_uuid_fkey",
        "form_item_groups",
        type_="foreignkey",
    )
    op.drop_column("form_item_groups", "form_uuid")
    op.add_column(
        "form_items",
        sa.Column(
            "is_deletable",
            sa.Boolean(),
            nullable=False,
            comment="Allow delete Form item",
        ),
    )
    op.alter_column(
        "form_items",
        "item_side",
        existing_type=sa.VARCHAR(),
        nullable=True,
        existing_comment="User role responsible for inputting this item (e.g., doctor, patient, admin)",
    )
    op.drop_constraint(
        "form_submissions_form_flow_uuid_fkey",
        "form_submissions",
        type_="foreignkey",
    )
    op.create_foreign_key(
        None, "form_submissions", "form_flows", ["form_flow_uuid"], ["uuid"]
    )
    op.add_column(
        "forms",
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether this Form is active",
        ),
    )
    op.add_column(
        "forms",
        sa.Column(
            "is_deletable",
            sa.Boolean(),
            nullable=False,
            comment="Allow delete Form",
        ),
    )
    op.drop_constraint("forms_form_flow_uuid_fkey", "forms", type_="foreignkey")
    op.create_foreign_key(None, "forms", "form_flows", ["form_flow_uuid"], ["uuid"])
    op.drop_column("forms", "delete_flag")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "forms",
        sa.Column(
            "delete_flag",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=False,
            comment="Form is deleted",
        ),
    )
    op.drop_constraint(None, "forms", type_="foreignkey")
    op.create_foreign_key(
        "forms_form_flow_uuid_fkey",
        "forms",
        "forms",
        ["form_flow_uuid"],
        ["uuid"],
    )
    op.drop_column("forms", "is_deletable")
    op.drop_column("forms", "is_active")
    op.drop_constraint(None, "form_submissions", type_="foreignkey")
    op.create_foreign_key(
        "form_submissions_form_flow_uuid_fkey",
        "form_submissions",
        "forms",
        ["form_flow_uuid"],
        ["uuid"],
    )
    op.alter_column(
        "form_items",
        "item_side",
        existing_type=sa.VARCHAR(),
        nullable=False,
        existing_comment="User role responsible for inputting this item (e.g., doctor, patient, admin)",
    )
    op.drop_column("form_items", "is_deletable")
    op.add_column(
        "form_item_groups",
        sa.Column(
            "form_uuid",
            sa.UUID(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key referencing the parent form",
        ),
    )
    op.create_foreign_key(
        "form_item_groups_form_uuid_fkey",
        "form_item_groups",
        "forms",
        ["form_uuid"],
        ["uuid"],
    )
    op.drop_column("form_item_groups", "is_deletable")
    op.drop_column("form_flows", "is_deletable")
    # ### end Alembic commands ###
