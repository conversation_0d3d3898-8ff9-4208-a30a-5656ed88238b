"""init table for relationship

Revision ID: b462bb1196a6
Revises: 0aea1bc6b773
Create Date: 2025-07-30 18:01:01.522009

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b462bb1196a6"
down_revision: Union[str, None] = "44e97480994d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "patient_relationship_requests",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("requester_user_id", sa.Integer(), nullable=False),
        sa.Column("target_user_id", sa.Integer(), nullable=False),
        sa.Column("relationship", sa.Integer(), nullable=False),
        sa.Column("approved_role", sa.Integer(), nullable=True),
        sa.Column("approved_by_id", sa.Integer(), nullable=True),
        sa.Column("status", sa.Integer(), nullable=False),
        sa.Column("request_count", sa.Integer(), nullable=False),
        sa.Column("updated_by", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["requester_user_id"],
            ["patient_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["target_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "requester_user_id",
            "target_user_id",
            name="uq_patient_relationship_requests",
        ),
    )
    op.create_table(
        "patient_user_relationship",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("sub_patient_user_id", sa.Integer(), nullable=False),
        sa.Column("relationship", sa.Integer(), nullable=False),
        sa.Column("request_id", sa.Integer(), nullable=False),
        sa.Column("comment", sa.String(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("created_by", sa.Integer(), nullable=False),
        sa.Column("updated_by", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["request_id"],
            ["patient_relationship_requests.id"],
        ),
        sa.ForeignKeyConstraint(
            ["sub_patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("patient_user_relationship")
    op.drop_table("patient_relationship_requests")
    # ### end Alembic commands ###
