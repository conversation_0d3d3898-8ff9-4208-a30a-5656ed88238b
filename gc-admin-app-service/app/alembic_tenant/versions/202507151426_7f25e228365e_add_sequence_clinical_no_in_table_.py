"""add_sequence_clinical_no_in_table_patient_user

Revision ID: 7f25e228365e
Revises: c23c28794101
Create Date: 2025-07-15 14:26:02.631510

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "7f25e228365e"
down_revision: Union[str, None] = "c23c28794101"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "CREATE SEQUENCE IF NOT EXISTS patient_users_clinical_no_seq START 1 INCREMENT 1"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.execute("DROP SEQUENCE IF EXISTS patient_users_clinical_no_seq")
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
