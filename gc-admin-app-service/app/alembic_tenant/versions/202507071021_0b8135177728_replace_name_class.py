"""replace-name-class

Revision ID: 0b8135177728
Revises: 6516888e8b8b
Create Date: 2025-07-07 10:21:48.224269

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0b8135177728"
down_revision: Union[str, None] = "6516888e8b8b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "status",
        existing_type=sa.INTEGER(),
        comment="",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "status",
        existing_type=sa.INTEGER(),
        comment=None,
        existing_comment="",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
