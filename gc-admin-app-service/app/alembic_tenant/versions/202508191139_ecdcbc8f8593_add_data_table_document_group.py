"""add data table document group

Revision ID: ecdcbc8f8593
Revises: 982def5cfdcf
Create Date: 2025-08-19 11:39:17.918864

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ecdcbc8f8593"  # pragma: allowlist secret
down_revision: Union[str, None] = "982def5cfdcf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
        SELECT SETVAL('document_group_id_seq', COALESCE((SELECT MAX(id) FROM document_group), 0) + 1, false);
        """
    )
    op.execute(
        """
        INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
        VALUES ('Treatment Record', TRUE, NULL, 'treatment_record');
        INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
        VALUES ('Medical Questionnaire', TRUE, NULL, 'medical_questionnaire');
        INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
        VALUES ('Periodontal Exam', TRUE, NULL, 'periodontal_exam');
        INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
        VALUES ('Patient Provided Materials', TRUE, NULL, 'patient_provided_materials');
        INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
        VALUES ('Image Layout', TRUE, NULL, 'img_layout');
        INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
        VALUES ('Other', TRUE, NULL, 'other');
    """
    )
    op.execute(
        """
        DO $$
        DECLARE med_device_id INT;
        BEGIN
            INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
            VALUES ('Medical Device', TRUE, NULL, 'medical_device')
            RETURNING id INTO med_device_id;

            INSERT INTO document_group (name, is_parent, group_parent_id, key_name)
            VALUES ('Bite Force Analyzer', FALSE, med_device_id, 'bite_force_analyzer'),
                ('Bite Eye', FALSE, med_device_id, 'bite_eye'),
                ('Wearable Electromyograph', FALSE, med_device_id, 'wearable_electromyograph'),
                ('Motion Trainer', FALSE, med_device_id, 'motion_trainer');
        END $$;
    """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.execute(
        """
        DELETE FROM document_group
        WHERE key_name IN (
            'treatment_record',
            'medical_questionnaire',
            'periodontal_exam',
            'patient_provided_materials',
            'img_layout',
            'other',
            'medical_device',
            'bite_force_analyzer',
            'bite_eye',
            'wearable_electromyograph',
            'motion_trainer'
        );
        """
    )
    # ### end Alembic commands ###
