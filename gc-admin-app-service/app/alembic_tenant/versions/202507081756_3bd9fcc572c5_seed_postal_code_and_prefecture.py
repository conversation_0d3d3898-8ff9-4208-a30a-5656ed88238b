"""run seed postal code and prefecture

Revision ID: 3bd9fcc572c5
Revises: c148fe8097b1
Create Date: 2025-07-08 17:56:13.983185

"""

from pathlib import Path
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3bd9fcc572c5"  # pragma: allowlist secret
down_revision: Union[str, None] = "c148fe8097b1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    raw_conn = conn.connection

    base_dir_alembic = Path(__file__).resolve().parent.parent
    # Import postal code data

    postal_code_file_path = base_dir_alembic / "master_csv/m_postal_code.csv"
    with open(postal_code_file_path) as f:
        with raw_conn.cursor() as cur:
            cur.copy_expert(
                """
                COPY m_postal_code (id, postal_code, pref_name, pref_type, pref_code,
                            city_name, city_type, city_code,
                            town, del_flag, created_at, updated_at)
                FROM STDIN WITH CSV HEADER
            """,
                f,
            )

    # Import prefecture data
    prefecture_file_path = base_dir_alembic / "master_csv/m_prefecture.csv"
    with open(prefecture_file_path) as f:
        with raw_conn.cursor() as cur:
            cur.copy_expert(
                """
                COPY m_prefecture (id, name_json, sort, del_flag, created_at, updated_at)
                FROM STDIN WITH CSV HEADER
            """,
                f,
            )

    raw_conn.commit()
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("TRUNCATE TABLE m_prefecture CASCADE RESTART IDENTITY CASCADE")
    op.execute("TRUNCATE TABLE m_postal_code CASCADE RESTART IDENTITY CASCADE")
    # ### end Alembic commands ###
