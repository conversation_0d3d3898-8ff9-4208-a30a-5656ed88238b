"""change_column_clinical_no_to_patient_no_tbl_patient_users

Revision ID: 89cc49e13045
Revises: 7dbebc341e2e
Create Date: 2025-08-07 11:38:51.671049

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "89cc49e13045"  # pragma: allowlist secret
down_revision: Union[str, None] = "7dbebc341e2e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("patient_users", "clinical_no", new_column_name="patient_no")
    op.drop_constraint("patient_users_clinical_no_key", "patient_users", type_="unique")
    op.create_unique_constraint(
        "patient_users_patient_no_key", "patient_users", ["patient_no"]
    )

    op.execute(
        "ALTER SEQUENCE patient_users_clinical_no_seq RENAME TO patient_users_patient_no_seq"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("patient_users", "patient_no", new_column_name="clinical_no")
    op.drop_constraint("patient_users_patient_no_key", "patient_users", type_="unique")
    op.create_unique_constraint(
        "patient_users_clinical_no_key", "patient_users", ["clinical_no"]
    )

    op.execute(
        "ALTER SEQUENCE patient_users_patient_no_seq RENAME TO patient_users_clinical_no_seq"
    )
    # ### end Alembic commands ###
