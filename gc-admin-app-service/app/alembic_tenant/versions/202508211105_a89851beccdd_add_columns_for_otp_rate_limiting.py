"""Add columns for otp rate limiting

Revision ID: a89851beccdd
Revises: 4924b324f127
Create Date: 2025-08-21 11:05:28.496951

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a89851beccdd"  # pragma: allowlist secret
down_revision: Union[str, None] = "4924b324f127"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "doctor_users",
        sa.Column("otp_first_send_at", sa.TIMESTAMP(timezone=True), nullable=True),
    )
    op.add_column(
        "doctor_users",
        sa.Column("otp_lock_expires_at", sa.TIMESTAMP(timezone=True), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("doctor_users", "otp_lock_expires_at")
    op.drop_column("doctor_users", "otp_first_send_at")
    # ### end Alembic commands ###
