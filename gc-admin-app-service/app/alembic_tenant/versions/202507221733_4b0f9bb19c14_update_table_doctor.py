"""update_table_doctor

Revision ID: 4b0f9bb19c14
Revises: 0ac331d584a0
Create Date: 2025-07-22 17:33:56.871069

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "4b0f9bb19c14"  # pragma: allowlist secret
down_revision: Union[str, None] = "0ac331d584a0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "doctor_profiles",
        sa.Column("first_name_kana", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column("last_name_kana", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "date_of_birth", sa.String(), nullable=False, comment="Date of birth"
        ),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "date_of_birth_hash",
            sa.String(),
            nullable=True,
            comment="Date of birth hash, sha-256",
        ),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "order_index",
            sa.Integer(),
            server_default=sa.text("0"),
            nullable=False,
            comment="Order index of doctor",
        ),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "gender",
            sa.Integer(),
            server_default=sa.text("1"),
            nullable=False,
            comment="1:Male, 2:Female",
        ),
    )
    op.add_column(
        "doctor_profiles", sa.Column("prefecture_id", sa.Integer(), nullable=True)
    )
    op.add_column(
        "doctor_profiles", sa.Column("created_by", sa.Integer(), nullable=True)
    )
    op.add_column(
        "doctor_profiles", sa.Column("updated_by", sa.Integer(), nullable=True)
    )
    op.alter_column(
        "doctor_profiles",
        "address_1",
        existing_type=sa.VARCHAR(),
        comment=None,
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "address_2",
        existing_type=sa.VARCHAR(),
        comment=None,
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "address_3",
        existing_type=sa.VARCHAR(),
        comment=None,
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles", "phone", existing_type=sa.VARCHAR(), nullable=False
    )
    op.alter_column(
        "doctor_profiles",
        "postal_code",
        existing_type=sa.VARCHAR(length=255),
        type_=sa.String(length=8),
        comment="Post code, ensure original length max 8",
        existing_comment="Data encrypted, ensure original length max 8",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "country_code",
        existing_type=sa.VARCHAR(length=10),
        type_=sa.String(length=3),
        comment="Country code, ensure original length max 10",
        existing_nullable=True,
    )
    op.create_unique_constraint(
        "uq_doctor_profiles_email", "doctor_profiles", ["email"]
    )
    op.create_unique_constraint(
        "uq_doctor_profiles_phone", "doctor_profiles", ["phone"]
    )
    op.create_foreign_key(
        None, "doctor_profiles", "m_prefecture", ["prefecture_id"], ["id"]
    )
    op.drop_column("doctor_profiles", "birthday")
    op.drop_column("doctor_profiles", "birthday_hash")
    op.drop_column("doctor_profiles", "extra_data")
    op.drop_column("doctor_profiles", "gender_id")
    op.drop_column("doctor_profiles", "avatar_url")
    op.add_column(
        "doctor_users",
        sa.Column(
            "login_success", sa.Integer(), server_default=sa.text("0"), nullable=False
        ),
    )
    op.add_column(
        "doctor_users",
        sa.Column(
            "status", sa.Boolean(), server_default=sa.text("true"), nullable=False
        ),
    )
    op.add_column(
        "doctor_users",
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
    )
    op.add_column(
        "doctor_users",
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who last updated the record",
        ),
    )
    op.alter_column(
        "doctor_users", "password", existing_type=sa.VARCHAR(), nullable=False
    )
    op.drop_column("doctor_users", "is_active")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "doctor_users",
        sa.Column("is_active", sa.BOOLEAN(), autoincrement=False, nullable=False),
    )
    op.alter_column(
        "doctor_users", "password", existing_type=sa.VARCHAR(), nullable=True
    )
    op.drop_column("doctor_users", "updated_by")
    op.drop_column("doctor_users", "created_by")
    op.drop_column("doctor_users", "status")
    op.drop_column("doctor_users", "login_success")
    op.add_column(
        "doctor_profiles",
        sa.Column("avatar_url", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "gender_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Gender id",
        ),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "extra_data",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "birthday_hash",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Birthday hash, sha-256",
        ),
    )
    op.add_column(
        "doctor_profiles",
        sa.Column(
            "birthday",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Data encrypted, ensure original length max 10",
        ),
    )
    op.drop_constraint(None, "doctor_profiles", type_="foreignkey")
    op.drop_constraint("uq_doctor_profiles_phone", "doctor_profiles", type_="unique")
    op.drop_constraint("uq_doctor_profiles_email", "doctor_profiles", type_="unique")
    op.alter_column(
        "doctor_profiles",
        "country_code",
        existing_type=sa.String(length=3),
        type_=sa.VARCHAR(length=10),
        comment=None,
        existing_comment="Country code, ensure original length max 10",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "postal_code",
        existing_type=sa.String(length=8),
        type_=sa.VARCHAR(length=255),
        comment="Data encrypted, ensure original length max 8",
        existing_comment="Post code, ensure original length max 8",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles", "phone", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column(
        "doctor_profiles",
        "address_3",
        existing_type=sa.VARCHAR(),
        comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "address_2",
        existing_type=sa.VARCHAR(),
        comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "address_1",
        existing_type=sa.VARCHAR(),
        comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.drop_column("doctor_profiles", "updated_by")
    op.drop_column("doctor_profiles", "created_by")
    op.drop_column("doctor_profiles", "prefecture_id")
    op.drop_column("doctor_profiles", "gender")
    op.drop_column("doctor_profiles", "order_index")
    op.drop_column("doctor_profiles", "date_of_birth_hash")
    op.drop_column("doctor_profiles", "date_of_birth")
    op.drop_column("doctor_profiles", "last_name_kana")
    op.drop_column("doctor_profiles", "first_name_kana")
    # ### end Alembic commands ###
