"""change column phone type

Revision ID: 524dedf0f8d4
Revises: 7f25e228365e
Create Date: 2025-07-15 19:18:16.420828

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "524dedf0f8d4"
down_revision: Union[str, None] = "7f25e228365e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "doctor_profiles",
        "phone",
        existing_type=sa.INTEGER(),
        type_=sa.String(),
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "home_phone",
        existing_type=sa.BIGINT(),
        type_=sa.String(),
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "phone",
        existing_type=sa.BIGINT(),
        type_=sa.String(),
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.drop_constraint(
        "patient_profiles_prefecture_id_fkey", "patient_profiles", type_="foreignkey"
    )
    op.drop_column("patient_profiles", "prefecture_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_profiles",
        sa.Column("prefecture_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "patient_profiles_prefecture_id_fkey",
        "patient_profiles",
        "m_prefecture",
        ["prefecture_id"],
        ["id"],
    )
    op.alter_column(
        "patient_profiles",
        "phone",
        existing_type=sa.String(),
        type_=sa.BIGINT(),
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "home_phone",
        existing_type=sa.String(),
        type_=sa.BIGINT(),
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "doctor_profiles",
        "phone",
        existing_type=sa.String(),
        type_=sa.INTEGER(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
