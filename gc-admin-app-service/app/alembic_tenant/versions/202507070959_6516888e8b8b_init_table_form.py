"""init table form

Revision ID: 6516888e8b8b
Revises: 56e1c2f1fdb1
Create Date: 2025-07-07 09:59:59.157011

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6516888e8b8b"
down_revision: Union[str, None] = "56e1c2f1fdb1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "forms",
        sa.Column(
            "uuid",
            sa.UUID(),
            nullable=False,
            comment="Primary key - UUID of the form",
        ),
        sa.Column(
            "form_name",
            sa.String(),
            nullable=False,
            comment="Name of the form (e.g. 'Medical Questionnaire for Adults')",
        ),
        sa.Column(
            "form_type",
            sa.Integer(),
            nullable=False,
            comment="Form type identifier, Ex: Survey, basic information,...",
        ),
        sa.Column(
            "is_active",
            sa.<PERSON>(),
            nullable=False,
            comment="Whether this form is currently active",
        ),
        sa.Column(
            "version",
            sa.String(),
            nullable=True,
            comment="Version of the form (e.g. v1.0, v2.1)",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("uuid"),
    )
    op.create_table(
        "form_item_groups",
        sa.Column(
            "uuid",
            sa.UUID(),
            nullable=False,
            comment="Primary key - UUID of the group",
        ),
        sa.Column(
            "form_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key referencing the parent form",
        ),
        sa.Column(
            "title",
            sa.String(),
            nullable=False,
            comment="Title of the question group/section (e.g. 'Basic Information')",
        ),
        sa.Column(
            "description",
            sa.String(),
            nullable=True,
            comment="Optional description to show under the title",
        ),
        sa.Column(
            "display_type",
            sa.Integer(),
            nullable=True,
            comment="Display style: accordion, page, section etc.",
        ),
        sa.Column(
            "order_index",
            sa.Integer(),
            nullable=True,
            comment="Ordering index within the form",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether the group is active",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["form_uuid"],
            ["forms.uuid"],
        ),
        sa.PrimaryKeyConstraint("uuid"),
    )
    op.create_table(
        "form_submissions",
        sa.Column(
            "uuid",
            sa.UUID(),
            nullable=False,
            comment="Primary key - UUID of the submission",
        ),
        sa.Column(
            "form_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key referencing the submitted form",
        ),
        sa.Column(
            "doctor_user_id",
            sa.Integer(),
            nullable=False,
            comment="Doctor who filled out the form on behalf of the patient",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="Patient for whom the form was filled out",
        ),
        sa.Column(
            "form_data",
            sa.JSON(),
            nullable=False,
            comment="Submitted answers in JSON format",
        ),
        sa.Column(
            "deleted_at",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Soft delete timestamp",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["doctor_user_id"],
            ["doctor_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["form_uuid"],
            ["forms.uuid"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("uuid"),
    )
    op.create_table(
        "form_items",
        sa.Column(
            "uuid",
            sa.UUID(),
            nullable=False,
            comment="Primary key - UUID of the question/item",
        ),
        sa.Column(
            "form_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key referencing the parent form",
        ),
        sa.Column(
            "label",
            sa.String(),
            nullable=False,
            comment="Label or question text shown to the user",
        ),
        sa.Column(
            "sub_label",
            sa.String(),
            nullable=True,
            comment="Sub Label or question text shown to the user",
        ),
        sa.Column(
            "field_type",
            sa.String(),
            nullable=False,
            comment="Type of input: text, radio, checkbox, date, image, etc.",
        ),
        sa.Column(
            "item_side",
            sa.String(),
            nullable=False,
            comment="User role responsible for inputting this item (e.g., doctor, patient, admin)",
        ),
        sa.Column(
            "required",
            sa.Boolean(),
            nullable=True,
            comment="Whether this question is required to answer",
        ),
        sa.Column(
            "is_favorite",
            sa.Boolean(),
            nullable=True,
            comment="Whether this question is required to answer",
        ),
        sa.Column(
            "extra_data",
            sa.JSON(),
            nullable=True,
            comment="Extra metadata: options, placeholders, validation rules",
        ),
        sa.Column(
            "order_index",
            sa.Integer(),
            nullable=True,
            comment="Ordering index within the form or group",
        ),
        sa.Column(
            "form_item_group_id",
            sa.UUID(),
            nullable=True,
            comment="Optional reference to item group",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether this question is active",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["form_item_group_id"],
            ["form_item_groups.uuid"],
        ),
        sa.ForeignKeyConstraint(
            ["form_uuid"],
            ["forms.uuid"],
        ),
        sa.PrimaryKeyConstraint("uuid"),
    )
    op.alter_column(
        "patient_waitings",
        "status",
        existing_type=sa.INTEGER(),
        comment="",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "status",
        existing_type=sa.INTEGER(),
        comment=None,
        existing_comment="",
        existing_nullable=False,
    )
    op.drop_table("form_items")
    op.drop_table("form_submissions")
    op.drop_table("form_item_groups")
    op.drop_table("forms")
    # ### end Alembic commands ###
