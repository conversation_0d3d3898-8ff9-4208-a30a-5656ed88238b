"""rename tenant id to tenant uuid

Revision ID: 04270bd458e3
Revises: b462bb1196a6
Create Date: 2025-08-07 11:51:00.540663

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "04270bd458e3"  # pragma: allowlist secret
down_revision: Union[str, None] = "b462bb1196a6"  # pragma: allowlist secret
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tenant_configuration", sa.Column("tenant_uuid", sa.UUID(), nullable=False)
    )
    op.drop_column("tenant_configuration", "tenant_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tenant_configuration",
        sa.Column("tenant_id", sa.VARCHAR(), autoincrement=False, nullable=False),
    )
    op.drop_column("tenant_configuration", "tenant_uuid")
    # ### end Alembic commands ###
