"""init table medical device

Revision ID: 7dbebc341e2e
Revises: 04270bd458e3
Create Date: 2025-08-07 13:07:35.824316

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "7dbebc341e2e"  # pragma: allowlist secret
down_revision: Union[str, None] = "04270bd458e3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_device_bfa",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "auto_cleaning",
            sa.String(),
            nullable=True,
            comment="Auto-cleaning mode (ON/OFF), Enums: BfaDeviceAutoCleaning",
        ),
        sa.Column(
            "area_total",
            sa.Float(),
            nullable=False,
            comment="Total occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_left",
            sa.Float(),
            nullable=False,
            comment="Left occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_right",
            sa.Float(),
            nullable=False,
            comment="Right occlusion contact area (mm^2)",
        ),
        sa.Column(
            "ave", sa.Float(), nullable=False, comment="Average pressure - Total (MPa)"
        ),
        sa.Column(
            "ave_left",
            sa.Float(),
            nullable=False,
            comment="Average pressure - Left side (MPa)",
        ),
        sa.Column(
            "ave_right",
            sa.Float(),
            nullable=False,
            comment="Average pressure - Right side (MPa)",
        ),
        sa.Column(
            "max_total",
            sa.Float(),
            nullable=False,
            comment="Maximum pressure - Total (MPa)",
        ),
        sa.Column(
            "max_left",
            sa.Float(),
            nullable=False,
            comment="Maximum pressure - Left side (MPa)",
        ),
        sa.Column(
            "max_right",
            sa.Float(),
            nullable=False,
            comment="Maximum pressure - Right side (MPa)",
        ),
        sa.Column(
            "force_total", sa.Float(), nullable=False, comment="Total bite force (N)"
        ),
        sa.Column(
            "force_left", sa.Float(), nullable=False, comment="Left bite force (N)"
        ),
        sa.Column(
            "force_right", sa.Float(), nullable=False, comment="Right bite force (N)"
        ),
        sa.Column(
            "comment", sa.Text(), nullable=True, comment="Additional notes or comments"
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_bfa_business_patient_device"
        ),
    )
    op.create_table(
        "medical_device_bte",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "bite_array",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="Data array containing detailed information of the bites",
        ),
        sa.Column(
            "comment", sa.Text(), nullable=True, comment="Additional notes or comments"
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_bte_business_patient_device"
        ),
    )
    op.create_table(
        "medical_device_emg",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "start_time", sa.Time(), nullable=False, comment="Measurement start time"
        ),
        sa.Column(
            "end_time", sa.Time(), nullable=False, comment="Measurement end time"
        ),
        sa.Column(
            "total_time",
            sa.Time(),
            nullable=False,
            comment="Total analysis time (string format)",
        ),
        sa.Column(
            "max_peak",
            sa.Float(),
            nullable=False,
            comment="Maximum clenching peak value (mV)",
        ),
        sa.Column(
            "base_peak",
            sa.Float(),
            nullable=False,
            comment="Baseline section peak value (mV)",
        ),
        sa.Column(
            "total_clenching",
            sa.Float(),
            nullable=False,
            comment="Total number of clenches",
        ),
        sa.Column(
            "clenching_per_hour",
            sa.Float(),
            nullable=False,
            comment="Clenching frequency per hour",
        ),
        sa.Column(
            "burst_total", sa.Float(), nullable=False, comment="Total number of bursts"
        ),
        sa.Column(
            "burst_total_dur",
            sa.Time(),
            nullable=False,
            comment="Total duration of bursts (string format)",
        ),
        sa.Column(
            "burst_total_ave",
            sa.Time(),
            nullable=False,
            comment="Average duration of bursts (string format)",
        ),
        sa.Column(
            "burst_per_hour",
            sa.Float(),
            nullable=False,
            comment="Number of bursts per hour",
        ),
        sa.Column(
            "burst_total_duration_per_hour",
            sa.Time(),
            nullable=False,
            comment="Total duration of bursts per hour (string format)",
        ),
        sa.Column(
            "clenching_strength_ave",
            sa.Float(),
            nullable=False,
            comment="Average clenching intensity",
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_emg_business_patient_device"
        ),
    )
    op.create_table(
        "medical_device_mvt",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=True,
            comment="Path or filename of the related image",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_mvt_business_patient_device"
        ),
    )
    op.create_table(
        "medical_images",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="Foreign key to the patient this image belongs to.",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="The date of the examination, used for grouping and filtering.",
        ),
        sa.Column(
            "device_type",
            sa.String(),
            nullable=False,
            comment="Device type (BFA, EMG, MVT, BTE),Enums: MedicalDeviceType ",
        ),
        sa.Column(
            "medical_device_id",
            sa.Integer(),
            nullable=False,
            comment="The ID of the record in the source medical device table. Example: medical_device_bfa",
        ),
        sa.Column(
            "image_file_path",
            sa.Text(),
            nullable=False,
            comment="Path or URL to the image file.",
        ),
        sa.Column(
            "comment",
            sa.Text(),
            nullable=True,
            comment="Additional notes or comments about the image.",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("medical_images")
    op.drop_table("medical_device_mvt")
    op.drop_table("medical_device_emg")
    op.drop_table("medical_device_bte")
    op.drop_table("medical_device_bfa")
    # ### end Alembic commands ###
