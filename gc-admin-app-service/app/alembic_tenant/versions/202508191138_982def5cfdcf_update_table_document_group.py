"""update table document group

Revision ID: 982def5cfdcf
Revises: ead8f8dae255
Create Date: 2025-08-19 11:38:47.613550

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "982def5cfdcf"
down_revision: Union[str, None] = "ead8f8dae255"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("document_group", sa.Column("key_name", sa.String(), nullable=True))
    op.add_column(
        "document_group",
        sa.Column(
            "is_parent", sa.<PERSON>(), nullable=False, server_default=sa.text("FALSE")
        ),
    )
    op.add_column(
        "document_group", sa.Column("group_parent_id", sa.Integer(), nullable=True)
    )
    op.create_unique_constraint(
        "uq_key_name_document_group", "document_group", ["key_name"]
    )
    op.create_unique_constraint("uq_name_document_group", "document_group", ["name"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("uq_name_document_group", "document_group", type_="unique")
    op.drop_constraint("uq_key_name_document_group", "document_group", type_="unique")
    op.drop_column("document_group", "group_parent_id")
    op.drop_column("document_group", "is_parent")
    op.drop_column("document_group", "key_name")
    # ### end Alembic commands ###
