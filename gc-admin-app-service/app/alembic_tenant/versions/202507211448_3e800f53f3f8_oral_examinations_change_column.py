"""oral examinations change column

Revision ID: 3e800f53f3f8
Revises: 441255d2a029
Create Date: 2025-07-21 14:48:11.313770

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3e800f53f3f8"
down_revision: Union[str, None] = "441255d2a029"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "oral_examinations",
        sa.Column("medical_history_id", sa.Integer(), nullable=False),
    )
    op.drop_constraint(
        "oral_examinations_patient_waiting_id_fkey",
        "oral_examinations",
        type_="foreignkey",
    )
    op.create_foreign_key(
        None, "oral_examinations", "medical_histories", ["medical_history_id"], ["id"]
    )
    op.drop_column("oral_examinations", "patient_waiting_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "oral_examinations",
        sa.Column(
            "patient_waiting_id", sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.drop_constraint(None, "oral_examinations", type_="foreignkey")
    op.create_foreign_key(
        "oral_examinations_patient_waiting_id_fkey",
        "oral_examinations",
        "patient_waitings",
        ["patient_waiting_id"],
        ["id"],
    )
    op.drop_column("oral_examinations", "medical_history_id")
    # ### end Alembic commands ###
