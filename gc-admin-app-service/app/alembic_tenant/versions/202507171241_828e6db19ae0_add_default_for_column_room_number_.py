"""add_default_for_column_room_number_table_patient_waiting

Revision ID: 828e6db19ae0
Revises: ab4777147c37
Create Date: 2025-07-17 12:41:51.749398

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "828e6db19ae0"  # pragma: allowlist secret
down_revision: Union[str, None] = "ab4777147c37"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "room_number",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="Room number",
        server_default=sa.text("0"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "room_number",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="Room number",
    )
    # ### end Alembic commands ###
