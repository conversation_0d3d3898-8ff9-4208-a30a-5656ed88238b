"""add form uuid in form group

Revision ID: b8129ecf932a
Revises: 4e4f0954e0d3
Create Date: 2025-07-11 17:02:44.211991

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b8129ecf932a"  # pragma: allowlist secret
down_revision: Union[str, None] = "4e4f0954e0d3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "form_item_groups",
        sa.Column(
            "form_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key referencing the parent form",
        ),
    )
    op.create_foreign_key(None, "form_item_groups", "forms", ["form_uuid"], ["uuid"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "form_item_groups", type_="foreignkey")
    op.drop_column("form_item_groups", "form_uuid")
    # ### end Alembic commands ###
