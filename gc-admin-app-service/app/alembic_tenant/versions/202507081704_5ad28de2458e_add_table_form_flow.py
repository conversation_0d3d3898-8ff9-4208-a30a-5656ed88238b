"""add table form flow

Revision ID: 5ad28de2458e
Revises: e1c2093ea923
Create Date: 2025-07-08 17:04:57.103761

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "5ad28de2458e"
down_revision: Union[str, None] = "e1c2093ea923"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "form_flows",
        sa.Column(
            "uuid",
            sa.UUID(),
            nullable=False,
            comment="Primary key - UUID of the form flow",
        ),
        sa.Column(
            "flow_name",
            sa.String(),
            nullable=False,
            comment="Name of the Flow (e.g. 'Medical Questionnaire for Adults')",
        ),
        sa.Column(
            "flow_type",
            sa.Integer(),
            nullable=False,
            comment="Form type identifier, Ex: Survey, basic information,...",
        ),
        sa.Column(
            "description",
            sa.String(),
            nullable=True,
            comment="Optional description to show under the title",
        ),
        sa.Column(
            "version",
            sa.String(),
            nullable=False,
            comment="Version of the form (e.g. 1.0, 2.1)",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether this form is currently active",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("uuid"),
    )
    op.add_column(
        "form_submissions",
        sa.Column(
            "form_flow_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key referencing the submitted form flow",
        ),
    )
    op.alter_column(
        "form_submissions",
        "form_data",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        comment="Submitted answers in JSON format and form item type",
        existing_comment="Submitted answers in JSON format",
        existing_nullable=False,
    )
    op.alter_column(
        "form_submissions",
        "deleted_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        comment=None,
        existing_comment="Soft delete timestamp",
        existing_nullable=True,
    )
    op.drop_constraint(
        "form_submissions_form_uuid_fkey",
        "form_submissions",
        type_="foreignkey",
    )
    op.create_foreign_key(
        None, "form_submissions", "forms", ["form_flow_uuid"], ["uuid"]
    )
    op.drop_column("form_submissions", "form_uuid")
    op.add_column(
        "forms",
        sa.Column(
            "form_flow_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key referencing the submitted form flow",
        ),
    )
    op.add_column(
        "forms",
        sa.Column(
            "description",
            sa.String(),
            nullable=True,
            comment="Optional description to show under the title",
        ),
    )
    op.add_column(
        "forms",
        sa.Column(
            "order_index",
            sa.Integer(),
            nullable=False,
            comment="Ordering index within the form flows",
        ),
    )
    op.add_column(
        "forms",
        sa.Column(
            "delete_flag",
            sa.Boolean(),
            nullable=False,
            comment="Form is deleted",
        ),
    )
    op.alter_column(
        "forms",
        "form_name",
        existing_type=sa.VARCHAR(),
        comment="Name of the form (e.g. 'Basic Information')",
        existing_comment="Name of the form (e.g. 'Medical Questionnaire for Adults')",
        existing_nullable=False,
    )
    op.create_foreign_key(None, "forms", "forms", ["form_flow_uuid"], ["uuid"])
    op.drop_column("forms", "is_active")
    op.drop_column("forms", "version")
    op.drop_column("forms", "form_type")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "forms",
        sa.Column(
            "form_type",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Form type identifier, Ex: Survey, basic information,...",
        ),
    )
    op.add_column(
        "forms",
        sa.Column(
            "version",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Version of the form (e.g. v1.0, v2.1)",
        ),
    )
    op.add_column(
        "forms",
        sa.Column(
            "is_active",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=False,
            comment="Whether this form is currently active",
        ),
    )
    op.drop_constraint(None, "forms", type_="foreignkey")
    op.alter_column(
        "forms",
        "form_name",
        existing_type=sa.VARCHAR(),
        comment="Name of the form (e.g. 'Medical Questionnaire for Adults')",
        existing_comment="Name of the form (e.g. 'Basic Information')",
        existing_nullable=False,
    )
    op.drop_column("forms", "delete_flag")
    op.drop_column("forms", "order_index")
    op.drop_column("forms", "description")
    op.drop_column("forms", "form_flow_uuid")
    op.add_column(
        "form_submissions",
        sa.Column(
            "form_uuid",
            sa.UUID(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key referencing the submitted form",
        ),
    )
    op.drop_constraint(None, "form_submissions", type_="foreignkey")
    op.create_foreign_key(
        "form_submissions_form_uuid_fkey",
        "form_submissions",
        "forms",
        ["form_uuid"],
        ["uuid"],
    )
    op.alter_column(
        "form_submissions",
        "deleted_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        comment="Soft delete timestamp",
        existing_nullable=True,
    )
    op.alter_column(
        "form_submissions",
        "form_data",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        comment="Submitted answers in JSON format",
        existing_comment="Submitted answers in JSON format and form item type",
        existing_nullable=False,
    )
    op.drop_column("form_submissions", "form_flow_uuid")
    op.drop_table("form_flows")
    # ### end Alembic commands ###
