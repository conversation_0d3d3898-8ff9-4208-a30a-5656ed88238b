"""add_column_room_number_to_table_patient_waiting

Revision ID: ab4777147c37
Revises: 77075931160b
Create Date: 2025-07-16 20:51:11.523358

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "ab4777147c37"
down_revision: Union[str, None] = "77075931160b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_waitings",
        sa.Column("room_number", sa.Integer(), nullable=True, comment="Room number"),
    )
    op.alter_column(
        "patient_waitings",
        "coming_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "coming_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
    )
    op.drop_column("patient_waitings", "room_number")
    # ### end Alembic commands ###
