"""add unique patient user relationship

Revision ID: 1a6fb7a1aa38
Revises: b462bb1196a6
Create Date: 2025-08-05 11:01:06.748803

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1a6fb7a1aa38"
down_revision: Union[str, None] = "b71826223be2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "uq_patient_user_relationship",
        "patient_user_relationship",
        ["patient_user_id", "sub_patient_user_id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uq_patient_user_relationship", "patient_user_relationship", type_="unique"
    )
    # ### end Alembic commands ###
