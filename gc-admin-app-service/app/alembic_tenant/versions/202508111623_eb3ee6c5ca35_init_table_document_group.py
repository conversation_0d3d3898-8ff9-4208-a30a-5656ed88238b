"""init table document group

Revision ID: eb3ee6c5ca35
Revises: 61f852ffcc41
Create Date: 2025-08-11 16:23:24.859561

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "eb3ee6c5ca35"
down_revision: Union[str, None] = "61f852ffcc41"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_group",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "name", sa.String(), nullable=False, comment="Name of the document group"
        ),
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who last updated the record",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "medical_templates",
        sa.Column(
            "document_group_id",
            sa.Integer(),
            nullable=True,
            comment="Reference to document group",
        ),
    )
    op.create_foreign_key(
        None, "medical_templates", "document_group", ["document_group_id"], ["id"]
    )
    op.drop_column("medical_templates", "label")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "medical_templates",
        sa.Column(
            "label",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Label of the template",
        ),
    )
    op.drop_constraint(None, "medical_templates", type_="foreignkey")
    op.drop_column("medical_templates", "document_group_id")
    op.drop_table("document_group")
    # ### end Alembic commands ###
