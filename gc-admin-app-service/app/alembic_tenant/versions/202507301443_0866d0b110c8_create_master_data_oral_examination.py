"""create master data oral examination

Revision ID: 0866d0b110c8
Revises: 0aea1bc6b773
Create Date: 2025-07-30 14:43:02.577736

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy import MetaData, Table
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "0866d0b110c8"
down_revision: Union[str, None] = "0aea1bc6b773"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_oral_examinations",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name_jp", sa.String(), nullable=True, comment="Name Japanese"),
        sa.Column("name_en", sa.String(), nullable=True, comment="Name English"),
        sa.Column(
            "type",
            sa.String(),
            nullable=True,
            comment="Type of the examination (FULL, PART, OVER)",
        ),
        sa.Column(
            "category",
            sa.Integer(),
            nullable=True,
            comment="Category of the examination (1: Treatment, 2: Method, 3: Both Method and Treatment, 4: Other)",
        ),
        sa.Column(
            "order", sa.JSON(), nullable=True, comment="Order of the examination items"
        ),
        sa.Column(
            "need_treatment",
            sa.Integer(),
            nullable=True,
            comment="Does this examination require treatment? (1: Yes, 2: No)",
        ),
        sa.Column(
            "is_hidden",
            sa.Boolean(),
            nullable=True,
            comment="Is this examination hidden from the list?",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.drop_table("m_oral_examination_treatments")
    op.drop_table("m_oral_examination_methods")

    # Insert initial data into m_oral_examinations
    data = [
        {
            "name_jp": "H7",
            "name_en": "In treatment",
            "type": "OVER",
            "category": 1,
            "is_hidden": 1,
            "need_treatment": 1,
            "order": {"position_x": "H", "position_y": 6},
        },
        {
            "name_jp": "H6",
            "name_en": "In treatment",
            "type": "OVER",
            "category": 1,
            "is_hidden": 1,
            "need_treatment": 1,
            "order": {"position_x": "H", "position_y": 5},
        },
        {
            "name_jp": "ハセツ",
            "name_en": "Need treatment",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "H", "position_y": 4},
        },
        {
            "name_jp": "全埋伏",
            "name_en": "All impacted",
            "type": "OVER",
            "category": 3,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "H", "position_y": 3},
        },
        {
            "name_jp": "フテキ",
            "name_en": "Futeki",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "H", "position_y": 2},
        },
        {
            "name_jp": 'C"',
            "name_en": 'C"',
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "H", "position_y": 1},
        },
        {
            "name_jp": "G7",
            "name_en": "G&",
            "type": "OVER",
            "category": 1,
            "is_hidden": 1,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 8},
        },
        {
            "name_jp": "G6",
            "name_en": "G6",
            "type": "OVER",
            "category": 1,
            "is_hidden": 1,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 7},
        },
        {
            "name_jp": "治療中",
            "name_en": "In treatment",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "H", "position_y": 5},
        },
        {
            "name_jp": "GA",
            "name_en": "GA",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 5},
        },
        {
            "name_jp": "ダツリ",
            "name_en": "Back half impacted",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 4},
        },
        {
            "name_jp": "半埋伏",
            "name_en": "Front half impacted",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 3},
        },
        {
            "name_jp": "Per",
            "name_en": "Per",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 2},
        },
        {
            "name_jp": "WSD",
            "name_en": "WSD",
            "type": "OVER",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "G", "position_y": 1},
        },
        {
            "name_jp": "WSD",
            "name_en": "WSD",
            "type": "PART",
            "category": 1,
            "is_hidden": 1,
            "need_treatment": 1,
            "order": {"position_x": "F", "position_y": 6},
        },
        {
            "name_jp": "PonG",
            "name_en": "PonG",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "F", "position_y": 5},
        },
        {
            "name_jp": "Pon白",
            "name_en": "Pon white",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "F", "position_y": 4},
        },
        {
            "name_jp": "PonMB",
            "name_en": "PonMB",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "F", "position_y": 3},
        },
        {
            "name_jp": "Pon",
            "name_en": "Pon",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "F", "position_y": 2},
        },
        {
            "name_jp": "Br",
            "name_en": "Br",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "F", "position_y": 1},
        },
        {
            "name_jp": "TEK",
            "name_en": "TEK",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "E", "position_y": 6},
        },
        {
            "name_jp": "IP義",
            "name_en": "Ip meaning",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "E", "position_y": 5},
        },
        {
            "name_jp": "IP",
            "name_en": "IP",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "E", "position_y": 4},
        },
        {
            "name_jp": "MB",
            "name_en": "MB",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "E", "position_y": 3},
        },
        {
            "name_jp": "JC",
            "name_en": "JC",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "E", "position_y": 2},
        },
        {
            "name_jp": "前CK",
            "name_en": "Previous ck",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "E", "position_y": 1},
        },
        {
            "name_jp": "冠G",
            "name_en": "Crown g",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "D", "position_y": 5},
        },
        {
            "name_jp": "冠白",
            "name_en": "Crown white",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "D", "position_y": 4},
        },
        {
            "name_jp": "CAD",
            "name_en": "Cad",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "D", "position_y": 3},
        },
        {
            "name_jp": "FMC",
            "name_en": "Fmc",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "D", "position_y": 2},
        },
        {
            "name_jp": "コア",
            "name_en": "Core",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "D", "position_y": 1},
        },
        {
            "name_jp": "On",
            "name_en": "On",
            "type": "PART",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "C", "position_y": 6},
        },
        {
            "name_jp": "InG",
            "name_en": "InG",
            "type": "PART",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "C", "position_y": 5},
        },
        {
            "name_jp": "In白",
            "name_en": "In white",
            "type": "PART",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "C", "position_y": 4},
        },
        {
            "name_jp": "In",
            "name_en": "In",
            "type": "PART",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "C", "position_y": 3},
        },
        {
            "name_jp": "AF",
            "name_en": "AF",
            "type": "PART",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "C", "position_y": 2},
        },
        {
            "name_jp": "A充",
            "name_en": "A filling",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "C", "position_y": 1},
        },
        {
            "name_jp": "CR充",
            "name_en": "Cr filling",
            "type": "PART",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "B", "position_y": 5},
        },
        {
            "name_jp": "C4",
            "name_en": "C4",
            "type": "FULL",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "B", "position_y": 4},
        },
        {
            "name_jp": "C3",
            "name_en": "C3",
            "type": "FULL",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "B", "position_y": 3},
        },
        {
            "name_jp": "C2",
            "name_en": "C2",
            "type": "PART",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "B", "position_y": 2},
        },
        {
            "name_jp": "C1",
            "name_en": "C1",
            "type": "PART",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "B", "position_y": 1},
        },
        {
            "name_jp": "C0",
            "name_en": "C0",
            "type": "PART",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "B", "position_y": 1},
        },
        {
            "name_jp": "根面板",
            "name_en": "Root plate",
            "type": "FULL",
            "category": 3,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "A", "position_y": 6},
        },
        {
            "name_jp": "残根",
            "name_en": "Residual root",
            "type": "FULL",
            "category": 1,
            "is_hidden": 0,
            "need_treatment": 1,
            "order": {"position_x": "A", "position_y": 5},
        },
        {
            "name_jp": "ｼｰﾗﾝﾄ",
            "name_en": "Sealant",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "A", "position_y": 4},
        },
        {
            "name_jp": "欠損",
            "name_en": "Missing",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "A", "position_y": 2},
        },
        {
            "name_jp": "健康",
            "name_en": "Healthy",
            "type": "FULL",
            "category": 2,
            "is_hidden": 0,
            "need_treatment": 0,
            "order": {"position_x": "A", "position_y": 1},
        },
    ]

    metadata = MetaData()
    m_oral_examinations = Table(
        "m_oral_examinations", metadata, autoload_with=op.get_bind()
    )

    op.bulk_insert(m_oral_examinations, data)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_oral_examination_methods",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "name_jp",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Name Japanese",
        ),
        sa.Column(
            "name_en",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Name English",
        ),
        sa.Column(
            "type",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Type of the examination (FULL, PART)",
        ),
        sa.Column(
            "is_hidden",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="Is this examination hidden from the list?",
        ),
        sa.Column(
            "order",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Order of the examination in the list",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name="m_oral_examination_methods_pkey"),
    )
    op.create_table(
        "m_oral_examination_treatments",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "name_jp",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Name Japanese",
        ),
        sa.Column(
            "name_en",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Name English",
        ),
        sa.Column(
            "is_hidden",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="Is this examination hidden from the list?",
        ),
        sa.Column(
            "order",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Order of the examination in the list",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name="m_oral_examination_treatments_pkey"),
    )
    op.drop_table("m_oral_examinations")
    # ### end Alembic commands ###
