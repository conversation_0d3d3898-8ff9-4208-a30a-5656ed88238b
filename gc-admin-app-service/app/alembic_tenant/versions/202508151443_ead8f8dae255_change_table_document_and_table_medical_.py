"""change table document and table medical device

Revision ID: ead8f8dae255
Revises: 1a6fb7a1aa38
Create Date: 2025-08-15 14:43:22.150775

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "ead8f8dae255"
down_revision: Union[str, None] = "1a6fb7a1aa38"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("medical_images")
    op.add_column(
        "document_management_versions",
        sa.Column(
            "preview_document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Preview(thumbnail) data of the document",
        ),
    )
    op.add_column(
        "document_managements",
        sa.Column(
            "preview_document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Preview(Thumbnail) data of the document",
        ),
    )
    op.add_column(
        "medical_device_bfa",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.drop_column("medical_device_bfa", "image_file_path")
    op.add_column(
        "medical_device_bte",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.drop_column("medical_device_bte", "image_file_path")
    op.add_column(
        "medical_device_emg",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.drop_column("medical_device_emg", "image_file_path")
    op.add_column(
        "medical_device_mvt",
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
    )
    op.drop_column("medical_device_mvt", "image_file_path")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "medical_device_mvt",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.drop_column("medical_device_mvt", "image_file_paths")
    op.add_column(
        "medical_device_emg",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.drop_column("medical_device_emg", "image_file_paths")
    op.add_column(
        "medical_device_bte",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.drop_column("medical_device_bte", "image_file_paths")
    op.add_column(
        "medical_device_bfa",
        sa.Column(
            "image_file_path",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Path or filename of the related image",
        ),
    )
    op.drop_column("medical_device_bfa", "image_file_paths")
    op.drop_column("document_managements", "preview_document_data")
    op.drop_column("document_management_versions", "preview_document_data")
    op.create_table(
        "medical_images",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "patient_user_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key to the patient this image belongs to.",
        ),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="The date of the examination, used for grouping and filtering.",
        ),
        sa.Column(
            "device_type",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Device type (BFA, EMG, MVT, BTE),Enums: MedicalDeviceType ",
        ),
        sa.Column(
            "medical_device_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="The ID of the record in the source medical device table. Example: medical_device_bfa",
        ),
        sa.Column(
            "image_file_path",
            sa.TEXT(),
            autoincrement=False,
            nullable=False,
            comment="Path or URL to the image file.",
        ),
        sa.Column(
            "comment",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Additional notes or comments about the image.",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="medical_images_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_images_pkey"),
    )
    # ### end Alembic commands ###
