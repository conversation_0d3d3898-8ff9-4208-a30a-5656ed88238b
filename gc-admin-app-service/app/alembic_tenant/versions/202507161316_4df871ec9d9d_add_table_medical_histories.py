"""add table medical histories

Revision ID: 4df871ec9d9d
Revises: 524dedf0f8d4
Create Date: 2025-07-16 13:16:42.829988

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "4df871ec9d9d"  # pragma: allowlist secret
down_revision: Union[str, None] = "524dedf0f8d4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_histories",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="Reference to patient user",
        ),
        sa.Column(
            "patient_waiting_id",
            sa.Integer(),
            nullable=True,
            comment="Reference to patient waiting record",
        ),
        sa.Column(
            "doctor_user_ids",
            sa.ARRAY(sa.Integer()),
            nullable=True,
            comment="List of doctor user IDs (PostgreSQL only)",
        ),
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            comment="Status of the medical history",
        ),
        sa.Column(
            "visit_start_datetime",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Start datetime of the visit",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_waiting_id"],
            ["patient_waitings.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "patient_profiles", sa.Column("prefecture_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        None, "patient_profiles", "m_prefecture", ["prefecture_id"], ["id"]
    )
    op.alter_column(
        "patient_users",
        "status",
        existing_type=sa.BOOLEAN(),
        comment="Status active of patient",
        existing_comment="1: Active, 2: Inactive",
        existing_nullable=False,
    )
    op.add_column(
        "patient_waitings",
        sa.Column(
            "visit_start_date", sa.Date(), nullable=True, comment="Visit start date"
        ),
    )
    op.add_column(
        "patient_waitings",
        sa.Column(
            "visit_start_time", sa.Time(), nullable=True, comment="Visit start time"
        ),
    )
    op.add_column(
        "patient_waitings",
        sa.Column("visit_end_date", sa.Date(), nullable=True, comment="Visit end date"),
    )
    op.add_column(
        "patient_waitings",
        sa.Column("visit_end_time", sa.Time(), nullable=True, comment="Visit end time"),
    )
    op.add_column(
        "patient_waitings",
        sa.Column(
            "assigned_doctors",
            sa.ARRAY(sa.Integer()),
            nullable=True,
            comment="Comma-separated list of doctor IDs or names",
        ),
    )
    op.add_column(
        "patient_waitings",
        sa.Column(
            "assigned_room",
            sa.String(),
            nullable=True,
            comment="Assigned room name or ID",
        ),
    )
    op.drop_column("patient_waitings", "end_examine_time")
    op.drop_column("patient_waitings", "checking_time")
    op.drop_column("patient_waitings", "start_examine_time")
    op.add_column(
        "reservations",
        sa.Column(
            "external_reservation_code",
            sa.String(),
            nullable=True,
            comment="External reservation code",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "external_patient_id",
            sa.String(),
            nullable=True,
            comment="External patient identifier",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "group_uuid",
            sa.String(),
            nullable=True,
            comment="UUID for reservation group",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "doctor_user_ids",
            sa.ARRAY(sa.Integer()),
            nullable=True,
            comment="List of doctor user IDs (PostgreSQL only)",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "schedule_visit_date",
            sa.Date(),
            nullable=True,
            comment="Scheduled visit date",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "schedule_visit_time",
            sa.Time(),
            nullable=True,
            comment="Scheduled visit time",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "schedule_visit_minutes",
            sa.Integer(),
            nullable=True,
            comment="Estimated duration of visit in minutes",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "source",
            sa.String(),
            nullable=True,
            comment="Source of the reservation (e.g., web, mobile, etc.)",
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "extra_data",
            sa.JSON(),
            nullable=True,
            comment="Extra JSON data for customization or metadata",
        ),
    )
    op.alter_column(
        "reservations",
        "status",
        existing_type=sa.INTEGER(),
        nullable=True,
        comment="Reservation status",
    )
    op.alter_column(
        "reservations",
        "note",
        existing_type=sa.TEXT(),
        comment="Additional notes for the reservation",
        existing_nullable=True,
    )
    op.drop_column("reservations", "next_book_date")
    op.drop_column("reservations", "next_note")
    op.drop_column("reservations", "this_book_date")
    op.drop_column("reservations", "handrwitten_s3_path")
    op.drop_column("reservations", "next_doctors")
    op.drop_column("reservations", "form_submission_id")
    op.drop_column("reservations", "this_doctors")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "reservations",
        sa.Column(
            "this_doctors",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "form_submission_id", sa.INTEGER(), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "next_doctors",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "handrwitten_s3_path", sa.VARCHAR(), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "this_book_date", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "reservations",
        sa.Column("next_note", sa.TEXT(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "reservations",
        sa.Column(
            "next_book_date", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
    )
    op.alter_column(
        "reservations",
        "note",
        existing_type=sa.TEXT(),
        comment=None,
        existing_comment="Additional notes for the reservation",
        existing_nullable=True,
    )
    op.alter_column(
        "reservations",
        "status",
        existing_type=sa.INTEGER(),
        nullable=False,
        comment=None,
        existing_comment="Reservation status",
    )
    op.drop_column("reservations", "extra_data")
    op.drop_column("reservations", "source")
    op.drop_column("reservations", "schedule_visit_minutes")
    op.drop_column("reservations", "schedule_visit_time")
    op.drop_column("reservations", "schedule_visit_date")
    op.drop_column("reservations", "doctor_user_ids")
    op.drop_column("reservations", "group_uuid")
    op.drop_column("reservations", "external_patient_id")
    op.drop_column("reservations", "external_reservation_code")
    op.add_column(
        "patient_waitings",
        sa.Column(
            "start_examine_time",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
            comment="Start time of the examination",
        ),
    )
    op.add_column(
        "patient_waitings",
        sa.Column(
            "checking_time",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
            comment="Time when the patient was checked in",
        ),
    )
    op.add_column(
        "patient_waitings",
        sa.Column(
            "end_examine_time",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
            comment="End time of the examination",
        ),
    )
    op.drop_column("patient_waitings", "assigned_room")
    op.drop_column("patient_waitings", "assigned_doctors")
    op.drop_column("patient_waitings", "visit_end_time")
    op.drop_column("patient_waitings", "visit_end_date")
    op.drop_column("patient_waitings", "visit_start_time")
    op.drop_column("patient_waitings", "visit_start_date")
    op.alter_column(
        "patient_users",
        "status",
        existing_type=sa.BOOLEAN(),
        comment="1: Active, 2: Inactive",
        existing_comment="Status active of patient",
        existing_nullable=False,
    )
    op.drop_constraint(None, "patient_profiles", type_="foreignkey")
    op.drop_column("patient_profiles", "prefecture_id")
    op.drop_table("medical_histories")
    # ### end Alembic commands ###
