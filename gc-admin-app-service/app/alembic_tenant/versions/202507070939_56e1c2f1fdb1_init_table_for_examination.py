"""init-table-for-examination

Revision ID: 56e1c2f1fdb1
Revises: e7ed1e3751a6
Create Date: 2025-07-07 09:39:18.831066

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "56e1c2f1fdb1"  # pragma: allowlist secret
down_revision: Union[str, None] = "e7ed1e3751a6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_eras",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=True, comment="Name of the eras"),
        sa.Column("year", sa.Integer(), nullable=True, comment="Year of the eras"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "m_oral_examination_methods",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name_jp", sa.String(), nullable=True, comment="Name Japanese"),
        sa.Column("name_en", sa.String(), nullable=True, comment="Name English"),
        sa.Column(
            "type",
            sa.String(),
            nullable=True,
            comment="Type of the examination (FULL, PART)",
        ),
        sa.Column(
            "is_hidden",
            sa.Boolean(),
            nullable=True,
            comment="Is this examination hidden from the list?",
        ),
        sa.Column(
            "order",
            sa.Integer(),
            nullable=True,
            comment="Order of the examination in the list",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "m_oral_examination_treatments",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name_jp", sa.String(), nullable=True, comment="Name Japanese"),
        sa.Column("name_en", sa.String(), nullable=True, comment="Name English"),
        sa.Column(
            "is_hidden",
            sa.Boolean(),
            nullable=True,
            comment="Is this examination hidden from the list?",
        ),
        sa.Column(
            "order",
            sa.Integer(),
            nullable=True,
            comment="Order of the examination in the list",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "reservations",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("this_doctors", sa.JSON(), nullable=True),
        sa.Column("next_doctors", sa.JSON(), nullable=True),
        sa.Column("this_book_date", sa.DateTime(), nullable=True),
        sa.Column("next_book_date", sa.DateTime(), nullable=True),
        sa.Column("note", sa.Text(), nullable=True),
        sa.Column("next_note", sa.Text(), nullable=True),
        sa.Column("status", sa.Integer(), nullable=False),
        sa.Column("handrwitten_s3_path", sa.String(), nullable=True),
        sa.Column("form_submission_id", sa.Integer(), nullable=True),
        sa.Column(
            "chair_number",
            sa.Integer(),
            nullable=True,
            comment="Chair number for the reservation",
        ),
        sa.Column("created_by", sa.Integer(), nullable=True),
        sa.Column("updated_by", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "teeth_details",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "tooth_data",
            sa.JSON(),
            nullable=False,
            comment="Details of the tooth all attributes",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "patient_waitings",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("reservation_id", sa.Integer(), nullable=False),
        sa.Column(
            "emergency_flag",
            sa.Integer(),
            nullable=False,
            comment="0: Normal, 1: Emergency",
        ),
        sa.Column("coming_time", sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column("status", sa.Integer(), nullable=False),
        sa.Column(
            "start_examine_time",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Start time of the examination",
        ),
        sa.Column(
            "end_examine_time",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="End time of the examination",
        ),
        sa.Column(
            "checking_time",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Time when the patient was checked in",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["reservation_id"],
            ["reservations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "oral_examinations",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column("patient_waiting_id", sa.Integer(), nullable=False),
        sa.Column("examination_date", sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column("note", sa.Text(), nullable=True),
        sa.Column(
            "memo_path",
            sa.String(),
            nullable=True,
            comment="Path to the oral examination memo file in S3",
        ),
        sa.Column(
            "tooth_type",
            sa.Integer(),
            nullable=True,
            comment="Type of tooth examined",
        ),
        sa.Column(
            "oral_examination",
            sa.JSON(),
            nullable=True,
            comment="Details of the oral examination",
        ),
        sa.Column(
            "periodontal_1point",
            sa.JSON(),
            nullable=True,
            comment="Periodontal 1-point",
        ),
        sa.Column(
            "periodontal_4point",
            sa.JSON(),
            nullable=True,
            comment="Periodontal 4-point",
        ),
        sa.Column(
            "periodontal_6point",
            sa.JSON(),
            nullable=True,
            comment="Periodontal 6-point",
        ),
        sa.Column("pcr", sa.JSON(), nullable=True, comment="PCR results"),
        sa.Column(
            "pcr_6point",
            sa.JSON(),
            nullable=True,
            comment="PCR 6-point results",
        ),
        sa.Column(
            "created_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            nullable=True,
            comment="ID of the user who last updated the record",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["patient_waiting_id"],
            ["patient_waitings.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "patient_users",
        sa.Column(
            "status",
            sa.Boolean(),
            nullable=False,
            comment="1: Active, 2: Inactive",
        ),
    )
    op.add_column(
        "patient_users",
        sa.Column(
            "iapo_patient_id",
            sa.String(),
            nullable=True,
            comment="ID from IAPO system",
        ),
    )
    op.add_column("patient_users", sa.Column("created_by", sa.Integer(), nullable=True))
    op.add_column("patient_users", sa.Column("updated_by", sa.Integer(), nullable=True))
    op.drop_column("patient_users", "is_active")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_users",
        sa.Column("is_active", sa.BOOLEAN(), autoincrement=False, nullable=False),
    )
    op.drop_column("patient_users", "updated_by")
    op.drop_column("patient_users", "created_by")
    op.drop_column("patient_users", "iapo_patient_id")
    op.drop_column("patient_users", "status")
    op.drop_table("oral_examinations")
    op.drop_table("patient_waitings")
    op.drop_table("teeth_details")
    op.drop_table("reservations")
    op.drop_table("m_oral_examination_treatments")
    op.drop_table("m_oral_examination_methods")
    op.drop_table("m_eras")
    # ### end Alembic commands ###
