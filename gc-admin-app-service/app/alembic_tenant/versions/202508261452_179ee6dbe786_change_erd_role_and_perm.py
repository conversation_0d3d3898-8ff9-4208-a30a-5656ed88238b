"""change erd role and perm

Revision ID: 179ee6dbe786
Revises: 957c283546e8
Create Date: 2025-08-26 14:52:05.921507

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "179ee6dbe786"
down_revision: Union[str, None] = "957c283546e8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "roles",
        sa.Column(
            "role_key_id",
            sa.Integer(),
            autoincrement=False,
            nullable=False,
            comment="1: CLINIC_ADMIN, 3: CLINIC_STAFF, Enums: RoleKeyEnum",
        ),
    )

    op.add_column(
        "doctor_roles",
        sa.Column(
            "role_key_id",
            sa.Integer(),
            nullable=False,
            comment="Reference to roles.role_key_id",
        ),
    )
    op.drop_constraint(
        op.f("doctor_roles_role_id_fkey"), "doctor_roles", type_="foreignkey"
    )
    op.drop_column("doctor_roles", "role_id")

    op.add_column(
        "role_permissions",
        sa.Column(
            "role_key_id",
            sa.Integer(),
            nullable=False,
            comment="Reference to roles.role_key_id",
        ),
    )
    op.drop_constraint(
        op.f("role_permissions_role_id_fkey"), "role_permissions", type_="foreignkey"
    )
    op.drop_column("role_permissions", "role_id")

    op.drop_constraint("roles_pkey", "roles", type_="primary")
    op.create_primary_key("roles_pkey", "roles", ["role_key_id"])

    op.create_foreign_key(
        "fk_doctor_roles_role_key",
        "doctor_roles",
        "roles",
        ["role_key_id"],
        ["role_key_id"],
        ondelete="RESTRICT",
    )

    op.create_foreign_key(
        "fk_role_permissions_role_key",
        "role_permissions",
        "roles",
        ["role_key_id"],
        ["role_key_id"],
        ondelete="RESTRICT",
    )

    op.drop_column("roles", "id")
    op.drop_column("roles", "role_key")


def downgrade() -> None:
    op.add_column(
        "roles",
        sa.Column("id", sa.Integer(), sa.Identity(always=False), nullable=False),
    )

    op.drop_constraint(
        "fk_role_permissions_role_key", "role_permissions", type_="foreignkey"
    )
    op.drop_constraint("fk_doctor_roles_role_key", "doctor_roles", type_="foreignkey")

    op.drop_constraint("roles_pkey", "roles", type_="primary")
    op.create_primary_key("roles_pkey", "roles", ["id"])

    op.add_column("role_permissions", sa.Column("role_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        op.f("role_permissions_role_id_fkey"),
        "role_permissions",
        "roles",
        ["role_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    op.drop_column("role_permissions", "role_key_id")

    op.add_column("doctor_roles", sa.Column("role_id", sa.Integer(), nullable=False))
    op.create_foreign_key(
        op.f("doctor_roles_role_id_fkey"),
        "doctor_roles",
        "roles",
        ["role_id"],
        ["id"],
    )
    op.drop_column("doctor_roles", "role_key_id")

    op.add_column(
        "roles",
        sa.Column(
            "role_key",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="1: admin, 2: doctor, 3: patient",
        ),
    )
    op.alter_column("roles", "role_key", nullable=False)
    op.drop_column("roles", "role_key_id")
