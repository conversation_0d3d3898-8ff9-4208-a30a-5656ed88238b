"""Init model permission

Revision ID: 52e63d0cc027
Revises: ecdcbc8f8593
Create Date: 2025-08-20 17:27:17.421714

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "52e63d0cc027"  # pragma: allowlist secret
down_revision: Union[str, None] = "ecdcbc8f8593"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "permissions",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "module",
            sa.String(length=255),
            nullable=True,
            comment="Module Screen Name allowed to perform",
        ),
        sa.Column(
            "sub_module",
            sa.String(length=255),
            nullable=True,
            comment="Sub Module Screen Name allowed to perform",
        ),
        sa.Column(
            "action", sa.String(length=30), nullable=True, comment="Action for module"
        ),
        sa.Column(
            "permission_key",
            sa.String(length=255),
            nullable=True,
            comment="key permission, Example: Notification:view",
        ),
        sa.Column("delete_flag", sa.Boolean(), nullable=True, comment="Delete flag"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "doctor_roles",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("doctor_user_id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column("delete_flag", sa.Boolean(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "role_permissions",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=True, comment="Reference to role"),
        sa.Column(
            "permission_id",
            sa.Integer(),
            nullable=True,
            comment="Reference to permission",
        ),
        sa.Column("delete_flag", sa.Boolean(), nullable=True, comment="Delete flag"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["permission_id"], ["permissions.id"], ondelete="RESTRICT"
        ),
        sa.ForeignKeyConstraint(["role_id"], ["roles.id"], ondelete="RESTRICT"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "roles",
        sa.Column(
            "role_key",
            sa.Integer(),
            nullable=False,
            comment="1: admin, 2: doctor, 3: patient",
        ),
    )
    op.add_column("roles", sa.Column("is_system", sa.Boolean(), nullable=False))
    op.add_column("roles", sa.Column("delete_flag", sa.Boolean(), nullable=True))
    op.alter_column(
        "roles",
        "name_json",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        comment="Master name for multiple languages",
        existing_comment="Master name",
        existing_nullable=True,
    )
    op.drop_constraint("roles_sort_key", "roles", type_="unique")
    op.drop_column("roles", "sort")
    op.drop_column("roles", "role_scope")
    op.drop_column("roles", "level")
    op.add_column(
        "tenant_configuration",
        sa.Column("default_storage", sa.Integer(), nullable=True),
    )
    op.add_column(
        "tenant_configuration", sa.Column("extra_storage", sa.Integer(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenant_configuration", "extra_storage")
    op.drop_column("tenant_configuration", "default_storage")
    op.add_column(
        "roles",
        sa.Column(
            "level",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Level Of Role",
        ),
    )
    op.add_column(
        "roles",
        sa.Column(
            "role_scope",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="1: doctor, 2: patient",
        ),
    )
    op.add_column(
        "roles",
        sa.Column(
            "sort", sa.INTEGER(), autoincrement=False, nullable=True, comment="Sort"
        ),
    )
    op.create_unique_constraint("roles_sort_key", "roles", ["sort"])
    op.alter_column(
        "roles",
        "name_json",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        comment="Master name",
        existing_comment="Master name for multiple languages",
        existing_nullable=True,
    )
    op.drop_column("roles", "delete_flag")
    op.drop_column("roles", "is_system")
    op.drop_column("roles", "role_key")
    op.drop_table("role_permissions")
    op.drop_table("doctor_roles")
    op.drop_table("permissions")
    # ### end Alembic commands ###
