"""add_unique_reservation_id_tbl_patient_waiting

Revision ID: b083632faba0
Revises: cbb787cd1a82
Create Date: 2025-07-24 09:07:56.745765

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b083632faba0"
down_revision: Union[str, None] = "cbb787cd1a82"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "uq_patient_waitings_reservation_id", "patient_waitings", ["reservation_id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uq_patient_waitings_reservation_id", "patient_waitings", type_="unique"
    )
    # ### end Alembic commands ###
