"""init_table_postal_code_and_prefecture

Revision ID: e1c2093ea923
Revises: 0b8135177728
Create Date: 2025-07-08 16:58:20.254857

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "e1c2093ea923"
down_revision: Union[str, None] = "0b8135177728"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_postal_code",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("postal_code", sa.String(length=20), nullable=False),
        sa.Column("pref_name", sa.String(length=64), nullable=False),
        sa.Column("pref_type", sa.String(length=32), nullable=True),
        sa.Column("pref_code", sa.String(length=16), nullable=True),
        sa.Column("city_name", sa.String(length=64), nullable=True),
        sa.Column("city_code", sa.String(length=16), nullable=True),
        sa.Column("town", sa.String(length=128), nullable=True),
        sa.Column("del_flag", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "m_prefecture",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name_json", sa.JSON(), nullable=False),
        sa.Column("sort", sa.Integer(), nullable=True),
        sa.Column("del_flag", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "patient_profiles",
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
    )
    op.add_column("patient_profiles", sa.Column("era_id", sa.Integer(), nullable=True))
    op.add_column(
        "patient_profiles",
        sa.Column(
            "home_phone",
            sa.BigInteger(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "home_phone_hash",
            sa.String(),
            nullable=True,
            comment="Home Phone hash, sha-256",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column("parent_name", sa.String(), nullable=True),
    )
    op.add_column("patient_profiles", sa.Column("gender", sa.Integer(), nullable=True))
    op.add_column(
        "patient_profiles",
        sa.Column(
            "date_of_birth",
            sa.String(),
            nullable=True,
            comment="Data encrypted, ensure original length max 255",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "date_of_birth_hash",
            sa.String(),
            nullable=True,
            comment="Date of birth hash, sha-256",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column("last_name_kana", sa.String(), nullable=True),
    )
    op.add_column(
        "patient_profiles",
        sa.Column("first_name_kana", sa.String(), nullable=True),
    )
    op.add_column(
        "patient_profiles", sa.Column("created_by", sa.String(), nullable=True)
    )
    op.add_column(
        "patient_profiles", sa.Column("updated_by", sa.String(), nullable=True)
    )
    op.alter_column(
        "patient_profiles",
        "postal_code",
        existing_type=sa.VARCHAR(length=255),
        type_=sa.String(length=8),
        existing_comment="Data encrypted, ensure original length max 8",
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "phone",
        existing_type=sa.INTEGER(),
        type_=sa.BigInteger(),
        comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "email",
        existing_type=sa.VARCHAR(),
        comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.create_foreign_key(
        None, "patient_profiles", "patient_users", ["patient_user_id"], ["id"]
    )
    op.create_foreign_key(None, "patient_profiles", "m_eras", ["era_id"], ["id"])
    op.drop_column("patient_profiles", "avatar_url")
    op.drop_column("patient_profiles", "country_code")
    op.drop_column("patient_profiles", "gender_id")
    op.drop_column("patient_profiles", "extra_data")
    op.drop_column("patient_profiles", "birthday_hash")
    op.drop_column("patient_profiles", "birthday")
    op.add_column("patient_users", sa.Column("is_adult", sa.Boolean(), nullable=False))
    op.alter_column(
        "patient_waitings",
        "status",
        existing_type=sa.INTEGER(),
        comment="status",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_waitings",
        "status",
        existing_type=sa.INTEGER(),
        comment=None,
        existing_comment="status",
        existing_nullable=False,
    )
    op.drop_column("patient_users", "is_adult")
    op.add_column(
        "patient_profiles",
        sa.Column(
            "birthday",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Data encrypted, ensure original length max 10",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "birthday_hash",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Birthday hash, sha-256",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "extra_data",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "gender_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="Gender id",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "country_code",
            sa.VARCHAR(length=10),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column("avatar_url", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "patient_profiles", type_="foreignkey")
    op.drop_constraint(None, "patient_profiles", type_="foreignkey")
    op.alter_column(
        "patient_profiles",
        "email",
        existing_type=sa.VARCHAR(),
        comment=None,
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "phone",
        existing_type=sa.BigInteger(),
        type_=sa.INTEGER(),
        comment=None,
        existing_comment="Data encrypted, ensure original length max 255",
        existing_nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "postal_code",
        existing_type=sa.String(length=8),
        type_=sa.VARCHAR(length=255),
        existing_comment="Data encrypted, ensure original length max 8",
        existing_nullable=True,
    )
    op.drop_column("patient_profiles", "updated_by")
    op.drop_column("patient_profiles", "created_by")
    op.drop_column("patient_profiles", "first_name_kana")
    op.drop_column("patient_profiles", "last_name_kana")
    op.drop_column("patient_profiles", "date_of_birth_hash")
    op.drop_column("patient_profiles", "date_of_birth")
    op.drop_column("patient_profiles", "gender")
    op.drop_column("patient_profiles", "parent_name")
    op.drop_column("patient_profiles", "home_phone_hash")
    op.drop_column("patient_profiles", "home_phone")
    op.drop_column("patient_profiles", "era_id")
    op.drop_column("patient_profiles", "patient_user_id")
    op.drop_table("m_prefecture")
    op.drop_table("m_postal_code")
    # ### end Alembic commands ###
