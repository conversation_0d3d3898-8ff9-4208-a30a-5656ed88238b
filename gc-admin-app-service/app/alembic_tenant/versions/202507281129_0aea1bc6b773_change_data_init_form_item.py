"""change data init form item

Revision ID: 0aea1bc6b773
Revises: 2dc54c0f268a
Create Date: 2025-07-28 11:29:56.711692

"""

import json
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0aea1bc6b773"  # pragma: allowlist secret
down_revision: Union[str, None] = "2dc54c0f268a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()

    updates = [
        {
            "label": "お名前",
            "extra_data": {"options": ["姓", "名"]},
        },
        {
            "label": "フリガナ",
            "extra_data": {"options": ["姓(フリガナ)", "名(フリガナ)"]},
        },
        {
            "label": "携帯番号",
            "extra_data": {"options": ["男性", "女性"]},
        },
        {
            "label": "生年月日",
            "extra_data": {"options": ["日付を入力してください"]},
        },
        {
            "label": "性別",
            "extra_data": {"options": ["携帯電話番号を入力してくだ"]},
        },
    ]

    for item in updates:
        conn.execute(
            sa.text(
                """
                UPDATE form_items
                SET extra_data = CAST(:extra_data AS JSONB)
                WHERE label = :label
            """
            ),
            {
                "label": item["label"],
                "extra_data": json.dumps(item["extra_data"]),
            },
        )


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
