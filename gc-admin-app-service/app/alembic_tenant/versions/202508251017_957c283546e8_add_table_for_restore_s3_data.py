"""Add table for restore s3 data

Revision ID: 957c283546e8
Revises: 935eee5b0a0d
Create Date: 2025-08-25 10:17:14.471593

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "957c283546e8"  # pragma: allowlist secret
down_revision: Union[str, None] = "935eee5b0a0d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "restore_s3_data",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "s3_url",
            sa.String(),
            nullable=False,
            comment="Original S3 URL of the object",
        ),
        sa.Column(
            "s3_url_temp",
            sa.String(),
            nullable=True,
            comment="Temporary S3 URL for restored object",
        ),
        sa.Column(
            "document_uuid",
            sa.String(),
            nullable=False,
            comment="UUID represents a group of documents with multiple versions.",
        ),
        sa.Column(
            "version_id",
            sa.Integer(),
            nullable=False,
            comment="The version corresponding to a group of documents. Used for generating the S3 URL",
        ),
        sa.Column(
            "status",
            sa.Integer(),
            server_default="2",
            nullable=False,
            comment="Status of the restoration process",
        ),
        sa.Column(
            "expires_at",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Expiration date of the restored object",
        ),
        sa.Column(
            "restored_at",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Time when the object was restored",
        ),
        sa.Column(
            "requested_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            comment="Time when the restoration was requested",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("restore_s3_data")
    # ### end Alembic commands ###
