"""remove_tbl_document_management_version

Revision ID: 4924b324f127
Revises: dd78c47884ae
Create Date: 2025-08-20 17:29:36.335573

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "4924b324f127"
down_revision: Union[str, None] = "dd78c47884ae"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("document_management_versions")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_management_versions",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "document_management_id", sa.INTEGER(), autoincrement=False, nullable=False
        ),
        sa.Column(
            "document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
            comment="Data of the document",
        ),
        sa.Column(
            "version",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Version of the document",
        ),
        sa.Column(
            "data_type",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Type of the document. Enums: DocumentType",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "created_by",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the user who created the record",
        ),
        sa.Column(
            "updated_by",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the user who updated the record",
        ),
        sa.Column(
            "deleted_by",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the user who deleted the record",
        ),
        sa.Column("patient_user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "version_uuid",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="UUID of the version",
        ),
        sa.Column(
            "preview_document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="Preview(thumbnail) data of the document",
        ),
        sa.ForeignKeyConstraint(
            ["document_management_id"],
            ["document_managements.id"],
            name="document_management_versions_document_management_id_fkey",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="document_management_versions_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="document_management_versions_pkey"),
        sa.UniqueConstraint(
            "document_management_id",
            "version",
            name="uq_document_management_versions_document_management_id_version",
        ),
    )
    # ### end Alembic commands ###
