"""add table patient fcm tokens

Revision ID: 935eee5b0a0d
Revises: a89851beccdd
Create Date: 2025-08-22 15:35:57.536528

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "935eee5b0a0d"
down_revision: Union[str, None] = "a89851beccdd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "patient_fcm_tokens",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "fcm_token",
            sa.String(),
            nullable=False,
            comment="Firebase Cloud Messaging token",
        ),
        sa.Column(
            "is_active",
            sa.<PERSON>(),
            nullable=True,
            comment="Indicates if the FCM token is active",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("patient_fcm_tokens")
    # ### end Alembic commands ###
