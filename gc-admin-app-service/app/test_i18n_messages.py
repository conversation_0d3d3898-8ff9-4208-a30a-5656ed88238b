#!/usr/bin/env python3
"""
Test script để kiểm tra việc thiết lập đa ngôn ngữ cho CustomMessageCode
"""
import os
import sys
from pathlib import Path

# Thêm đường dẫn để import được các module
sys.path.append(str(Path(__file__).parent.parent.parent))

from gc_dentist_shared.core.common.i18n import i18n
from gettext import GNUTranslations
from core.messages import CustomMessageCode


def load_translations():
    """Load translations cho Japanese"""
    locale_dir = Path(__file__).parent / "locale"
    ja_mo_file = locale_dir / "ja_JP" / "LC_MESSAGES" / "messages.mo"
    
    if ja_mo_file.exists():
        with open(ja_mo_file, 'rb') as f:
            translation = GNUTranslations(f)
        
        # Load translations vào i18n instance
        i18n.load_translations({"ja_JP": translation})
        print("✅ Đã load translations thành công!")
        return True
    else:
        print(f"❌ Không tìm thấy file: {ja_mo_file}")
        return False


def test_messages():
    """Test các message codes với ngôn ngữ khác nhau"""
    
    print("\n=== TEST MESSAGES ===")
    
    # Test với English (default)
    i18n.set_language("en_US")
    print(f"\n🇺🇸 English:")
    print(f"X_TENANT_SLUG_IS_REQUIRED: {CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title}")
    print(f"CLINIC_NOT_FOUND: {CustomMessageCode.CLINIC_NOT_FOUND.title}")
    print(f"TENANT_NOT_FOUND: {CustomMessageCode.TENANT_NOT_FOUND.title}")
    
    # Test với Japanese
    i18n.set_language("ja_JP")
    print(f"\n🇯🇵 Japanese:")
    print(f"X_TENANT_SLUG_IS_REQUIRED: {CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title}")
    print(f"CLINIC_NOT_FOUND: {CustomMessageCode.CLINIC_NOT_FOUND.title}")
    print(f"TENANT_NOT_FOUND: {CustomMessageCode.TENANT_NOT_FOUND.title}")


def test_api_response_simulation():
    """Mô phỏng API response như trong ví dụ của bạn"""
    
    print("\n=== SIMULATION API RESPONSE ===")
    
    # Giả lập request không có X-Tenant-Slug header
    i18n.set_language("ja_JP")
    
    print("\n📝 Mô phỏng API /slugs khi thiếu X-Tenant-Slug header:")
    error_response = {
        "message": CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title,
        "message_code": CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.code,
    }
    print(f"Response: {error_response}")
    
    print("\n📝 Mô phỏng API /slugs khi không tìm thấy clinic:")
    error_response_2 = {
        "data": {"slug": "test-clinic", "exists": False},
        "message": CustomMessageCode.CLINIC_NOT_FOUND.title,
    }
    print(f"Response: {error_response_2}")


if __name__ == "__main__":
    print("🚀 Bắt đầu test i18n cho CustomMessageCode...")
    
    # Load translations
    if load_translations():
        # Test messages
        test_messages()
        
        # Test API simulation
        test_api_response_simulation()
        
        print("\n✅ Test hoàn thành!")
    else:
        print("\n❌ Không thể load translations!")
