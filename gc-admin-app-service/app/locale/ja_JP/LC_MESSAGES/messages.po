# Japanese (Japan) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-08-29 11:22+0700\n"
"PO-Revision-Date: 2025-08-29 11:27+0700\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: ja_JP <<EMAIL>>\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.17.0\n"

#: api/health_check.py:10
msgid "OK"
msgstr "わかりました"

#: core/messages.py:56
msgid "X-Tenant-Slug header is required!"
msgstr "X-Tenant-Slugヘッダーが必要です！"

#: core/messages.py:82
msgid "Clinic not found!"
msgstr "クリニックが見つかりません。"

#: core/messages.py:62
msgid "Tenant not found!"
msgstr "テナントが見つかりません！"

#: core/messages.py:68
msgid "Clinic information not found!"
msgstr "クリニック情報が見つかりません！"

#: core/messages.py:72
msgid "Clinic created successfully!"
msgstr "クリニックが正常に作成されました！"

#: core/messages.py:77
msgid "Clinic creation failed!"
msgstr "クリニックの作成に失敗しました！"

#: core/messages.py:36
msgid "Unknown error!"
msgstr "不明なエラーです！"

#: core/messages.py:40
msgid "Twilio error!"
msgstr "Twilioエラーです！"

#: core/messages.py:44
msgid "Twilio send message error!"
msgstr "Twilioメッセージ送信エラーです！"

#: core/messages.py:49
msgid "S3 bucket error!"
msgstr "S3バケットエラーです！"
