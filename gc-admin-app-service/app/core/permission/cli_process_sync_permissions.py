import argparse
import asyncio

from core.permission.process_sync_permissions import process_sync_tenant

from gc_dentist_shared.core.logger.config import log

# ================================================================
# CLI tool for syncing roles and permissions across tenants
#
# Usage:
#   PYTHONPATH=$PYTHONPATH:$(pwd)
#   python core/permission/cli_process_sync_permissions.py <plan_key_id> <tenant1> <tenant2> ...
#
# Example:
#   python cli_process_sync_permissions.py 1 tenant_template acd
#
# Arguments:
#   plan_key_id   : (int) The plan key ID (e.g., 1, 2, 3...)
#   tenants       : One or more tenant database names
#
# Options:
#   -h, --help    : Show this help message and exit
#
# ================================================================


def main():
    parser = argparse.ArgumentParser(
        description="Sync roles and permissions for tenants."
    )
    parser.add_argument("plan_key_id", type=int, help="The plan key ID (integer).")
    parser.add_argument("tenants", nargs="+", help="List of tenant database names.")

    args = parser.parse_args()

    log.info(f"📋 Using plan_key_id: {args.plan_key_id}")
    log.info(f"🔄 Processing tenants: {', '.join(args.tenants)}")

    asyncio.run(process_sync_tenant(args.tenants, args.plan_key_id))


if __name__ == "__main__":
    main()
