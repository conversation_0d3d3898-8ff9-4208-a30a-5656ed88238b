#!/usr/bin/env python3
"""
Script tự động apply bản dịch cho CustomMessageCode vào file .po
"""
import polib
from pathlib import Path
from typing import Dict


class AutoTranslator:
    def __init__(self, base_dir: Path):
        self.base_dir = base_dir
        self.locale_dir = base_dir / "locale"
        
        # Dictionary chứa các bản dịch mặc định
        self.translations = {
            'ja_JP': {
                # Server Errors
                "Unknown error!": "不明なエラーです！",
                "Twilio error!": "Twilioエラーです！",
                "Twilio send message error!": "Twilioメッセージ送信エラーです！",
                "S3 bucket error!": "S3バケットエラーです！",
                
                # Tenant/Clinic Messages
                "X-Tenant-Slug header is required!": "X-Tenant-Slugヘッダーが必要です！",
                "Tenant not found!": "テナントが見つかりません！",
                "Clinic information not found!": "クリニック情報が見つかりません！",
                "Clinic created successfully!": "クリニックが正常に作成されました！",
                "Clinic creation failed!": "クリニックの作成に失敗しました！",
                "Clinic not found!": "クリニックが見つかりません。",
                
                # Validation Errors
                "Invalid date provided!": "無効な日付が提供されました！",
                "Invalid end date!": "無効な終了日です！",
                
                # File Upload Errors
                "File upload failed!": "ファイルのアップロードに失敗しました！",
                "Invalid file format or content file error!": "無効なファイル形式またはコンテンツファイルエラーです！",
                "Unsupported source!": "サポートされていないソースです！",
                "Invalid device type!": "無効なデバイスタイプです！",
                "Missing data ID!": "データIDが不足しています！",
                "File upload successfully": "ファイルのアップロードが成功しました",
                "Invalid image file type!": "無効な画像ファイルタイプです！",
                "Missing business number!": "事業者番号が不足しています！",
                
                # Process File Errors
                "File already processed!": "ファイルは既に処理されています！",
                "Device configuration not found!": "デバイス設定が見つかりません！",
                "Tenant DB URI is missing!": "テナントDB URIが不足しています！",
                
                # Common
                "OK": "わかりました",
            },
            'en_US': {
                # English は default nên không cần translate
            }
        }
    
    def apply_translations_to_po(self, language: str):
        """Apply bản dịch vào file .po"""
        po_file_path = self.locale_dir / language / "LC_MESSAGES" / "messages.po"
        
        if not po_file_path.exists():
            print(f"❌ File không tồn tại: {po_file_path}")
            return False
            
        try:
            # Load PO file
            po = polib.pofile(str(po_file_path))
            
            # Get translations cho ngôn ngữ này
            lang_translations = self.translations.get(language, {})
            
            if not lang_translations:
                print(f"⚠️ Không có bản dịch cho ngôn ngữ: {language}")
                return True
            
            # Apply translations
            updated_count = 0
            for entry in po:
                if not entry.translated() and entry.msgid in lang_translations:
                    entry.msgstr = lang_translations[entry.msgid]
                    updated_count += 1
                    print(f"✅ Translated: '{entry.msgid}' → '{entry.msgstr}'")
            
            # Save file
            po.save()
            print(f"✅ Updated {updated_count} translations in {language}")
            return True
            
        except Exception as e:
            print(f"❌ Error processing {language}: {e}")
            return False
    
    def apply_all_translations(self, languages: list = None):
        """Apply translations cho tất cả ngôn ngữ"""
        if languages is None:
            languages = ['ja_JP', 'en_US']
            
        for lang in languages:
            print(f"\n🌍 Processing {lang}...")
            self.apply_translations_to_po(lang)


def main():
    """Main function"""
    base_dir = Path(__file__).parent.parent.parent
    translator = AutoTranslator(base_dir)
    
    print("🚀 Bắt đầu apply translations...")
    translator.apply_all_translations(['ja_JP'])
    print("✅ Hoàn thành!")


if __name__ == "__main__":
    main()
