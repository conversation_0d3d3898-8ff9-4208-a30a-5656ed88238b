import time

from configuration.settings import configuration

from gc_dentist_shared.core.common.hmac import generate_hmac_signature


def generate_hmac_header() -> dict:
    x_timestamp = str(int(time.time()))
    x_signature = generate_hmac_signature(
        x_timestamp, configuration.COMMUNICATE_SECRET_KEY
    )
    return {
        "X-Signature": x_signature,
        "X-Timestamp": x_timestamp,
    }
