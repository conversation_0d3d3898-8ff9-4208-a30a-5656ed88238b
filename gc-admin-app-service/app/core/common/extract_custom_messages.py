#!/usr/bin/env python3
"""
Script tự động extract messages từ CustomMessageCode và tạo file .pot, .po, .mo
"""
import ast
import os
import polib
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple


class CustomMessageExtractor:
    def __init__(self, base_dir: Path):
        self.base_dir = base_dir
        self.locale_dir = base_dir / "locale"
        self.messages_pot = self.locale_dir / "messages.pot"
        
    def extract_custom_message_codes(self, file_path: Path) -> List[Tuple[str, int, str]]:
        """
        Extract message codes từ CustomMessageCode enum
        Returns: List of (message_text, line_number, file_path)
        """
        messages = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef) and node.name == "CustomMessageCode":
                    for item in node.body:
                        if isinstance(item, ast.Assign):
                            # Tìm các assignment như: MESSAGE_NAME = (code, "title", "description")
                            for target in item.targets:
                                if isinstance(target, ast.Name):
                                    if isinstance(item.value, ast.Tuple) and len(item.value.elts) >= 2:
                                        # Lấy title (element thứ 2)
                                        title_node = item.value.elts[1]
                                        if isinstance(title_node, ast.Constant):
                                            message_text = title_node.value
                                            line_number = item.lineno
                                            relative_path = file_path.relative_to(self.base_dir)
                                            messages.append((message_text, line_number, str(relative_path)))
                                            
        except Exception as e:
            print(f"❌ Error parsing {file_path}: {e}")
            
        return messages
    
    def create_pot_file(self, messages: List[Tuple[str, int, str]]):
        """Tạo file .pot từ danh sách messages"""
        
        # Tạo thư mục locale nếu chưa có
        self.locale_dir.mkdir(exist_ok=True)
        
        # Tạo POT file
        pot = polib.POFile()
        pot.metadata = {
            'Project-Id-Version': 'PROJECT VERSION',
            'Report-Msgid-Bugs-To': 'EMAIL@ADDRESS',
            'POT-Creation-Date': '2025-08-29 11:27+0700',
            'PO-Revision-Date': 'YEAR-MO-DA HO:MI+ZONE',
            'Last-Translator': 'FULL NAME <EMAIL@ADDRESS>',
            'Language-Team': 'LANGUAGE <<EMAIL>>',
            'MIME-Version': '1.0',
            'Content-Type': 'text/plain; charset=utf-8',
            'Content-Transfer-Encoding': '8bit',
            'Generated-By': 'Custom Message Extractor 1.0',
        }
        
        # Thêm messages vào POT
        for message_text, line_number, file_path in messages:
            entry = polib.POEntry(
                msgid=message_text,
                msgstr='',
                occurrences=[(file_path, line_number)]
            )
            pot.append(entry)
        
        # Lưu POT file
        pot.save(str(self.messages_pot))
        print(f"✅ Created POT file: {self.messages_pot}")
        
    def update_po_files(self, languages: List[str] = None):
        """Update các file .po từ .pot"""
        if languages is None:
            languages = ['ja_JP', 'en_US']
            
        for lang in languages:
            po_dir = self.locale_dir / lang / "LC_MESSAGES"
            po_file = po_dir / "messages.po"
            
            if not po_file.exists():
                # Tạo mới nếu chưa có
                self.init_po_file(lang)
            else:
                # Update existing
                self.update_existing_po_file(lang)
    
    def init_po_file(self, language: str):
        """Tạo file .po mới cho ngôn ngữ"""
        cmd = [
            "pybabel", "init",
            "-i", str(self.messages_pot),
            "-d", str(self.locale_dir),
            "-l", language
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.base_dir)
            if result.returncode == 0:
                print(f"✅ Initialized {language} locale")
            else:
                print(f"❌ Error initializing {language}: {result.stderr}")
        except Exception as e:
            print(f"❌ Error running pybabel init: {e}")
    
    def update_existing_po_file(self, language: str):
        """Update file .po hiện có"""
        cmd = [
            "pybabel", "update",
            "-i", str(self.messages_pot),
            "-d", str(self.locale_dir),
            "-l", language
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.base_dir)
            if result.returncode == 0:
                print(f"✅ Updated {language} locale")
            else:
                print(f"❌ Error updating {language}: {result.stderr}")
        except Exception as e:
            print(f"❌ Error running pybabel update: {e}")
    
    def compile_mo_files(self, languages: List[str] = None):
        """Compile các file .po thành .mo"""
        if languages is None:
            languages = ['ja_JP', 'en_US']
            
        for lang in languages:
            cmd = [
                "pybabel", "compile",
                "-d", str(self.locale_dir),
                "-l", lang
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.base_dir)
                if result.returncode == 0:
                    print(f"✅ Compiled {lang} messages")
                else:
                    print(f"❌ Error compiling {lang}: {result.stderr}")
            except Exception as e:
                print(f"❌ Error running pybabel compile: {e}")
    
    def run_full_extraction(self, languages: List[str] = None):
        """Chạy toàn bộ quy trình extraction"""
        print("🚀 Bắt đầu extract CustomMessageCode...")
        
        # 1. Extract messages từ CustomMessageCode
        messages_file = self.base_dir / "core" / "messages.py"
        messages = self.extract_custom_message_codes(messages_file)
        
        if not messages:
            print("❌ Không tìm thấy messages nào trong CustomMessageCode")
            return
            
        print(f"📝 Tìm thấy {len(messages)} messages")
        
        # 2. Tạo POT file
        self.create_pot_file(messages)
        
        # 3. Update PO files
        self.update_po_files(languages)
        
        # 4. Compile MO files
        self.compile_mo_files(languages)
        
        print("✅ Hoàn thành extraction!")


def main():
    """Main function"""
    base_dir = Path(__file__).parent.parent.parent
    extractor = CustomMessageExtractor(base_dir)
    
    # Chạy với Japanese và English
    extractor.run_full_extraction(['ja_JP', 'en_US'])


if __name__ == "__main__":
    main()
