@echo off
REM Batch script for running Babel internationalization commands
REM Usage: babel-commands.bat <command> [language] [directory] [ignore-dirs]
REM Commands: extract, init, update, compile

if "%1"=="" (
    echo Usage: babel-commands.bat ^<command^> [language] [directory] [ignore-dirs]
    echo Commands: extract, init, update, compile
    echo Examples:
    echo   babel-commands.bat extract . venv
    echo   babel-commands.bat init en_US
    echo   babel-commands.bat update
    echo   babel-commands.bat compile ja_JP
    exit /b 1
)

set COMMAND=%1
set LANGUAGE=%2
set DIRECTORY=%3
set IGNORE_DIRS=%4

REM Set default values
if "%LANGUAGE%"=="" set LANGUAGE=ja_JP
if "%DIRECTORY%"=="" set DIRECTORY=.

REM Set up Python path to include the root directory
for %%i in ("%~dp0..") do set ROOT_DIR=%%~fi
set PYTHONPATH=%PYTHONPATH%;%ROOT_DIR%

REM Change to the app directory
cd /d "%~dp0app"

echo Running babel %COMMAND% command...

if "%COMMAND%"=="extract" (
    if "%IGNORE_DIRS%"=="" (
        python core/common/babel_cli.py extract -d %DIRECTORY%
    ) else (
        python core/common/babel_cli.py extract -d %DIRECTORY% --ignore-dirs %IGNORE_DIRS%
    )
) else if "%COMMAND%"=="init" (
    python core/common/babel_cli.py init -l %LANGUAGE%
) else if "%COMMAND%"=="update" (
    python core/common/babel_cli.py update
) else if "%COMMAND%"=="compile" (
    python core/common/babel_cli.py compile -l %LANGUAGE%
) else (
    echo Invalid command: %COMMAND%
    echo Valid commands: extract, init, update, compile
    exit /b 1
)

if %ERRORLEVEL% equ 0 (
    echo Babel %COMMAND% command completed successfully!
) else (
    echo Babel %COMMAND% command failed with exit code %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)
