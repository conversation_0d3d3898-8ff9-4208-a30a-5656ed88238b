# PowerShell script for running Babel internationalization commands
# Usage: .\babel-commands.ps1 <command> [options]
# Commands: extract, init, update, compile

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("extract", "init", "update", "compile")]
    [string]$Command,
    
    [string]$Language = "ja_JP",
    [string]$Directory = ".",
    [string]$IgnoreDirs = $null
)

# Set up the Python path to include the root directory
$rootDir = Split-Path -Parent $PSScriptRoot
$env:PYTHONPATH = "$env:PYTHONPATH;$rootDir"

# Change to the app directory
Set-Location "$PSScriptRoot\app"

Write-Host "Running babel $Command command..." -ForegroundColor Green

switch ($Command) {
    "extract" {
        if ($IgnoreDirs) {
            & python core/common/babel_cli.py extract -d $Directory --ignore-dirs $IgnoreDirs
        } else {
            & python core/common/babel_cli.py extract -d $Directory
        }
    }
    "init" {
        & python core/common/babel_cli.py init -l $Language
    }
    "update" {
        & python core/common/babel_cli.py update
    }
    "compile" {
        & python core/common/babel_cli.py compile -l $Language
    }
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "Babel $Command command completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Babel $Command command failed with exit code $LASTEXITCODE" -ForegroundColor Red
}
