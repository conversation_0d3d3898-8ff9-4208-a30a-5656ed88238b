import asyncio
import os
import warnings
from datetime import datetime
from os.path import dirname, join
from pathlib import Path
from unittest.mock import patch

import psycopg2
import pytest
import pytest_asyncio
from alembic import command
from alembic.config import Config as AlembicConfig
from dotenv import load_dotenv
from httpx import ASGITransport, AsyncClient
from pydantic import PostgresDsn
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from starlette.middleware.base import BaseHTTPMiddleware

BASE_URL = dirname(__file__)
ENV_FILE = os.environ.get("USE_ENV_FILE", ".env")

if ENV_FILE and len(ENV_FILE) > 0:
    load_dotenv(join(BASE_URL, ENV_FILE), verbose=True, override=True)
else:
    load_dotenv(verbose=True, override=True)


POSTGRES_SERVER = os.getenv("POSTGRES_SERVER_UNIT_TEST")
POSTGRES_PORT = os.getenv("POSTGRES_PORT_UNIT_TEST")
POSTGRES_USER = os.getenv("POSTGRES_USER_UNIT_TEST")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD_UNIT_TEST")
DATABASE_NAME = (
    os.getenv("USE_DBNAME_UNIT_TEST")
    or f"payment_client_unittest_{datetime.now().strftime("%Y%m%d%H%M%S")}"
)


SQLALCHEMY_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=DATABASE_NAME,
    )
)

SQLALCHEMY_ASYNC_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql+asyncpg",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=DATABASE_NAME,
    )
)
print("\n DATABASE_NAME ", DATABASE_NAME)
print("\n SQLALCHEMY_ASYNC_DATABASE_URL ", SQLALCHEMY_ASYNC_DATABASE_URL)

warnings.filterwarnings("ignore", category=DeprecationWarning, module="swigvarlink")


@pytest.fixture(scope="session", autouse=True)
def override_db_url():
    from db.db_connection import Database

    Database._engine = None
    Database._sessionmaker = None

    test_url = URL.create(
        drivername="postgresql+asyncpg",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        database=DATABASE_NAME,
    )

    with patch.object(Database, "get_url", return_value=test_url):
        yield


@pytest_asyncio.fixture(scope="session")
def create_new_database():
    if os.getenv("USE_DBNAME_UNIT_TEST"):
        print(f"🔁 Skipping DB creation. Using existing database: {DATABASE_NAME}")
        return

    print(f"🛠️  Creating new test database: {DATABASE_NAME}")
    conn = psycopg2.connect(
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=POSTGRES_PORT,
    )
    conn.autocommit = True
    cur = conn.cursor()
    cur.execute(f"CREATE DATABASE {DATABASE_NAME};")
    cur.close()
    conn.close()

    return DATABASE_NAME


@pytest.fixture(scope="session")
def alembic_config(override_db_url, create_new_database):
    """Return Config Alembic With Database UnitTest"""
    base_dir_alembic = Path(__file__).resolve().parent.parent
    alembic_ini_path = str(base_dir_alembic / "alembic.ini")

    config = AlembicConfig(alembic_ini_path)
    config.set_main_option("script_location", str(base_dir_alembic / "alembic"))
    config.set_main_option("sqlalchemy.url", SQLALCHEMY_DATABASE_URL)

    return config


@pytest.fixture(scope="session", autouse=True)
def apply_migrations(alembic_config):
    """Run migration UnitTest Database"""
    print("🛠️  Applying migrations... ")
    command.upgrade(alembic_config, "head")


@pytest.fixture(scope="session")
def setup_test_db(apply_migrations):
    """Ensure database migrations are applied before any test"""
    yield


class DummyMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        return await call_next(request)


@pytest_asyncio.fixture(scope="session")
def app():
    from main import create_app

    app = create_app()
    return app


@pytest_asyncio.fixture(scope="module")
def anyio_backend():
    return "asyncio"


@pytest_asyncio.fixture(scope="session")
async def async_client(app):
    transport = ASGITransport(app=app, raise_app_exceptions=True)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac


@pytest_asyncio.fixture(scope="session")
async def async_db_session_object():
    async_engine = create_async_engine(
        SQLALCHEMY_ASYNC_DATABASE_URL,
        echo=True,
        future=True,
        pool_pre_ping=True,
    )
    async_session = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )

    yield async_session


@pytest_asyncio.fixture(scope="session")
def event_loop():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()
