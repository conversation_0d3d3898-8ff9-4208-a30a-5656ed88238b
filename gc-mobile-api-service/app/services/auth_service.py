from core.messages import CustomMessageCode
from services.common.validate_service import ValidateService
from sqlalchemy import select
from sqlalchemy.exc import DB<PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import PatientFCMToken


class AuthPatientService:
    def __init__(self, db_session: AsyncSession = None):
        self.db_session = db_session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_fcm_token",
    )
    async def create_or_update_fcm_token(self, fcm_token: str, patient_user_id: int):
        """
        Create or update the FCM token for the patient.
        """
        if not fcm_token:
            raise CustomValueError(
                message=CustomMessageCode.INVALID_FCM_TOKEN.title,
                message_code=CustomMessageCode.INVALID_FCM_TOKEN.code,
            )

        async with self.db_session.begin():
            await ValidateService().validate_patient_user(
                patient_id=patient_user_id,
                db_session=self.db_session,
            )
            fcm_token_obj = (
                await self.db_session.execute(
                    select(PatientFCMToken).where(
                        PatientFCMToken.patient_user_id == patient_user_id,
                        PatientFCMToken.fcm_token == fcm_token,
                    )
                )
            ).scalar_one_or_none()
            if not fcm_token_obj:
                fcm_token_obj = PatientFCMToken(
                    patient_user_id=patient_user_id,
                    fcm_token=fcm_token,
                )
                self.db_session.add(fcm_token_obj)

            fcm_token_obj.is_active = True
            await self.db_session.flush()

        return fcm_token_obj.id
