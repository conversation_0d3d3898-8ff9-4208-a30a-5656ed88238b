from core.messages import CustomMessageCode
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import PatientUser


class ValidateService:
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_patient_user",
    )
    async def validate_patient_user(self, patient_id: int, db_session: AsyncSession):
        """
        Validate if a patient user exists with the given ID.
        :param patient_id: ID of the patient user.
        :return: PatientUser object if exists, otherwise raises CustomValueError.
        """
        patient = await db_session.get(PatientUser, patient_id)
        if not patient:
            raise CustomValueError(
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
            )
        return True
