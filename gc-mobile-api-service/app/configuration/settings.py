from pathlib import Path
from typing import ClassVar, Literal, Optional

import jwt
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    BASE_DIR: ClassVar[Path] = Path(__file__).resolve().parent

    model_config = SettingsConfigDict(
        env_file=str(BASE_DIR / ".env"),
        env_file_encoding="utf-8",
        extra="ignore",
    )

    PROJECT_NAME: str
    ENVIRONMENT: Literal["unittest", "develop", "testing", "staging", "production"]

    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_GLOBAL_DB_NAME: str
    POSTGRES_PORT: int
    DB_ECHO: bool = False
    DB_INIT: bool = False

    READ_ONLY_POSTGRES_SERVER: str
    READ_ONLY_POSTGRES_USER: str
    READ_ONLY_POSTGRES_PASSWORD: str
    READ_ONLY_POSTGRES_GLOBAL_DB_NAME: str
    READ_ONLY_POSTGRES_PORT: int

    COMMUNICATE_SECRET_KEY: str

    AES_SECRET_ID_ROTATION: str
    AWS_SECRET_ROTATION_KEY_MAPPING: dict = {}
    AWS_SECRET_CURRENT_VERSION: str
    AES_SECRET_KEY_MAPPING: str

    AWS_REGION_NAME: str | None = None
    AWS_ACCESS_KEY_ID: str | None = None
    AWS_SECRET_ACCESS_KEY: str | None = None

    # Dentist URL
    SERVICE_DENTIST_URL: str = "http://localhost:8000"
    LOGIN_ENDPOINT: str = "/v1_0/auth/login"

    JWT_SECRET_KEY: str = "your_jwt_secret"
    JWT_ALGORITHM: str = "HS256"

    OAUTH_CODE_EXPIRE_MINUTES: int = 10
    TEMPORARY_TOKEN_EXPIRE_MINUTES: int = 5
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30

    DEFAULT_CLIENT_NAME_FOR_WEB: str = "Noda-Web"
    DEFAULT_CLIENT_NAME_FOR_APP: str = "Noda-App"

    # Auth configuration
    AUTH_SERVICE_JWKS_URL: str = "http://127.0.0.1:8000/.well-known/jwks.json"
    AUTH_SERVICE_JWKS_LIFESPAN: int = 300  # in seconds
    JWT_ALGORITHM: str = "HS256"

    TOKEN_EXCLUDE_URLS: list[str] = [
        "/docs",
        "/openapi.json",
        "/health",
    ]


def get_settings() -> Settings:
    """Read configuration optimization writing method"""
    return Settings()


# def load_jwks(url: str):
#     return jwt.PyJWKClient(url)


configuration = get_settings()
jwks_client: Optional[jwt.PyJWKClient | None] = None
