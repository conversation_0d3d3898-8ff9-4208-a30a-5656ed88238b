from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from db.db_connection import CentralDatabase, TenantDatabase
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    OAuth2Token,
    TenantClinic,
    TenantPatientUserMapping,
)
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import PatientUser


class JwtAuthService:
    """
    Service for handling JWT authentication.
    """

    async def get_db_name_by_tenant_uuid(
        self, central_session: AsyncSession, tenant_uuid: str
    ):
        """
        Get the database name associated with a tenant UUID.
        """
        query = select(TenantClinic.db_name).where(
            TenantClinic.tenant_uuid == tenant_uuid
        )
        result = await central_session.execute(query)
        db_name = result.scalar_one_or_none()

        if not db_name:
            log.error(f"❌ Database name not found for tenant UUID: {tenant_uuid}")
            return None
        return db_name

    async def validate_token(
        self,
        central_session: AsyncSession,
        access_token: str,
        user_id: int,
        tenant_uuid: str,
    ):
        """
        Validate the JWT token and return the decoded claims.
        """
        try:
            query = select(OAuth2Token).where(OAuth2Token.access_token == access_token)
            result = await central_session.execute(query)
            token = result.scalar_one_or_none()

            if not token:
                log.error("❌ JWT token not found in the database")
                return None

            if token.global_user_id == user_id or (
                token.tenant_uuid == tenant_uuid and token.clinic_patient_id == user_id
            ):
                return token
            log.error("❌ JWT token user ID does not match the provided user ID")
            return None
        except Exception as e:
            log.error(f"❌ Error validating JWT token: {e}")
            return None

    async def get_patient_user_in_global_user(
        self,
        central_session: AsyncSession,
        global_user_id: int,
        tenant_uuid: str,
    ):
        """
        Get the patient user associated with a global user ID.
        """
        query = select(TenantPatientUserMapping.patient_user_id).where(
            TenantPatientUserMapping.global_user_id == global_user_id,
            TenantPatientUserMapping.tenant_uuid == tenant_uuid,
            TenantPatientUserMapping.is_active.is_(True),
        )
        result = await central_session.execute(query)
        return result.scalar_one_or_none()

    async def validate_auth(
        self,
        access_token: str,
        role_id: int,
        user_id: int,
        tenant_uuid: str,
    ):
        """
        Validate the role ID against the tenant UUID.
        """
        central_session = await CentralDatabase().get_instance_db()
        try:
            async with central_session.begin():
                token = await self.validate_token(
                    central_session, access_token, user_id, tenant_uuid
                )
                if not token:
                    return None

                patient_id = token.clinic_patient_id
                if not patient_id:
                    patient_id = await self.get_patient_user_in_global_user(
                        central_session, user_id, tenant_uuid
                    )

                if not patient_id:
                    log.error("❌ Not able to find patient user!")
                    return None

                db_name = await self.get_db_name_by_tenant_uuid(
                    central_session, tenant_uuid
                )
                if not db_name:
                    return None

            token_db = set_current_db_name(db_name)
            tenant_session = await TenantDatabase.get_instance_tenant_db()
            try:
                async with tenant_session.begin():
                    result = await tenant_session.execute(
                        select(PatientUser).where(
                            PatientUser.id == patient_id,
                            PatientUser.status.is_(True),
                        )
                    )
                    patient_user = result.scalar_one_or_none()
                    if not patient_user:
                        log.error(
                            f"❌ User not found or inactive in tenant: {tenant_uuid}"
                        )
                        return None
                    # TODO Validate the role ID

                return patient_user
            except Exception as e:
                log.error(f"❌ Error setting current database name: {e}")
                return None
            finally:
                await tenant_session.close()
                reset_current_db_name(token_db)

        except Exception as e:
            log.error(f"❌ Error validating role ID: {e}")
            return None
        finally:
            await central_session.close()
