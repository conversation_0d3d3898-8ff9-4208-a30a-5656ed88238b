from typing import Optional
from uuid import UUID

from pydantic import BaseModel


class AppClientRequest(BaseModel):
    name: str
    client_id: str
    client_secret: str
    tenant_uuid: str
    metadata: dict


class AppClientResponse(BaseModel):
    # model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    client_id: str
    client_secret: str
    tenant_uuid: Optional[UUID]
    metadata: Optional[dict] = {}
