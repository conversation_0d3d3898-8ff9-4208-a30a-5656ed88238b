from typing import ClassVar, Optional

from core.constants import TOKEN_PREFIX
from pydantic import BaseModel, Field


class TenantInfoSchema(BaseModel):
    db_name: str
    tenant_uuid: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "db_name": "tenant_db",
                "tenant_uuid": "123e4567-e89b-12d3-a456-************",
            }
        }


class LoginRequest(BaseModel):
    tenant_slug: Optional[str] = None
    username: str
    password: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "username": "john_doe",
                "password": "secure_password",  # pragma: allowlist secret
            }  # pragma: allowlist secret
        }


class LoginOTPRequest(BaseModel):
    phone_number: str
    contry_code: str
    otp: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "phone_number": "09051234567",
                "contry_code": "+81",  # pragma: allowlist secret
                "otp": "123456",
            }  # pragma: allowlist secret
        }


class OAuthTokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = TOKEN_PREFIX
    expires_in: int

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "access_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "refresh_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "token_type": "bearer",
                "expires_in": 3600,
            }
        }


class TemporaryTokenResponse(BaseModel):
    user_id: int
    bearer_token: str = TOKEN_PREFIX
    expires_in: int

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "access_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "expires_in": 3600,
            }
        }


class RefreshTokenRequest(BaseModel):
    refresh_token: str = Field(..., description="Refresh token")
    client_id: str = Field(..., description="Client ID (OAuth client)")
    tenant_uuid: str = Field(..., description="Tenant UUID")

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "refresh_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "client_id": "client_id",
                "tenant_uuid": "tenant_uuid",
            }
        }


class OAuth2ConsentPayloads(BaseModel):
    client_id: str
    redirect_uri: str
    scope: str
    state: Optional[str]
