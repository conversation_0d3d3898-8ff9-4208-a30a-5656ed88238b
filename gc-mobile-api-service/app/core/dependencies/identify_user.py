from datetime import datetime, timezone
from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import ApiResponse
from core.constants import TOKEN_PREFIX
from core.messages import INVALID_TOKEN, USER_NOT_FOUND
from db.db_connection import CentralDatabase
from fastapi import Depends, Request, status
from jose import JW<PERSON><PERSON>r, jwt
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import GlobalUser


async def require_login(
    request: Request,
    session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
) -> GlobalUser:
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise ApiResponse.error(
            status_code=status.HTTP_401_UNAUTHORIZED, message=INVALID_TOKEN
        )

    token_str = auth_header[len(f"{TOKEN_PREFIX} ") :]

    try:
        payload = jwt.decode(
            token_str,
            configuration.JWT_SECRET_KEY,
            algorithms=[configuration.JWT_ALGORITHM],
        )
        sub = payload.get("sub")
        tenant_uuid = payload.get("tenant_uuid")
        exp = payload.get("exp")
        if not sub or not tenant_uuid:
            raise ApiResponse.error(
                status_code=status.HTTP_401_UNAUTHORIZED, message=INVALID_TOKEN
            )
        if datetime.now(timezone.utc).timestamp() > exp:
            raise ApiResponse.error(
                status_code=status.HTTP_401_UNAUTHORIZED, message=INVALID_TOKEN
            )
    except JWTError:
        raise ApiResponse.error(
            status_code=status.HTTP_401_UNAUTHORIZED, message=INVALID_TOKEN
        )

    # result = await session.execute(
    #     select(OAuth2Token).where(OAuth2Token.access_token == token_str)
    # )
    # token_in_db = result.scalar_one_or_none()
    # if not token_in_db or token_in_db.revoked:
    #     raise ApiResponse.error(status_code=status.HTTP_401_UNAUTHORIZED, message=INVALID_TOKEN_IS_REVOKED)

    result = await session.execute(
        select(GlobalUser).where(GlobalUser.id == sub, GlobalUser.is_active.is_(True))
    )
    user = result.scalar_one_or_none()
    if not user:
        raise ApiResponse.error(
            status_code=status.HTTP_401_UNAUTHORIZED, message=USER_NOT_FOUND
        )

    return user
