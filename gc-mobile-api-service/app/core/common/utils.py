import random
import string

from core.constants import SCOPE_ATTRIBUTE_MAP


def generate_random_word(length):
    characters = string.ascii_letters + string.digits + "!@#%^&*()-_=+[]{};:,.<>?"
    random_word = "".join(random.choice(characters) for _ in range(length))
    return random_word


def map_profile_with_scopes(user_profile, requested_scopes: list) -> dict:
    if not user_profile:
        return {}

    response_data = {}
    for scope in requested_scopes:
        if scope in SCOPE_ATTRIBUTE_MAP:
            mapper = SCOPE_ATTRIBUTE_MAP[scope]

            if callable(mapper):
                response_data[scope] = mapper(user_profile)
            else:
                response_data[scope] = getattr(user_profile, mapper, None)

    return response_data
