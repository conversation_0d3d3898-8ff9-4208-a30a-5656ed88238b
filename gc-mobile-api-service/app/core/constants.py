X_TENANT_SLUG = "X-Tenant-Slug"
X_TENANT_UUID = "X-Tenant-UUID"

ENDPOINT_ACCOUNT_API = "http://localhost:8002"
FRONTEND_URL = "http://localhost:3000"

TOKEN_PREFIX = "Bearer"

SCOPE_ATTRIBUTE_MAP = {
    "openid": "patient_user_id",
    "profile": lambda p: f"{p.first_name} {p.last_name}",
    "email": "email",
    "address": lambda p: f"{p.address_1} {p.address_2} {p.address_3}",
    "phone": "phone",
}

RESPONSE_TYPE_CODE = "code"
