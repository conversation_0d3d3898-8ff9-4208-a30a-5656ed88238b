from typing import Annotated

from core.common.api_response import ApiResponse
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request
from services.auth_service import AuthPatientService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(path="/fcm-token")
@version(1, 0)
async def set_fcm_token(
    fcm_token: str,
    request: Request,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """
    Set the FCM token for the patient.
    """
    try:
        patient_user = request.user
        service = AuthPatientService(db_session=db_session)
        fcm_token_id = await service.create_or_update_fcm_token(
            fcm_token=fcm_token,
            patient_user_id=patient_user.id,
        )
        return ApiResponse.success(data={"fcm_token_id": fcm_token_id})
    except CustomValueError as e:
        log.error(f"❌ CustomValueError setting FCM token: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Exception setting FCM token: {str(e)}")
        ApiResponse.error()
